export type PhysicalExam = {
  [category: string]: {
    [item: string]: string | string[];
  };
};

export type Document = {
  id: string;
  file_name: string;
  file_path: string;
  file_type: string;
  created_at: string;
  url: string;
};

export type ConsultationV2 = {
  id: string;
  notes: string;
  order: string | null;
  ai_billing_note?: string;
  ai_generated_note_subjective?: string;
  ai_generated_note_objective?: string;
  ai_generated_note_assessment?: string;
  ai_generated_note_plan?: string;
  consultation_date: string;
  physical_exams: PhysicalExam[];
  chief_complaint: string;
  internal_notes?: string | null;
  status: string;
  consultation_request_id?: string;
  recent_consultations?: any[];
  isEstablishedPatient?: boolean;
  nurse_name?: string;
  provider_name?: string;
  patient_details_snapshot: {
    id: string;
    patient_id: string;
    resident_id: string;
    full_name: string;
    firstName: string;
    lastName: string;
    name: string;
    // dob?: string;
    dateOfBirth: string;
    gender: string;
    room_number: string;
    admission_date: string;
    status: string;
    allergies: any;
    diagnoses: {
      code: string;
      description: string;
    }[];
    medications: any;
    providers: {
      providerId: string;
      name: string;
      role: string;
    }[];
    latest_progress_notes: {
      noteId: string;
      date: string;
      author: string;
      content: string;
    }[];
    created_at: string;
    updated_at: string;
    medicalHistory: any;
    physicalExam: any;
    vitals: any;
  };
  physical_exam: PhysicalExam;
  created_at: string;
  updated_at: string;
  provider_id: string;
  nurse_id: string;
  started_at: string | null;
  completed_at?: string | null;
  submitted_at: string | null;
  billing_codes: { code: string; description: string }[];
  icd_codes: { code: string; description: string }[];
  order_approved_at?: string | null;
  external_patient_id?: string | null;
  ehr_name?: string | null;
  video?: {
    status: string;
    duration: number | null;
    start_time: string | null;
    end_time: string | null;
    created_at: string;
    updated_at: string;
    started_at: string | null;
    ended_at: string | null;
  };
  documents?: Document[];
};

export type ConsulataionTypeV2 = {
  consultation: ConsultationV2;
  isEstablishedPatient?: boolean;
  recent_consultations?: any[];
};
