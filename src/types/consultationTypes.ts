export interface ConsultationType {
  id: string;
  notes: string;
  order: string;
  chief_complaint: string;
  status: string;
  patient_details_snapshot: {
    race: string;
    gender: string;
    language: string;
    lastName: string;
    allergies: string;
    coverages: string;
    firstName: string;
    dateOfBirth: string;
    medications: string;
    medicalHistory: string;
  };
  created_at: string;
  updated_at: string;
  provider_id: string;
  nurse_id: string;
  started_at: string | null;
  submitted_at: string | null;
  billing_codes: string[];
  video: {
    status: string;
    duration: number | null;
    start_time: string | null;
    end_time: string | null;
    created_at: string;
    updated_at: string;
    started_at: string | null;
    ended_at: string | null;
  };
}
