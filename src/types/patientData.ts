export type PatientDetailsSnapshot = {
  id?: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  status?: string;
  race?: string;
  language?: string;
  coverages?: string;
  allergies: string;
  diagnoses?: {
    code: string;
    description: string;
  }[];
  medications: string;
  medicalHistory: string;
  created_at?: string;
  updated_at?: string;
};

export type ConsultationInCallResponse = {
  consultation: {
    id: string;
    notes: string | null;
    order: string | null;
    chief_complaint: string;
    internal_notes: string | null;
    status: string;
    patient_details_snapshot: PatientDetailsSnapshot;
    billing_codes: string[];
    created_at: string;
    updated_at: string;
    consultation_requests: {
      reason: string;
      nurse_id: string;
      accepted_provider_id: string;
      status: string;
      accepted_at: string | null;
      created_at: string;
      updated_at: string;
    };
    video: {
      status: string;
      duration: number | null;
      start_time: string | null;
      end_time: string | null;
      created_at: string;
      updated_at: string;
    };
    // patient: {
    //   id: string;
    //   name: string;
    //   external_id: string;
    //   ehr_account_id: string;
    // };
  };
};
