// src/context/SocketContext.tsx
import React, { createContext, useContext, useCallback } from "react";
import { Socket } from "socket.io-client";
import { useAlertsContext } from "./AlertsContext";

export interface ChatMessage {
  id: string;
  consultation_id: string;
  sender_id: string;
  message: string;
  sent_at: string;
  created_at: string;
  updated_at: string;
}

export interface ChatUpdateData {
  chatMessage: ChatMessage;
  consultationId: string;
}

export interface SocketContextType {
  socket: Socket | null;
  onChatUpdate: (callback: (data: ChatUpdateData) => void) => void;
  offChatUpdate: (callback: (data: ChatUpdateData) => void) => void;
}

const ProviderSocketContext = createContext<SocketContextType>({
  socket: null,
  onChatUpdate: () => {},
  offChatUpdate: () => {},
});

export const useSocket = () => useContext(ProviderSocketContext);

export const SocketProvider: React.FC<{
  socket: Socket | null;
  children: React.ReactNode;
}> = ({ socket, children }) => {
  const chatUpdateCallbacks = React.useRef<((data: ChatUpdateData) => void)[]>(
    []
  );
  const { refreshAlerts } = useAlertsContext();

  const onChatUpdate = useCallback(
    (callback: (data: ChatUpdateData) => void) => {
      chatUpdateCallbacks.current.push(callback);
    },
    []
  );

  const offChatUpdate = useCallback(
    (callback: (data: ChatUpdateData) => void) => {
      chatUpdateCallbacks.current = chatUpdateCallbacks.current.filter(
        (cb) => cb !== callback
      );
    },
    []
  );

  React.useEffect(() => {
    if (!socket) return;

    const handleChatUpdate = (data: ChatUpdateData) => {
      chatUpdateCallbacks.current.forEach((callback) => callback(data));
      // Refresh alerts when a new message is received
      refreshAlerts();
    };

    socket.on("consultationChatUpdate", handleChatUpdate);

    return () => {
      socket.off("consultationChatUpdate", handleChatUpdate);
    };
  }, [socket, refreshAlerts]);

  return (
    <ProviderSocketContext.Provider
      value={{ socket, onChatUpdate, offChatUpdate }}
    >
      {children}
    </ProviderSocketContext.Provider>
  );
};
