import React from "react";
import { Text, Button, YStack } from "tamagui";
import * as Linking from "expo-linking";
import { useAuth } from "~/context/AuthContext";

export default function AdminInfoPage() {
  const { signOut } = useAuth();
  const handleGoToWebPortal = () => {
    Linking.openURL("https://www.app.vitalcare.org");
  };

  return (
    <YStack
      flex={1}
      p={20}
      bg={"$background"}
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Text fontSize={24} fontWeight="bold" mb={20}>
        Admin Access Required
      </Text>
      <Text fontSize={16} verticalAlign={"center"} mb={20}>
        To access admin functionality, please sign into the web portal.
      </Text>
      <Button onPress={handleGoToWebPortal} size="$4">
        <Text>Go to Admin Web Portal</Text>
      </Button>
      <Button
        onPress={() => {
          signOut();
        }}
        size="$4"
        style={{ marginTop: 20 }}
      >
        <Text>Logout</Text>
      </Button>
    </YStack>
  );
}
