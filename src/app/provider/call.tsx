import { RouteProp, useRoute } from "@react-navigation/native";
import { useRouter } from "expo-router";
import React from "react";
import ProviderCallWrapper from "../../components/ProviderCallComponent";
import axiosConfig from "../../services/axiosConfig";

type ProviderCallRouteParams = {
  params: {
    consultationId: string;
    sdkId: string;
    rejoin?: string;
  };
};

const ProviderCallPage = ({
  goToDetails,
  callCompleted,
}: {
  goToDetails: () => void;
  callCompleted: boolean;
}) => {
  const router = useRouter();
  const route = useRoute<RouteProp<ProviderCallRouteParams, "params">>();
  const { consultationId, sdkId, rejoin } = route.params;

  const handleRetryRequest = async (): Promise<string> => {
    try {
      const url = `/consultation/rejoin-request/${consultationId}`;
      const response = await axiosConfig.get(url);
      const { providerSDKJWT } = response.data;
      return providerSDKJWT;
    } catch (error) {
      console.error("Failed to get new SDK token:", error);
      throw error;
    }
  };

  return (
    <ProviderCallWrapper
      consultationId={consultationId}
      sdkToken={sdkId}
      onCallEnd={goToDetails}
      onGoToDetails={goToDetails}
      onRetryRequest={handleRetryRequest}
      callCompleted={callCompleted}
    />
  );
};

export default ProviderCallPage;
