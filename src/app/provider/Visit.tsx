import React from "react";
import ImageViewing from "react-native-image-viewing";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import AttachmentSection from "src/components/AttachmentSection";
import CallerNotes from "src/components/calldetailcomponents/CallerNotes";
import { HorizontalDashedLine } from "src/components/DashedLine";
import DropdownTextEditor from "src/components/DropdownTextEditor";
import PhysicalExam from "src/components/PhysicalExam";
import { ScrollView, Text, YStack } from "tamagui";
import { useVisitStyles } from "./Styles/VisitStyles";
interface VisitProps {
  notes: string;
  orderText: string;
  setNotes: (text: string) => void;
  onOrdersChange: (text: string) => void;
  isSubmitted: boolean;
  consultation: any;
  physicalExam: any;
  setPhysicalExam: (value: any) => void;
}

export default function Visit({
  notes,
  setNotes,
  orderText,
  onOrdersChange,
  consultation,
  isSubmitted = false,
  physicalExam,
  setPhysicalExam,
}: VisitProps) {
  const visitStyle = useVisitStyles();

  const [modalVisible, setModalVisible] = React.useState(false);
  const [selectedImage, setSelectedImage] = React.useState<string | null>(null);
  return (
    <KeyboardAwareScrollView>
      <YStack {...visitStyle.mainContainer}>
        <YStack {...visitStyle.childContainer}>
          <Text {...visitStyle.profileText}>Visit</Text>
          <ScrollView
            showsVerticalScrollIndicator={false}
            keyboardDismissMode="interactive"
          >
            <YStack marginBlockEnd={20}>
              <CallerNotes
                value={notes}
                onChangeText={setNotes}
                isEditable={!isSubmitted}
              />
              <YStack>
                <DropdownTextEditor
                  title="Orders"
                  defaultOpen={true}
                  data={orderText || ""}
                  onChangeText={onOrdersChange}
                  isComingFromReviewCall
                  isSubmitted={isSubmitted}
                  orderConfirmed={consultation?.order_confirmed}
                />
              </YStack>
              <HorizontalDashedLine
                height={1}
                dashLength={2}
                dashGap={2}
                color="#D2D6DB"
                style={{ marginTop: 18 }}
              />

              <AttachmentSection
                attachments={(consultation?.documents || []).map(
                  (doc: any) => ({ url: doc.url || doc.uri, id: doc.id })
                )}
              />

              <YStack>
                <PhysicalExam
                  physicalExam={physicalExam}
                  setPhysicalExam={setPhysicalExam}
                  isSubmitted={isSubmitted}
                />
              </YStack>
            </YStack>
          </ScrollView>
        </YStack>
      </YStack>
      {modalVisible && selectedImage && (
        <ImageViewing
          key={selectedImage}
          images={[{ uri: selectedImage }]}
          imageIndex={0}
          visible={modalVisible}
          onRequestClose={() => setModalVisible(false)}
        />
      )}
    </KeyboardAwareScrollView>
  );
}
