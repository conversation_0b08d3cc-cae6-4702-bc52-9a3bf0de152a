import { useEffect, useRef, useState } from "react";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import NotesEditor from "src/components/NotesEditor";
import SubmittedAiNotes from "src/components/SubmittedAiNotes";
import { View, YStack } from "tamagui";
import { useSOAPStyles } from "./Styles/SOAPStyles";

export const extractSection = (text: any, start: any, end: any) => {
  const regex = new RegExp(`${start}([\\s\\S]*?)${end}`, "i");
  const match = text.match(regex);
  return match ? match[1].trim() : "";
};

interface SOAPProps {
  subjective: string;
  setSubjective: (val: string) => void;
  objective: string;
  setObjective: (val: string) => void;
  assessment: string;
  setAssessment: (val: string) => void;
  plan: string;
  setPlan: (val: string) => void;
  isSubmitted: boolean;
  aiBillingNote?: string;
}

export default function SOAP({
  subjective,
  setSubjective,
  objective,
  setObjective,
  assessment,
  setAssessment,
  plan,
  setPlan,
  isSubmitted,
  aiBillingNote,
}: SOAPProps) {
  const styles = useSOAPStyles();
  const [noteSignedBy, setNoteSignedBy] = useState("");
  const [dateSignedBy, setDateSignedBy] = useState("");
  const [datePatientConsented, setDatePatientConsented] = useState("");
  useEffect(() => {
    if (aiBillingNote) {
      setNoteSignedBy(
        extractSection(
          aiBillingNote,
          "Note signed by:",
          "Date signed by provider:"
        )
      );

      setDateSignedBy(
        extractSection(
          aiBillingNote,
          "Date signed by provider:",
          "Date patient consented:"
        )
      );

      setDatePatientConsented(
        aiBillingNote.split("Date patient consented:")[1]?.trim() || ""
      );
    }
  }, [aiBillingNote]);
  const refs = {
    subjective: useRef(null),
    objective: useRef(null),
    assessment: useRef(null),
    plan: useRef(null),
  };

  const sections = [
    {
      ref: refs.subjective,
      title: "Subjective",
      subtitle: "Patient reported symptoms and health history",
      value: subjective,
      onChange: setSubjective,
    },
    {
      ref: refs.objective,
      title: "Objective",
      subtitle: "Clinician observed signs, labs, and vitals",
      value: objective,
      onChange: setObjective,
    },
    {
      ref: refs.assessment,
      title: "Assessment",
      subtitle: "Diagnoses and interpretation of findings",
      value: assessment,
      onChange: setAssessment,
    },
    {
      ref: refs.plan,
      title: "Plan",
      subtitle: "Next steps for treatment and management",
      value: plan,
      onChange: setPlan,
    },
  ];

  const noNotesPresent =
    !subjective?.trim() &&
    !objective?.trim() &&
    !assessment?.trim() &&
    !plan?.trim() &&
    !noteSignedBy?.trim() &&
    !dateSignedBy?.trim() &&
    !datePatientConsented?.trim();

  return (
    <KeyboardAwareScrollView style={{ flex: 1 }} extraScrollHeight={-100}>
      <YStack {...styles.mainContainer}>
        {!isSubmitted ? (
          sections.map(({ ref, title, subtitle, value, onChange }) => (
            <NotesEditor
              key={title}
              ref={ref}
              title={title}
              subtitle={subtitle}
              onChangeText={onChange}
              data={value}
            />
          ))
        ) : (
          <View marginBlock={20}>
            <SubmittedAiNotes
              subjective={subjective}
              objective={objective}
              assessment={assessment}
              plan={plan}
              noteSignedBy={noteSignedBy}
              dateSignedByProvider={dateSignedBy}
              datePatientConsented={datePatientConsented}
            />
          </View>
        )}
      </YStack>
    </KeyboardAwareScrollView>
  );
}
