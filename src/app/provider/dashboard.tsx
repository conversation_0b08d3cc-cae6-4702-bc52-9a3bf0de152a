import Header from "@/common/header";
import React, { useCallback, useEffect, useState } from "react";
import SheetDemo from "src/components/SettingsDrawer";
import { Label, Switch, Text, View, XStack, YStack } from "tamagui";
import { useDashboardStyles } from "./Styles/DashboardStyle";
import ConsultationTabs from "./ConsultationTabs";
import { useAuth } from "~/context/AuthContext";
import axiosConfig from "~/services/axiosConfig";
// import { VoipCallTester } from "src/components/VoipCallTester";

export default function Dashboard() {
  const dashboardStyles = useDashboardStyles();
  const {
    container,
    mainStack,
    consultationTitle,
    toggleContainer,
    todayLabel,
    toggleText,
    toggleStack,
    switchStyle,
    switchThumbStyle,
  } = dashboardStyles;

  const [open, setOpen] = useState(false);
  const [isActive, setIsActive] = useState(false);
  const { user } = useAuth();

  const handlePress = useCallback(() => {
    setOpen(true);
  }, []);

  const { role } = user || {};

  useEffect(() => {
    const fetchProviderStatus = async () => {
      if (role === "provider") {
        try {
          const response = await axiosConfig.get("/provider/online", {
            params: { providerId: user?.id },
          });
          if (!response.data || response.data.error) {
            console.error(
              "Error fetching provider status",
              response.data?.error
            );
            return;
          }
          setIsActive(!!response.data.isOnline);
        } catch (error) {
          console.error("Error fetching provider status", error);
        }
      }
    };
    fetchProviderStatus();
  }, [role]);

  const handleStatusChange = async (newStatus: boolean) => {
    setIsActive(newStatus);
    try {
      await axiosConfig.put("/provider/online", {
        providerId: user?.id,
        status: newStatus,
      });
    } catch (error) {
      console.error("Error updating provider status", error);
    }
  };

  return (
    <View {...container}>
      <YStack {...mainStack}>
        <Header onAvatarPress={handlePress} />

        <Text {...consultationTitle}>Consultations</Text>
        <XStack {...toggleContainer}>
          <Text {...todayLabel}>TODAY</Text>

          <XStack {...toggleStack}>
            <Switch
              {...switchStyle}
              checked={isActive}
              onCheckedChange={handleStatusChange}
              backgroundColor={
                isActive ? "$switchToggleGreen" : "$inactiveToggleColor"
              }
            >
              <Switch.Thumb {...switchThumbStyle} />
            </Switch>
            <Label {...toggleText}>{isActive ? "Online" : "Offline"}</Label>
          </XStack>
        </XStack>

        <YStack flex={1}>
          <ConsultationTabs />
        </YStack>

        {open && <SheetDemo open={open} setOpen={setOpen} />}
      </YStack>
    </View>
  );
}
