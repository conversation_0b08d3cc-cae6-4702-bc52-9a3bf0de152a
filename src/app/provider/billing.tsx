import { <PERSON><PERSON><PERSON><PERSON>, FilePlus, Search, X } from "@tamagui/lucide-icons";
import {
  <PERSON><PERSON>,
  Card,
  Text,
  YStack,
  <PERSON>Stack,
  ScrollView,
  Input,
  Spinner,
  View,
} from "tamagui";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Dimensions, KeyboardAvoidingView, Platform } from "react-native";
import { useEffect, useState } from "react";
import { useBillingStyles } from "./Styles/BillingStyles";
import ICDCodes from "./ICDCodes";
import BillingCodes from "./BillingCodes";
import { HorizontalDashedLine } from "src/components/DashedLine";

export function useDebounce<T>(value: T, delay: number): T {
  const [debounced, setDebounced] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebounced(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debounced;
}

type BillingProps = {
  selectedCodes: { code: string; description: string }[];
  setSelectedCodes: React.Dispatch<
    React.SetStateAction<{ code: string; description: string }[]>
  >;
  selectedBillingCodes: { code: string; description: string }[];
  setSelectedBillingCodes: React.Dispatch<
    React.SetStateAction<{ code: string; description: string }[]>
  >;
  isSubmitted: boolean;
  isEstablishedPatient: boolean;
  onUnavailableCode?: (code: { code: string; description: string }) => void;
};

export default function Billing({
  selectedCodes,
  setSelectedCodes,
  selectedBillingCodes,
  setSelectedBillingCodes,
  isSubmitted = false,
  isEstablishedPatient,
  onUnavailableCode,
}: BillingProps) {
  const insets = useSafeAreaInsets();
  const screenHeight = Dimensions.get("window").height;
  const screenWidth = Dimensions.get("window").width;
  const availableHeight = screenHeight - (insets.top + insets.bottom);
  const billingStyles = useBillingStyles(availableHeight, screenWidth);
  const [addingSection, setAddingSection] = useState<"ICD" | "BILLING" | "">(
    ""
  );

  const [icdCodes, setIcdCodes] = useState<string[]>([]);
  const [billingCodes, setBillingCodes] = useState<string[]>([]);

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
      keyboardVerticalOffset={insets.top + 64}
    >
      <ScrollView
        contentContainerStyle={{ grow: 1, pb: insets.bottom }}
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="interactive"
      >
        <YStack {...billingStyles.mainContainer}>
          <BillingCodes
            selectedCodes={selectedBillingCodes}
            setSelectedCodes={setSelectedBillingCodes}
            isSubmitted={isSubmitted}
            isAdding={addingSection}
            setIsAdding={setAddingSection}
            isEstablishedPatient={isEstablishedPatient}
            onUnavailableCode={onUnavailableCode}
          />
          <HorizontalDashedLine
            height={1}
            dashLength={2}
            dashGap={2}
            color="#D2D6DB"
            style={{ marginTop: 16, marginBottom: 16 }}
          />
          <ICDCodes
            selectedCodes={selectedCodes}
            setSelectedCodes={setSelectedCodes}
            isSubmitted={isSubmitted}
            isAdding={addingSection}
            setIsAdding={setAddingSection}
          />
        </YStack>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}
