import { useTheme } from "@/_layout";
import { ChevronDown, Plus, Search, X } from "@tamagui/lucide-icons";
import { useEffect, useState } from "react";
import { HorizontalDashedLine } from "src/components/DashedLine";
import { useBillingCodes } from "src/hooks/useBillingCodes";
import {
  Accordion,
  Button,
  Input,
  ScrollView,
  Spinner,
  Square,
  Text,
  View,
  XStack,
  YStack,
} from "tamagui";
import { ProblemListDropDownStyles } from "./Styles/ProblemListDropdownStyles";
interface DropdownTextEditorProps {
  title: string;
  selectedCodes: { code: string; description: string }[];
  setSelectedCodes: React.Dispatch<
    React.SetStateAction<{ code: string; description: string }[]>
  >;
}

export function useDebounce<T>(value: T, delay: number): T {
  const [debounced, setDebounced] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebounced(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debounced;
}

export default function ProblemListDropDown({
  title,
  selectedCodes,
  setSelectedCodes,
}: DropdownTextEditorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const styles = ProblemListDropDownStyles();
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const debouncedQuery = useDebounce(searchQuery, 500);
  const {
    data: billingCodes,
    isLoading,
    isError,
    error,
  } = useBillingCodes(debouncedQuery);
  const [availableCodes, setAvailableCodes] = useState<any[]>([]);
  useEffect(() => {
    if (billingCodes && billingCodes.length > 0) {
      const filtered = billingCodes.filter(
        (item) => !selectedCodes.some((c) => c.code === item.code)
      );

      setAvailableCodes(filtered);
    }
  }, [billingCodes, selectedCodes]);

  const filteredCodes = searchQuery
    ? availableCodes.filter(
        (item) =>
          item.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : [];

  const handleAddCode = (codeObj: { code: string; description: string }) => {
    if (!selectedCodes.some((c) => c.code === codeObj.code)) {
      setSelectedCodes([...selectedCodes, codeObj]);
      setAvailableCodes((avail) =>
        avail.filter((item) => item.code !== codeObj.code)
      );
    }
  };

  const handleRemoveCode = (codeObj: { code: string; description: string }) => {
    setSelectedCodes((sel) => sel.filter((c) => c.code !== codeObj.code));
    // put it back into the pool
    const removed = billingCodes?.find((b) => b.code === codeObj.code);
    if (removed) {
      setAvailableCodes((avail) => [...avail, removed]);
    }
  };

  return (
    <View {...styles.container}>
      <Accordion
        overflow="hidden"
        borderWidth={1}
        type="multiple"
        {...styles.accordion}
        borderColor={isDarkMode ? "#697586" : "#D0D5DD"}
      >
        <Accordion.Item value="a1">
          <Accordion.Trigger
            {...styles.trigger}
            onPress={() => setIsOpen(!isOpen)}
          >
            {({ open }: { open: boolean }) => (
              <XStack {...styles.headerRow}>
                <YStack>
                  <Text {...styles.headerText}>{title}</Text>
                </YStack>
                <Square animation="quick" rotate={open ? "180deg" : "0deg"}>
                  <ChevronDown size="$1" />
                </Square>
              </XStack>
            )}
          </Accordion.Trigger>

          <Accordion.HeightAnimator animation="medium">
            <Accordion.Content {...styles.content}>
              <View>
                {!open && (
                  <YStack>
                    {selectedCodes.length === 0 ? (
                      <View>
                        <YStack {...styles.innerContainer}>
                          <Text {...styles.noICDCodesText}> No ICD Codes</Text>
                        </YStack>
                      </View>
                    ) : (
                      <ScrollView
                        style={styles.codesScroll}
                        showsVerticalScrollIndicator={true}
                      >
                        <YStack {...styles.codesContainer}>
                          {selectedCodes.map((c, idx) => (
                            <>
                              <XStack key={idx} {...styles.selectedCodeItem}>
                                <Text {...styles.selectedCodeTextCode}>
                                  {c.code}
                                </Text>
                                <Text {...styles.selectedCodeDescription}>
                                  {c.description}
                                </Text>
                                <Button
                                  {...styles.removeCodeBtn}
                                  onPress={() => handleRemoveCode(c)}
                                  disabled={false}
                                  icon={<X size={"$1"} />}
                                />
                              </XStack>

                              <HorizontalDashedLine
                                height={1}
                                dashLength={2}
                                dashGap={2}
                                color="#D2D6DB"
                              />
                            </>
                          ))}
                        </YStack>
                      </ScrollView>
                    )}
                  </YStack>
                )}
                <Button
                  icon={!open ? <Plus size="$1" /> : <X size={"$1"} />}
                  {...styles.addBtn}
                  onPress={() => {
                    setSearchQuery("");
                    setOpen(!open);
                  }}
                  marginBlockStart={open ? -20 : 30}
                >
                  {!open ? "Add" : "Close"}
                </Button>

                {selectedCodes.length > 0 && open && (
                  <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    style={{ ...styles.selectedCodesScroll, gap: 5 }}
                    paddingInline={10}
                  >
                    {selectedCodes.map((code, index) => (
                      <XStack
                        key={index}
                        {...styles.selectedCodeItem}
                        style={{
                          width: "auto",
                          borderWidth: 1,
                          borderColor: "#D2D6DB",
                          borderRadius: 5,
                          padding: 3,
                          marginRight: 5,
                        }}
                      >
                        <Text
                          {...styles.selectedCodeTextCode}
                          style={{
                            borderWidth: 0,
                            borderColor: "tansparent !important",
                          }}
                        >
                          {code.code}
                        </Text>
                        <Button
                          {...styles.removeCodeBtn}
                          onPress={() => handleRemoveCode(code)}
                          disabled={false}
                          icon={<X size={"$1"} />}
                        />
                      </XStack>
                    ))}
                  </ScrollView>
                )}
                {open && (
                  <YStack {...styles.frequentlyUsedContainer}>
                    <XStack width="100%" justify="space-between">
                      <XStack flex={1} {...styles.searchContainer}>
                        <Search size="$1" color="$textcolor" />
                        <Input
                          {...styles.searchInput}
                          value={searchQuery}
                          onChangeText={setSearchQuery}
                        />
                      </XStack>

                      <Button
                        {...styles.closeBtn}
                        onPress={() => {
                          setOpen(false);
                          setSearchQuery("");
                        }}
                      >
                        Close
                      </Button>
                    </XStack>

                    <ScrollView
                      showsVerticalScrollIndicator={false}
                      style={{ maxHeight: 400 }}
                    >
                      {isLoading ? (
                        <YStack {...styles.loadingContainer} height={400}>
                          <Spinner size="large" color="$gray10" />
                        </YStack>
                      ) : filteredCodes.length === 0 ? (
                        <Text {...styles.noResultsText}></Text>
                      ) : (
                        filteredCodes.map((item, index) => (
                          <>
                            <XStack
                              key={index}
                              {...styles.frequentlyUsedItem}
                              onPress={() => handleAddCode(item)}
                            >
                              <YStack {...styles.codeContainer}>
                                <Text {...styles.frequentlyUsedCode}>
                                  {item.code}
                                </Text>
                              </YStack>
                              <Text {...styles.frequentlyUsedDescription}>
                                {item.description}
                              </Text>
                            </XStack>
                            <HorizontalDashedLine
                              height={1}
                              dashLength={2}
                              dashGap={2}
                              color="#D2D6DB"
                            />
                          </>
                        ))
                      )}
                    </ScrollView>
                  </YStack>
                )}
              </View>
            </Accordion.Content>
          </Accordion.HeightAnimator>
        </Accordion.Item>
      </Accordion>
    </View>
  );
}
