export const useDashboardStyles = () => {
  return {
    container: { flex: 1, backgroundColor: "$screenBackgroundcolor" },
    mainStack: {
      marginInline: 20,
      marginBlock: 20,
      flex: 1,
      height: "100%" as "100%",
    },
    consultationTitle: {
      fontSize: 20,
      fontWeight: "600" as "600",
      marginBlockStart: 10,
      color: "$textcolor" as any,
    },
    toggleContainer: {
      marginBlockStart: 1,
      justifyContent: "space-between",
      alignItems: "center",
    },
    todayLabel: {
      fontSize: 14,
      fontWeight: "600" as "600",
      color: "$textcolor" as any,
    },
    scrollContainer: { flexGrow: 1, paddingBottom: 20 },
    toggleText: { fontSize: 16, fontWeight: "500" as "500" },
    toggleStack: { alignItems: "center" as any, gap: "$2" as any },
    switchStyle: {
      size: "$3" as any,
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 0.5,
    },
    switchThumbStyle: {
      animation: "bouncy" as any,
      backgroundColor: "white" as any,
      size: "$3" as any,
      marginTop: "auto" as any,
      marginBottom: "auto" as any,
    },
  };
};
