export const ProblemListDropDownStyles = () => {
  return {
    container: {
      marginBlockStart: 20,
    },
    accordion: {
      borderRadius: "$6" as "$6",
    },
    trigger: {
      flexDirection: "row" as "row",
      borderWidth: 0,
      backgroundColor: "$screenBackgroundcolor",
    },
    headerRow: {
      justifyContent: "space-between",
      flex: 1,
    },
    content: {
      animation: "medium" as "medium",
      exitStyle: { opacity: 0 },
      backgroundColor: "$screenBackgroundcolor",
    },
    innerContainer: {
      justifyContent: "center" as any,
      alignItems: "center",
    },
    headerText: {
      fontSize: 16,
      fontWeight: 600 as any,
    },
    noICDCodesText: {
      color: "$textcolor" as any,
      fontSize: 16,
    },
    addBtn: {
      fontSize: 16 as 16,
      size: "$3" as "$3",
      fontWeight: "500" as "500",
      backgroundColor: "$confirmOrderBlue",
      borderColor: "$confirmOrderBorderCOlor" as any,
      color: "$confirmOrderTextColor" as any,
      borderWidth: 1,
      marginBlockStart: 30,
    },

    frequentlyUsedContainer: {
      marginBlockStart: 10,
      backgroundColor: "$screenBackgroundcolor" as any,
      borderWidth: 1,
      borderColor: "$primaryBorderColor" as any,
      borderRadius: 10,
      padding: 10,
      maxHeight: 400 as any,
    },

    searchContainer: {
      alignItems: "center" as any,
      borderRadius: "$4" as any,
      paddingHorizontal: "$3" as any,
      borderWidth: 1,
      height: 38,
      bg: "transparent" as any,
      borderColor: "$primaryBorderColor" as any,
      marginBlockEnd: 5,
    },

    searchInput: {
      flex: 1,
      placeholder: "Search billing codes",
      borderWidth: 0,
      bg: "transparent" as any,
      placeholderTextColor: "$textcolor" as any,
      fontSize: 14,
      fontWeight: "600" as any,
    },
    closeBtn: {
      backgroundColor: "$primaryColor",
      color: "$buttonWhiteColor" as any,
      fontSize: 16,
      size: "$3" as any,
      fontWeight: "600" as any,
      borderWidth: 2,
      marginInline: 5,
    },
    loadingContainer: {
      flex: 1,
      justifyContent: "center" as any,
      alignItems: "center",
    },

    noResultsText: {
      fontSize: 14,
      fontWeight: "400" as any,
      color: "$textcolor" as any,
      textAlign: "center" as any,
      marginTop: 8,
    },
    frequentlyUsedTitle: {
      fontSize: 14,
      fontWeight: "600" as any,
      color: "$textcolor" as any,
      marginBottom: 10,
    },

    frequentlyUsedItem: {
      paddingVertical: 10,
      cursor: "pointer" as any,
      justifyContent: "space-between" as any,
      alignItems: "center" as any,
      flexDirection: "row" as any,
    },
    codeContainer: {
      borderWidth: 1,
      borderColor: "$primaryBorderColor" as any,
      borderRadius: 5,
      paddingHorizontal: 8,
      paddingVertical: 4,
    },
    frequentlyUsedCode: {
      fontSize: 16,
      fontWeight: "500" as any,
      color: "$textcolor" as any,
    },
    frequentlyUsedDescription: {
      fontSize: 14,
      fontWeight: "400" as any,
      color: "$textcolor" as any,
      opacity: 0.7,
      textAlign: "right",
      flexShrink: 1,
      maxWidth: "70%" as any,
    },

    selectedCodesScroll: {
      marginBlockStart: 10,
      maxHeight: 50,
      borderWidth: 1,
      borderRadius: 7,
      borderColor: "#D0D5DD",
    },
    codesScroll: {
      marginBlockStart: 10,
      maxHeight: 190,
      borderWidth: 1,
      borderRadius: 7,
      borderColor: "#D0D5DD",
    },
    selectedCodesContainer: {
      flexDirection: "row" as any,
      flexWrap: "wrap" as any,

      marginInline: 10,
      alignItems: "center" as any,
    },
    codesContainer: {
      flexDirection: "column" as any,
      marginInline: 10,
      alignItems: "center" as any,
    },
    selectedCodeText: {
      fontSize: 16,
      fontWeight: "500" as any,
      color: "$textcolor" as any,
      maxWidth: "70%" as any,
    },
    selectedCodeRight: {
      flexDirection: "row" as any,
      alignItems: "center" as any,
      justifyContent: "flex-end" as any,
      maxWidth: "70%" as any,
    },
    selectedCodeItem: {
      flexDirection: "row" as any,
      alignItems: "center" as any,
      padding: 8,
      marginVertical: 5,
      backgroundColor: "$screenBackgroundcolor" as any,
      gap: 5,
    },
    selectedCodeTextCode: {
      fontSize: 16,
      fontWeight: "500" as any,
      color: "$textcolor" as any,
      paddingHorizontal: 4,
      paddingVertical: 2,
    },
    selectedCodeDescription: {
      flex: 1,
      textAlign: "center" as any,
      fontSize: 14,
      fontWeight: "400" as any,
      color: "$textcolor" as any,
    },
    removeCodeBtn: {
      size: "$2" as any,
      backgroundColor: "transparent" as any,
      borderWidth: 0,
    },
  };
};
