export const useBillingStyles = (
  availableHeight: number,
  screenWidth: number
) => {
  const isSmallScreen = availableHeight < 700;
  const isLargeScreen = availableHeight > 800;

  return {
    mainContainer: {
      // flex: 1,
    },
    BillingText: {
      fontSize: 20,
      fontWeight: "600" as any,
    },
    assignBillingCodesBtn: {
      fontSize: 16,
      size: "$3" as any,
      fontWeight: "600" as any,
      backgroundColor: "$confirmOrderBlue" as any,
      borderColor: "$confirmOrderBorderCOlor" as any,
      color: "$confirmOrderTextColor" as any,
      borderWidth: 1,
      marginBlockStart: 8,
      width: "100%" as any,
      alignSelf: "center",
      paddingHorizontal: 20,
    },
    selectedCodesScroll: {
      marginBlockStart: 10,
      maxHeight: 50,
      borderWidth: 1,
      borderRadius: 7,
      borderColor: "#D0D5DD",
      // flexWrap: "wrap" as any,
    },
    codesScroll: {
      marginBlockStart: 10,
      maxHeight: 190, // ← constrained height
      borderWidth: 1,
      borderRadius: 7,
      borderColor: "#D0D5DD",
    },
    selectedCodesContainer: {
      flexDirection: "row" as any,
      flexWrap: "wrap" as any,
      maxWidth: screenWidth - 20,
      marginInline: 10,
      alignItems: "center" as any,
    },
    codesContainer: {
      flexDirection: "column" as any,
      // remove any height here so children can overflow into ScrollView
      maxWidth: screenWidth - 20,
      marginInline: 10,
      alignItems: "center" as any,
    },
    selectedCodeItem: {
      flexDirection: "row" as any,
      justifyContent: "space-between" as any,
      alignItems: "center" as any,
      width: screenWidth - 40, // leave some margin
      padding: 8,
      marginVertical: 5,
      // borderWidth: 1,
      // borderColor: "$primaryBorderColor" as any,
      // borderRadius: 5,
      backgroundColor: "$screenBackgroundcolor" as any,
    },
    selectedCodeText: {
      fontSize: 16,
      fontWeight: "500" as any,
      color: "$textcolor" as any,
      maxWidth: "70%" as any,
    },
    selectedCodeTextCode: {
      fontSize: 16,
      fontWeight: "500" as any,
      color: "$textcolor" as any,
      // borderWidth: 1,
      // borderColor: "$primaryBorderColor" as any,
      // borderRadius: 5,
      paddingHorizontal: 4,
      paddingVertical: 2,
    },
    selectedCodeRight: {
      flexDirection: "row" as any,
      alignItems: "center" as any,
      justifyContent: "flex-end" as any,
      maxWidth: "70%" as any,
      // flexShrink: 1, // allow it to shrink
    },

    // description text wraps
    selectedCodeDescription: {
      fontSize: 14,
      fontWeight: "400" as any,
      color: "$textcolor" as any,
      // flexShrink: 1, // shrink to fit
      // flexWrap: "wrap" as any,
      // marginRight: 8,
      // maxWidth: "70%" as any,
    },
    removeCodeBtn: {
      size: "$2" as any,
      backgroundColor: "transparent" as any,
      borderWidth: 0,
    },
    frequentlyUsedContainer: {
      marginBlockStart: 10,
      backgroundColor: "$screenBackgroundcolor" as any,
      borderWidth: 1,
      borderColor: "$primaryBorderColor" as any,
      borderRadius: 10,
      padding: 10,
      maxHeight: 400 as any,
    },
    frequentlyUsedTitle: {
      fontSize: 14,
      fontWeight: "600" as any,
      color: "$textcolor" as any,
      marginBottom: 10,
    },

    frequentlyUsedItem: {
      paddingVertical: 10,
      cursor: "pointer" as any,
      justifyContent: "space-between" as any,
      alignItems: "center" as any,
      flexDirection: "row" as any,
    },
    codeContainer: {
      borderWidth: 1,
      borderColor: "$primaryBorderColor" as any,
      borderRadius: 5,
      paddingHorizontal: 8,
      paddingVertical: 4,
    },
    frequentlyUsedCode: {
      fontSize: 16,
      fontWeight: "500" as any,
      color: "$textcolor" as any,
    },
    frequentlyUsedDescription: {
      fontSize: 14,
      fontWeight: "400" as any,
      color: "$textcolor" as any,
      opacity: 0.7,
      textAlign: "right",
      flexShrink: 1,
      maxWidth: "70%" as any,
    },
    noResultsText: {
      fontSize: 14,
      fontWeight: "400" as any,
      color: "$textcolor" as any,
      textAlign: "center" as any,
      marginTop: 8,
    },
    tabBody: {
      flex: 1,
      marginBlockStart: 20,
      width: "100%",
      backgroundColor: "$screenBackgroundcolor",
    },
    tabCard: {
      backgroundColor: "$screenBackgroundcolor",
      flex: 1,
    },
    iconSize: isSmallScreen ? "$1" : "$2",
    frequentlyUsedTextContainer: {
      justifyContent: "space-between",
      alignItems: "center",
    },

    noBiilingCodestabBody: {
      marginBlockStart: 8,
      backgroundColor: "$screenBackgroundcolor",
    },
    noBillingodesCard: {
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 1,
      borderRadius: 18,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "$screenBackgroundcolor",
    },
    dottedLineContainer: {
      width: "100%" as "100%",
      alignItems: "flex-start",
      marginBlockStart: 15,
      marginBlockEnd: 7,
    },
    dottedLine: {
      flexDirection: "row" as "row",
      width: "100%" as "100%",
      height: 2,
      backgroundColor: "transparent" as "transparent",
    },
    dot: {
      width: 5,
      height: 2,
      backgroundColor: "$primaryBorderColor",
      marginRight: 5,
    },
    dashedSeparator: {
      width: "100%" as any,
      borderBottomWidth: 1,
      borderStyle: "dashed" as any,
      borderColor: "$primaryBorderColor" as any,
    },
    noBillingCodesAddedText: {
      fontSize: 14,
      fontWeight: 500 as any,
      color: "$textcolor" as any,
      marginBlockStart: 20,
    },
    noBillingCodetabCard: {
      backgroundColor: "$screenBackgroundcolor" as any,
    },
    searchContainer: {
      alignItems: "center" as any,
      borderRadius: "$4" as any,
      paddingHorizontal: "$3" as any,
      borderWidth: 1,
      height: 38,
      bg: "transparent" as any,
      borderColor: "$primaryBorderColor" as any,
      marginBlockEnd: 5,
    },
    searchInput: {
      flex: 1,
      placeholder: "Search billing codes",
      borderWidth: 0,
      bg: "transparent" as any,
      placeholderTextColor: "$textcolor" as any,
      fontSize: 14,
      fontWeight: "600" as any,
    },
    closeBtn: {
      backgroundColor: "$primaryColor",
      color: "$buttonWhiteColor" as any,
      fontSize: 16,
      size: "$3" as any,
      fontWeight: "600" as any,
      borderWidth: 2,
      marginInline: 5,
    },

    loadingContainer: {
      flex: 1,
      justifyContent: "center" as any,
      alignItems: "center",
    },
  };
};
