import { Route<PERSON>rop, useRoute } from "@react-navigation/native";
import {
  <PERSON><PERSON><PERSON>,
  Mic,
  MicOff,
  PhoneCall,
  Video,
  VideoOff,
} from "@tamagui/lucide-icons";
import {
  EventType,
  useZoom,
  VideoAspect,
  VideoResolution,
  ZoomVideoSdkUser,
  ZoomView,
} from "@zoom/react-native-videosdk";
import React, { useEffect, useRef, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Platform,
  StyleSheet,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from "react-native";
import { Notification } from "src/components/Notification";
import { Text } from "tamagui";
import { useAuth } from "~/context/AuthContext";
import axiosConfig from "~/services/axiosConfig";
import CallKitService from "~/services/CallKitService";

const FILE_NAME = "ProviderCallView";
const logEvent = (
  level: string,
  status: string,
  message: string,
  metadata: object = {}
) => {
  axiosConfig.post("/log-event", {
    level,
    status,
    name: FILE_NAME,
    message,
    metadata,
  });
};

type ProviderCallRouteParams = {
  consultationId: string;
  sdkId: string;
};

const ProviderCallNative = ({
  goToDetails,
  callCompleted,
}: {
  goToDetails: () => void;
  callCompleted: boolean;
}) => {
  const { width, height } = useWindowDimensions();
  const isLandscape = width > height;
  const {
    joinSession,
    leaveSession,
    addListener,
    session,
    audioHelper,
    videoHelper,
  } = useZoom();
  const { user } = useAuth();
  const route =
    useRoute<RouteProp<{ params: ProviderCallRouteParams }, "params">>();
  const { consultationId, sdkId } = route.params;

  const [localSDKId, setLocalSDKId] = useState<string>(sdkId || "123");
  const [retryCount, setRetryCount] = useState<number>(0);

  const [loading, setLoading] = useState<boolean>(true);
  const [callStarted, setCallStarted] = useState<boolean>(false);
  const [callEnded, setCallEnded] = useState<boolean>(false);
  const [videoOn, setVideoOn] = useState<boolean>(true);
  const [audioOn, setAudioOn] = useState<boolean>(true);
  const [localUser, setLocalUser] = useState<ZoomVideoSdkUser | null>(null);
  const [remoteUsers, setRemoteUsers] = useState<ZoomVideoSdkUser | null>(null);
  const [isRecovering, setIsRecovering] = useState(false);
  const [disableReload, setDisableReload] = useState(false);
  const [viewKey, setViewKey] = useState(0);
  const [remoteUserAudio, setRemoteUserAudio] = useState<boolean>(false);
  const [localUserName, setLocalUserName] = useState<string>("");
  const [remoteUserName, setRemoteUserName] = useState<string>("");
  const listeners = useRef<any[]>([]);
  const [notification, setNotification] = useState<{
    message: string;
    visible: boolean;
  }>({
    message: "",
    visible: false,
  });
  const heartbeatIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const netListener = addListener(
      EventType.onUserVideoNetworkStatusChanged,
      async ({ user: netUser, result }) => {
        const me = await session.getMySelf();
        if (netUser.userId === me.userId && result.uplinkNetworkQuality < 2) {
          logEvent(
            "WARN",
            "NETWORK_DROP",
            "Network quality low, restarting video"
          );
          setIsRecovering(true);
          await videoHelper.stopVideo();
          await videoHelper.startVideo();
          setIsRecovering(false);
          logEvent(
            "WARN",
            "NETWORK_RECOVER",
            "Video restarted after network drop"
          );
        }
      }
    );
    const canvasFail = addListener(
      EventType.onVideoCanvasSubscribeFail,
      ({ canvasId }) => {
        logEvent(
          "WARN",
          "CANVAS_SUBSCRIBE_FAIL",
          `Canvas ${canvasId} failed, remounting view`
        );
        setViewKey((k) => k + 1);
      }
    );
    return () => {
      netListener.remove();
      canvasFail.remove();
    };
  }, []);

  const toggleAudio = async () => {
    try {
      const mySelf = await session.getMySelf();
      const muted = await mySelf.audioStatus.isMuted();
      if (muted) {
        await audioHelper.unmuteAudio(mySelf.userId);
      } else {
        await audioHelper.muteAudio(mySelf.userId);
      }
    } catch (err) {}
  };

  const toggleVideo = async () => {
    try {
      const mySelf = await session.getMySelf();
      const currentVideoOn = await mySelf.videoStatus.isOn();
      if (currentVideoOn) {
        await videoHelper.stopVideo();
      } else {
        await videoHelper.startVideo();
      }
    } catch (err) {}
  };

  const joinSessionHandler = async () => {
    if (!consultationId) {
      logEvent("ERROR", "MISSING_PARAMS", "Consultation ID is missing");
      Alert.alert("Error", "Consultation ID is missing.");
      setLoading(false);
      return;
    }
    setLoading(true);

    if (Platform.OS === "ios") {
      try {
        const result = await CallKitService.endCurrentCallKitCall();
      } catch (err) {}
    }

    try {
      const name = `${user?.firstName} ${user?.lastName.split("")[0]}`;
      await joinSession({
        sessionName: consultationId,
        userName: name,
        token: localSDKId,
        sessionIdleTimeoutMins: 10,
        audioOptions: {
          connect: true,
          mute: false,
          autoAdjustSpeakerVolume: false,
        },
        videoOptions: { localVideoOn: true },
      });
      setCallStarted(true);
      logEvent("INFO", "JOIN_SESSION_SUCCESS", "Joined session successfully");

      const sessionJoinListener = addListener(
        EventType.onSessionJoin,
        async () => {
          const mySelf = new ZoomVideoSdkUser(await session.getMySelf());
          const remoteUsers = await session.getRemoteUsers();
          const mySelfName = await mySelf.getUserName();
          const remoteUserName = await remoteUsers?.[0]?.getUserName();
          setLocalUserName(mySelfName);
          setRemoteUserName(remoteUserName);
          setRemoteUsers(remoteUsers?.[0]);
          setLocalUser(mySelf);
        }
      );
      listeners.current.push(sessionJoinListener);

      const userJoinListener = addListener(
        EventType.onUserJoin,
        async (event) => {
          const mySelf = new ZoomVideoSdkUser(await session.getMySelf());
          const remoteUsers = await session.getRemoteUsers();
          const mySelfName = await mySelf.getUserName();
          const remoteUserName = await remoteUsers?.[0]?.getUserName();
          setLocalUserName(mySelfName);
          setRemoteUserName(remoteUserName);
          setRemoteUsers(remoteUsers?.[0]);
          setLocalUser(mySelf);
          const joinedUser = event?.joinedUsers?.[0];
          if (joinedUser?.userId !== mySelf?.userId) {
            setNotification({
              message: `${joinedUser?.userName || "Nurse"} joined the call`,
              visible: true,
            });
          }
        }
      );
      listeners.current.push(userJoinListener);

      const userLeaveListener = addListener(
        EventType.onUserLeave,
        async (event) => {
          const mySelf = new ZoomVideoSdkUser(await session.getMySelf());
          const remoteUsers = await session.getRemoteUsers();
          const mySelfName = await mySelf.getUserName();
          const remoteUserName = await remoteUsers?.[0]?.getUserName();
          setLocalUserName(mySelfName);
          setRemoteUserName(remoteUserName);
          setRemoteUsers(remoteUsers?.[0]);
          setLocalUser(mySelf);
          const leftuser = event?.leftUsers?.[0];
          if (leftuser?.userId !== mySelf.userId) {
            setNotification({
              message: `${leftuser?.userName || "Nurse"} left the call`,
              visible: true,
            });
          }
        }
      );
      listeners.current.push(userLeaveListener);
      const userAudioListener = addListener(
        EventType.onUserAudioStatusChanged,
        async (event) => {
          const { changedUsers } = event;
          console.log("changedUsers", changedUsers);

          const mySelf = new ZoomVideoSdkUser(await session.getMySelf());
          const remoteUsers = await session.getRemoteUsers();

          changedUsers.forEach(async (changedUser) => {
            // Check if it's me
            if (changedUser.userId === mySelf.userId) {
              console.log("changedUsers if (mySelf)");
              const muted = await mySelf.audioStatus.isMuted();
              console.log("muted", muted);
              setAudioOn(!muted);
            } else {
              console.log("changedUsers else (remote user)");

              try {
                // Find this changed user in the remoteUsers list
                const foundRemoteUser = remoteUsers.find(
                  (u: ZoomVideoSdkUser) => u.userId === changedUser.userId
                );

                if (foundRemoteUser) {
                  const zoomRemoteUser = new ZoomVideoSdkUser(foundRemoteUser);
                  const muted = await zoomRemoteUser.audioStatus.isMuted();
                  console.log("RemoteUser: ", zoomRemoteUser);
                  setRemoteUserAudio(!muted);
                } else {
                  console.log("Remote user not found in remoteUsers list.");
                }
              } catch (err) {
                console.log("Error getting remote user audio status", err);
              }
            }
          });
        }
      );

      listeners.current.push(userAudioListener);

      const userVideoListener = addListener(
        EventType.onUserVideoStatusChanged,
        async (event) => {
          const { changedUsers } = event;
          const mySelf = new ZoomVideoSdkUser(await session.getMySelf());
          if (changedUsers.find((u) => u.userId === mySelf.userId)) {
            const on = await mySelf.videoStatus.isOn();
            setVideoOn(on);
          }
        }
      );
      listeners.current.push(userVideoListener);

      const sessionLeaveListener2 = addListener(
        EventType.onSessionLeave,
        () => {
          setCallStarted(false);
          setLocalUserName("");
          setRemoteUserName("");
          setRemoteUsers(null);
          setLocalUser(null);
          listeners.current.forEach((l) => l.remove());
          listeners.current = [];
        }
      );
      listeners.current.push(sessionLeaveListener2);

      const sessionError = addListener(EventType.onError, (event) => {
        sessionLeaveListener2.remove();
      });
      listeners.current.push(sessionError);
    } catch (err) {
      if (retryCount >= 1) {
        logEvent(
          "ERROR",
          "JOIN_SESSION_ERROR",
          `Error joining session ${retryCount + 1} times`,
          err as object
        );
        onRejoinCall();
      } else {
        logEvent(
          "ERROR",
          "JOIN_SESSION_ERROR",
          `Error joining session 1 times`,
          err as object
        );
        try {
          await leaveSession();
        } catch (leaveErr) {
          logEvent(
            "ERROR",
            "LEAVE_SESSION_ERROR",
            "Error leaving session after join failure",
            leaveErr as object
          );
        }
        setRetryCount((prev) => prev + 1);
        setTimeout(() => {
          joinSessionHandler();
        }, 5000);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleLeaveSession = async () => {
    try {
      listeners.current.forEach((listener) => listener.remove());
      listeners.current = [];

      try {
        await audioHelper.stopAudio();
      } catch {}
      try {
        await videoHelper.stopVideo();
      } catch {}

      // clear timers
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
        heartbeatIntervalRef.current = null;
      }

      await leaveSession();
      await new Promise((r) => setTimeout(r, 800));
      setVideoOn(true);
      setAudioOn(true);
      setLocalUser(null);
      setRemoteUsers(null);
      setIsRecovering(false);
      setViewKey(0);
      setRemoteUserAudio(false);
      setLocalUserName("");
      setRemoteUserName("");
      setCallStarted(false);

      logEvent("INFO", "LEAVE_SESSION_SUCCESS", "Left session successfully");
      if (consultationId && user?.token) {
        try {
          await axiosConfig.put(
            `/consultation/zoom/call-ended/${consultationId}`
          );
        } catch (e) {}
      }
    } catch (err) {
      logEvent(
        "ERROR",
        "LEAVE_SESSION_ERROR",
        "Error leaving session",
        err as object
      );
      Alert.alert("Error", "Failed to leave the session");
    } finally {
      callDetails();
      setCallEnded(true);
    }
  };

  useEffect(() => {
    if (callCompleted) {
      handleLeaveSession();
    }
  }, [callCompleted]);

  const callDetails = () => {
    goToDetails();
  };

  useEffect(() => {
    joinSessionHandler();
    return () => {};
  }, [localSDKId]);

  const callDetailsScreen = () => {
    goToDetails();
  };

  useEffect(() => {
    return () => {
      listeners.current.forEach((listener) => listener.remove());
      listeners.current = [];
    };
  }, []);

  useEffect(() => {
    if (callStarted && user?.id && consultationId) {
      const sendHeartbeat = async () => {
        try {
          await axiosConfig.patch("/consultation/zoom/session/heartbeat", {
            userId: user.id,
            consultationId,
          });
        } catch (e) {}
      };
      sendHeartbeat();
      heartbeatIntervalRef.current = setInterval(sendHeartbeat, 60000);
    }
    return () => {
      if (heartbeatIntervalRef.current)
        clearInterval(heartbeatIntervalRef.current);
    };
  }, [callStarted, user?.id, user?.token, consultationId]);

  const onRejoinCall = async () => {
    try {
      const consultationId = route.params.consultationId;
      const url = `/consultation/rejoin-request/${consultationId}`;
      const response = await axiosConfig.get(url);
      const { providerSDKJWT } = response.data;
      setLocalSDKId(providerSDKJWT);
    } catch (error) {}
  };

  const rejoinSession = async () => {
    if (callEnded) {
      try {
        await joinSessionHandler();
      } catch (err) {
        logEvent(
          "ERROR",
          "REJOIN_SESSION_ERROR",
          "Error rejoining session",
          err as object
        );
        Alert.alert("Error", "Failed to rejoin the session");
      }
      setCallEnded(false);
      return;
    }
    try {
      listeners.current.forEach((listener) => listener.remove());
      listeners.current = [];

      try {
        await audioHelper.stopAudio();
      } catch {}
      try {
        await videoHelper.stopVideo();
      } catch {}

      // clear timers
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
        heartbeatIntervalRef.current = null;
      }

      leaveSession();
      await new Promise<void>((resolve) => {
        const sub = addListener(EventType.onSessionLeave, () => {
          sub.remove();
          resolve();
        });
      });
      await new Promise((r) => setTimeout(r, 800));
      setCallEnded(false);
      setVideoOn(true);
      setAudioOn(true);
      setLocalUser(null);
      setRemoteUsers(null);
      setIsRecovering(false);
      setViewKey(0);
      setRemoteUserAudio(false);
      setLocalUserName("");
      setRemoteUserName("");
      setCallStarted(false);

      await joinSessionHandler();
    } catch (err) {
      logEvent(
        "ERROR",
        "REJOIN_CALL_ERROR",
        "Error rejoining call",
        err as object
      );
      onRejoinCall();
    }
  };

  if (loading || isRecovering) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#ffffff" />
        <View style={styles.loadingReloadVideoButtonContainer}>
          <TouchableOpacity
            style={styles.reloadVideoButton}
            onPress={() => {
              logEvent(
                "INFO",
                "MANUAL_RETRY",
                "User tapped retry in controls 1"
              );
              setDisableReload(true);
              rejoinSession();
              setTimeout(() => {
                setDisableReload(false);
              }, 5000);
            }}
          >
            <Text
              style={[
                styles.reloadButtonText,
                disableReload && { color: "grey" },
              ]}
            >
              Reload Video
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {callEnded ? (
        <View style={styles.callEndedContainer}>
          <View style={{ gap: 6 }}>
            <TouchableOpacity
              style={styles.callEndedButton}
              onPress={rejoinSession}
            >
              <PhoneCall size={24} color="#ffffff" />
            </TouchableOpacity>
            <Text>Rejoin Call</Text>
          </View>
          <View
            style={{ gap: 6, justifyContent: "center", alignItems: "center" }}
          >
            <TouchableOpacity
              style={styles.callDetailsButton}
              onPress={callDetails}
            >
              <ArrowRight size={24} color="#ffffff" />
            </TouchableOpacity>
            <Text>End Call</Text>
          </View>
        </View>
      ) : (
        <>
          <View style={styles.topSpacer} />
          <View
            style={[
              styles.videoContainer,
              isLandscape && { flexDirection: "row", gap: 10 },
            ]}
          >
            {remoteUsers ? (
              <>
                <View key={remoteUsers.userId} style={styles.videoWrapper}>
                  <ZoomView
                    style={styles.zoomView}
                    userId={remoteUsers.userId}
                    fullScreen={false}
                    videoAspect={VideoAspect.PanAndScan}
                    videoResolution={VideoResolution.ResolutionAuto}
                  />
                  <View style={styles.userInfoOverlay}>
                    <Text style={styles.userNameText}>{remoteUserName}</Text>
                    {remoteUserAudio ? (
                      <Mic size={16} color="#0f0" />
                    ) : (
                      <MicOff size={16} color="#f00" />
                    )}
                  </View>
                </View>
                {localUser && (
                  <View style={styles.videoWrapper}>
                    <ZoomView
                      key={viewKey + 1}
                      style={styles.zoomView}
                      userId={localUser.userId}
                      fullScreen={false}
                      videoAspect={VideoAspect.PanAndScan}
                      videoResolution={VideoResolution.ResolutionAuto}
                    />
                    <View style={styles.userInfoOverlay}>
                      <Text style={styles.userNameText}>{localUserName}</Text>
                      {audioOn ? (
                        <Mic size={16} color="#0f0" />
                      ) : (
                        <MicOff size={16} color="#f00" />
                      )}
                    </View>
                  </View>
                )}
              </>
            ) : localUser ? (
              <View style={[styles.videoWrapper, { flex: 1 }]}>
                <ZoomView
                  key={viewKey + 1}
                  style={styles.zoomView}
                  userId={localUser.userId}
                  fullScreen={true}
                  videoAspect={VideoAspect.PanAndScan}
                  videoResolution={VideoResolution.ResolutionAuto}
                />
                <View style={styles.userInfoOverlay}>
                  <Text style={styles.userNameText}>{localUserName}</Text>
                  {audioOn ? (
                    <Mic size={16} color="#0f0" />
                  ) : (
                    <MicOff size={16} color="#f00" />
                  )}
                </View>
              </View>
            ) : (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#ffffff" />
                <View style={styles.loadingReloadVideoButtonContainer}>
                  <TouchableOpacity
                    style={styles.reloadVideoButton}
                    onPress={() => {
                      logEvent(
                        "INFO",
                        "MANUAL_RETRY",
                        "User tapped retry in controls 2"
                      );
                      setDisableReload(true);
                      rejoinSession();
                      setTimeout(() => {
                        setDisableReload(false);
                      }, 5000);
                    }}
                  >
                    <Text
                      style={[
                        styles.reloadButtonText,
                        disableReload && { color: "grey" },
                      ]}
                    >
                      Reload Video
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>

          {callStarted && (
            <>
              <View style={styles.controlsContainer}>
                <TouchableOpacity
                  style={styles.controlButton}
                  onPress={toggleVideo}
                >
                  {videoOn ? (
                    <Video size={24} color="#ffffff" />
                  ) : (
                    <VideoOff size={24} color="#ffffff" />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.controlButton}
                  onPress={toggleAudio}
                >
                  {audioOn ? (
                    <Mic size={24} color="#ffffff" />
                  ) : (
                    <MicOff size={24} color="#ffffff" />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.controlButton, styles.endCallButton]}
                  onPress={handleLeaveSession}
                >
                  {/* <PhoneOff size={24} color="#ffffff" /> */}
                  <Text style={styles.reloadButtonText}>End</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.controlButton}
                  onPress={callDetails}
                >
                  <ArrowRight size={24} color="#ffffff" />
                </TouchableOpacity>
              </View>
              <View style={styles.reloadVideoButtonContainer}>
                <TouchableOpacity
                  style={styles.reloadVideoButton}
                  onPress={() => {
                    logEvent(
                      "INFO",
                      "MANUAL_RETRY",
                      "User tapped retry in controls"
                    );
                    // setViewKey((v) => v + 1);
                    // leave and rejoin session
                    setDisableReload(true);
                    rejoinSession();
                    setTimeout(() => {
                      setDisableReload(false);
                    }, 5000);
                  }}
                >
                  <Text
                    style={[
                      styles.reloadButtonText,
                      disableReload && { color: "grey" },
                    ]}
                  >
                    Reload Video
                  </Text>
                </TouchableOpacity>
              </View>
            </>
          )}
          <Notification
            message={notification.message}
            visible={notification.visible}
            onHide={() =>
              setNotification((prev) => ({ ...prev, visible: false }))
            }
          />
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  callEndedContainer: {
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "row",
    flex: 1,
    gap: 20,
  },
  callEndedButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "#4CAF50",
    justifyContent: "center",
    alignItems: "center",
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  callDetailsButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "#555",
    justifyContent: "center",
    alignItems: "center",
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  container: {
    flex: 1,
    backgroundColor: "#0C111D",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#0C111D",
  },
  loadingReloadVideoButtonContainer: {
    padding: 5,
    backgroundColor: "#1D2939",
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
    marginInline: 10,
    marginBottom: 20,
    marginTop: 40,
    flexDirection: "row",
    gap: 10,
  },
  topSpacer: {
    flex: 0,
  },
  videoContainer: {
    flex: 0.9,
    padding: 10,
  },
  videoWrapper: {
    flex: 1,
    marginVertical: 5,
    borderRadius: 10,
    overflow: "hidden",
    position: "relative",
    backgroundColor: "#1D2939",
  },
  zoomView: {
    width: "100%",
    height: "100%",
  },
  placeholder: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#333",
    borderRadius: 10,
    marginVertical: 5,
  },
  placeholderText: {
    color: "#ffffff",
    fontSize: 16,
    fontWeight: "500",
  },
  controlsContainer: {
    flex: 0.1,
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    paddingHorizontal: 20,
    backgroundColor: "#1D2939",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginInline: 10,
  },
  reloadVideoButtonContainer: {
    padding: 5,
    backgroundColor: "#1D2939",
    // borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
    marginInline: 10,
  },
  reloadVideoButton: {
    // backgroundColor: "#555",
    padding: 6,
    paddingHorizontal: 15,
    borderRadius: 50,
  },
  reloadButtonText: {
    color: "#ffffff",
    fontWeight: "600",
  },
  controlButton: {
    padding: 15,
    backgroundColor: "#555",
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
  },
  retryText: {
    color: "#ffffff",
    fontWeight: "600",
  },
  endCallButton: {
    backgroundColor: "#ff4444",
    height: 50,
    width: 50,
    padding: 10,
  },
  userInfoOverlay: {
    position: "absolute",
    bottom: 8,
    left: 8,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.6)",
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },

  userNameText: {
    color: "#fff",
    fontSize: 12,
    marginRight: 4,
  },
});

export default ProviderCallNative;
