import { <PERSON><PERSON><PERSON><PERSON>, FilePlus, X } from "@tamagui/lucide-icons";
import { useEffect, useState } from "react";
import { Dimensions } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { <PERSON><PERSON>, Card, ScrollView, Text, View, XStack, YStack } from "tamagui";
import { useBillingStyles } from "./Styles/BillingStyles";

export function useDebounce<T>(value: T, delay: number): T {
  const [debounced, setDebounced] = useState(value);
  useEffect(() => {
    const handler = setTimeout(() => setDebounced(value), delay);
    return () => clearTimeout(handler);
  }, [value, delay]);
  return debounced;
}

type BillingProps = {
  selectedCodes: { code: string; description: string }[];
  setSelectedCodes: React.Dispatch<
    React.SetStateAction<{ code: string; description: string }[]>
  >;
  isSubmitted: boolean;
  isAdding: "ICD" | "BILLING" | "";
  setIsAdding: React.Dispatch<React.SetStateAction<"ICD" | "BILLING" | "">>;
  isEstablishedPatient: boolean;
  onUnavailableCode?: (code: { code: string; description: string }) => void;
};

const codes = [
  { code: "99202", description: "Office/outpt visit new, straightforward MDM" },
  { code: "99203", description: "Office/outpt visit new, low MDM" },
  { code: "99204", description: "Office/outpt visit new, moderate MDM" },
  { code: "99205", description: "Office/outpt visit new, high MDM" },
  {
    code: "99212",
    description: "Office/outpt visit estab, straightforward MDM",
  },
  { code: "99213", description: "Office/outpt visit estab, low MDM" },
  { code: "99214", description: "Office/outpt visit estab, moderate MDM" },
  { code: "99215", description: "Office/outpt visit estab, high MDM" },
];

const firstTimeVisitCodes = [
  { code: "99202", description: "Office/outpt visit new, straightforward MDM" },
  { code: "99203", description: "Office/outpt visit new, low MDM" },
  { code: "99204", description: "Office/outpt visit new, moderate MDM" },
  { code: "99205", description: "Office/outpt visit new, high MDM" },
];

const establishedPatientCodes = [
  {
    code: "99212",
    description: "Office/outpt visit estab, straightforward MDM",
  },
  { code: "99213", description: "Office/outpt visit estab, low MDM" },
  { code: "99214", description: "Office/outpt visit estab, moderate MDM" },
  { code: "99215", description: "Office/outpt visit estab, high MDM" },
];

export default function BillingCodes({
  selectedCodes,
  setSelectedCodes,
  isSubmitted = false,
  isAdding,
  setIsAdding,
  isEstablishedPatient,
  onUnavailableCode,
}: BillingProps) {
  const insets = useSafeAreaInsets();
  const screenHeight = Dimensions.get("window").height;
  const screenWidth = Dimensions.get("window").width;
  const availableHeight = screenHeight - (insets.top + insets.bottom);
  const billingStyles = useBillingStyles(availableHeight, screenWidth);
  const [open, setOpen] = useState(false);
  const [availableCodes, setAvailableCodes] = useState(codes);
  const [allcodes, setAllCodes] = useState(codes);

  useEffect(() => {
    if (isEstablishedPatient) {
      setAvailableCodes([...establishedPatientCodes, ...firstTimeVisitCodes]);
      setAllCodes([...establishedPatientCodes, ...firstTimeVisitCodes]);
    } else {
      setAvailableCodes([...firstTimeVisitCodes, ...establishedPatientCodes]);
      setAllCodes([...firstTimeVisitCodes, ...establishedPatientCodes]);
    }
  }, [isEstablishedPatient]);

  const handleAddCode = (codeObj: { code: string; description: string }) => {
    if (!isCodeAvailable(codeObj)) {
      if (onUnavailableCode) {
        onUnavailableCode(codeObj);
      }
      return;
    }
    setSelectedCodes([codeObj]);
    setAvailableCodes(allcodes);
    setOpen(false);
    setIsAdding("");
  };

  const isCodeAvailable = (codeObj: { code: string; description: string }) => {
    const isFirstTimeCode = firstTimeVisitCodes.some(
      (c) => c.code === codeObj.code
    );
    return isEstablishedPatient ? !isFirstTimeCode : isFirstTimeCode;
  };

  const handleRemoveCode = (codeObj: { code: string; description: string }) => {
    setSelectedCodes([]);
    if (isEstablishedPatient) {
      setAvailableCodes([...establishedPatientCodes, ...firstTimeVisitCodes]);
    } else {
      setAvailableCodes([...firstTimeVisitCodes, ...establishedPatientCodes]);
    }
  };

  if (isAdding === "ICD") return null;

  return (
    <View>
      <Text {...billingStyles.BillingText}>Billing codes</Text>
      <Button
        {...billingStyles.assignBillingCodesBtn}
        icon={<FilePlus size={"$1"} />}
        onPress={() => {
          setIsAdding(open ? "" : "BILLING");
          setOpen(!open);
        }}
        disabled={isSubmitted}
      >
        {open ? "Close" : "Add Billing codes"}
      </Button>

      {selectedCodes.length > 0 &&
        (open ? (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={billingStyles.selectedCodesScroll}
          >
            <XStack {...billingStyles.selectedCodesContainer}>
              {selectedCodes.map((code, index) => (
                <XStack key={index} {...billingStyles.selectedCodeItem}>
                  <Text {...billingStyles.selectedCodeTextCode}>
                    {code.code}
                  </Text>
                  {!isSubmitted && (
                    <Button
                      {...billingStyles.removeCodeBtn}
                      onPress={() => handleRemoveCode(code)}
                      disabled={isSubmitted}
                      icon={<X size={"$1"} />}
                    />
                  )}
                </XStack>
              ))}
            </XStack>
          </ScrollView>
        ) : (
          <ScrollView
            style={billingStyles.codesScroll}
            showsVerticalScrollIndicator
          >
            <YStack {...billingStyles.codesContainer}>
              {selectedCodes.map((code, index) => (
                <XStack key={index} {...billingStyles.selectedCodeItem}>
                  <Text {...billingStyles.selectedCodeTextCode}>
                    {code.code}
                  </Text>
                  <Text {...billingStyles.selectedCodeText}>
                    {code.description}
                  </Text>
                  {!isSubmitted && (
                    <Button
                      {...billingStyles.removeCodeBtn}
                      onPress={() => handleRemoveCode(code)}
                      disabled={isSubmitted}
                      icon={<X size={"$1"} />}
                    />
                  )}
                </XStack>
              ))}
            </YStack>
          </ScrollView>
        ))}

      {open && (
        <YStack {...billingStyles.frequentlyUsedContainer}>
          <ScrollView
            showsVerticalScrollIndicator
            style={{ maxHeight: availableHeight * 0.4 }}
          >
            {availableCodes.map((item, index) => (
              <XStack
                key={index}
                {...billingStyles.frequentlyUsedItem}
                onPress={() => handleAddCode(item)}
              >
                <YStack {...billingStyles.codeContainer}>
                  <Text {...billingStyles.frequentlyUsedCode}>{item.code}</Text>
                </YStack>
                <Text {...billingStyles.frequentlyUsedDescription}>
                  {item.description}
                </Text>
              </XStack>
            ))}
          </ScrollView>
        </YStack>
      )}

      {!open && selectedCodes.length === 0 && (
        <YStack {...billingStyles.noBiilingCodestabBody}>
          <Card {...billingStyles.noBillingCodetabCard}>
            <YStack {...billingStyles.noBillingodesCard} paddingBlock={20}>
              <CircleAlert size={"$2"} />
              <Text {...billingStyles.noBillingCodesAddedText}>
                No Billing codes added
              </Text>
            </YStack>
          </Card>
        </YStack>
      )}
    </View>
  );
}
