import { useFocusEffect } from "expo-router";
import React from "react";
import { FlatList, RefreshControl } from "react-native";
import Consultation from "src/components/Consultation";
import {
  getShouldRefetchConsultations,
  setShouldRefetchConsultations,
} from "src/globals/consultationFlag";
import { useConsultations } from "src/hooks/useConsultations";
import { Spinner, Text, YStack } from "tamagui";

export const ProviderConsultations = ({
  tab,
  searchQuery,
}: {
  tab: "Completed" | "Pending";
  searchQuery: string;
}) => {
  const {
    consultations,
    loading,
    error,
    loadMore,
    hasMore,
    refreshConsultations,
  } = useConsultations(searchQuery);

  useFocusEffect(
    React.useCallback(() => {
      if (getShouldRefetchConsultations()) {
        refreshConsultations();
        setShouldRefetchConsultations(false);
      }
    }, [])
  );

  if (loading && !consultations) {
    return (
      <YStack {...ProviderConsultationStyle.spinner}>
        <Spinner size="large" />
      </YStack>
    );
  }
  const data =
    tab === "Completed"
      ? (consultations?.completed?.data ?? [])
      : (consultations?.pending?.data ?? []);

  return (
    <FlatList
      data={data.length > 0 ? data : []} // Ensure data is an array
      keyExtractor={(item) => item.id}
      renderItem={({ item }) => (
        <Consultation
          data={item}
          isFromProvider={true}
          isFromCallScreen={false}
          shouldShowDay={true}
        />
      )}
      showsVerticalScrollIndicator={false}
      onEndReached={hasMore ? loadMore : null} // Load more data when reaching the end
      onEndReachedThreshold={0.5} // Trigger `loadMore` when 50% from bottom
      ListEmptyComponent={
        !loading && data.length === 0 ? (
          <YStack
            {...ProviderConsultationStyle.noConsultationsText}
            marginBlockStart={50}
          >
            <Text>No Consultations Available.</Text>
          </YStack>
        ) : null
      }
      ListFooterComponent={
        hasMore && data.length > 0 ? (
          <Spinner size="large" style={{ marginBlockStart: 10 }} />
        ) : null
      } // Show a spinner while loading more data
      refreshControl={
        <RefreshControl
          refreshing={loading}
          onRefresh={refreshConsultations}
          colors={["red"]} // Change this to your desired color
          tintColor="blue" // iOS spinner color
          progressBackgroundColor="blue" // Background color for Android
        />
      }
    />
  );
};

export default ProviderConsultations;

const ProviderConsultationStyle = {
  spinner: {
    justifyContent: "center",
    alignItems: "center",
    flex: 1,
  },
  noConsultationsText: {
    justifyContent: "center",
    alignItems: "center",
    flex: 1,
    color: "$textcolor",
    fontSize: 20,
    fontWeight: 600,
  },
};
