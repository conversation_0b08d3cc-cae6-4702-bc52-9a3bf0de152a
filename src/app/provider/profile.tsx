import { Consultation } from "src/components/Consultation";
import DemographicsDropDown from "src/components/DemographicsDropDown";
import RecentConsultations from "src/components/RecentConsultations";
import { ScrollView, Text, YStack, useTheme } from "tamagui";
import { HorizontalDashedLine } from "src/components/DashedLine";
import {
  ArrowUpDown,
  Weight,
  Ruler,
  Wind,
  Heart,
  Thermometer,
  Frown,
  Activity,
} from "lucide-react-native";
import DropdownDetails from "src/components/DropdownDetails";
import React from "react";
import { useProfileStyles } from "./Styles/ProfileStyles";
interface ProfileProps {
  consultation: any;
}

export default function Profile({ consultation }: ProfileProps) {
  const theme = useTheme();
  const profileStyle = useProfileStyles();

  const vitals = consultation?.patient_details_snapshot?.vitals || {};

  const renderVitalBoxes = () => {
    const order = [
      "heartrate",
      "bloodpressure",
      "temperature",
      "respirations",
      "painlevel",
    ];

    const sortedEntries = Object.entries(vitals).sort(([a], [b]) => {
      const aKey = a.toLowerCase();
      const bKey = b.toLowerCase();
      const ia = order.indexOf(aKey);
      const ib = order.indexOf(bKey);
      const ra = ia >= 0 ? ia : order.length;
      const rb = ib >= 0 ? ib : order.length;
      return ra - rb;
    });

    return sortedEntries.map(([key, m]: any) => {
      const label =
        key.toLowerCase() === "bloodpressure"
          ? "Blood Pressure"
          : key.charAt(0).toUpperCase() + key.slice(1);

      const recorded = m.recorded;
      let Icon: React.ComponentType<any> = Activity;
      switch (key) {
        case "weight":
          Icon = Weight;
          break;
        case "height":
          Icon = Ruler;
          break;
        case "heartrate":
          Icon = Heart;
          break;
        case "temperature":
          Icon = Thermometer;
          break;
        case "respirations":
          Icon = Wind;
          break;
        case "painLevel":
        case "painlevel":
          Icon = Frown;
          break;
        case "bloodpressure":
          Icon = ArrowUpDown;
          break;
      }

      const valueText =
        key.toLowerCase() === "bloodpressure"
          ? `${m.systolic}/${m.diastolic}${m.unit ? " " + m.unit : ""}`
          : `${m.value}${m.unit ? " " + m.unit : ""}`;

      return (
        <YStack key={key}>
          <YStack key={key} {...profileStyle.vitalBoxContainer}>
            <Text verticalAlign={"center"} fontSize={10} mb={6}>
              {label}
            </Text>
            <Icon size={24} color={theme.textcolor.val} />
            <Text
              fontSize={12}
              fontWeight="500"
              verticalAlign={"center"}
              color="$confirmOrderTextColor"
              style={{
                width: "100%",
                textAlign: "center",
              }}
            >
              {valueText}
            </Text>
          </YStack>
          {(() => {
            const [datePart, ...rest] = recorded.split(" ");
            const timePart = rest.join(" ");
            return (
              <Text {...profileStyle.vitalBoxDateAndTimeContainer}>
                {datePart}
                {"\n"}
                {timePart}
              </Text>
            );
          })()}
        </YStack>
      );
    });
  };
  return (
    <YStack {...profileStyle.mainContainer}>
      <YStack {...profileStyle.childContainer}>
        <Text {...profileStyle.profileText}>Profile</Text>
        <ScrollView
          showsVerticalScrollIndicator={false}
          keyboardDismissMode="interactive"
        >
          <YStack marginBlockEnd={20}>
            <YStack>
              <Consultation
                data={consultation}
                isFromProvider
                isFromCallScreen
                shouldSowOrderandBadge={false}
              />
            </YStack>

            <YStack>
              <HorizontalDashedLine
                height={1}
                dashLength={2}
                dashGap={2}
                color="#D2D6DB"
                style={{ marginTop: 18 }}
              />
            </YStack>
            {Object.keys(vitals).length > 0 && (
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <YStack flexDirection="row" gap="$3" mt="$4">
                  {renderVitalBoxes()}
                </YStack>
              </ScrollView>
            )}
            <YStack>
              <RecentConsultations
                title="Recent Consultations"
                data={consultation.recent_consultations}
              />
            </YStack>
            <DemographicsDropDown
              title="Demographics"
              data={consultation?.patient_details_snapshot}
            />
            <YStack>
              <DropdownDetails
                title="Medical History"
                data={
                  consultation?.patient_details_snapshot?.medicalHistory || ""
                }
              />
            </YStack>
            <YStack>
              <DropdownDetails
                title="Medications"
                data={consultation?.patient_details_snapshot?.medications || ""}
              />
            </YStack>
            <YStack>
              <DropdownDetails
                title="Allergies"
                data={consultation?.patient_details_snapshot?.allergies || ""}
              />
            </YStack>
          </YStack>
        </ScrollView>
      </YStack>
    </YStack>
  );
}
