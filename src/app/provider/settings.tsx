import { useTheme } from "@/_layout";
import { useRouter } from "expo-router";
import { useState } from "react";
import FacilityDrawer from "src/components/FacilityDrawer";
import Notifications from "src/components/Notifications";
import Profile from "src/components/Profile";
import ProfilePhotoSheet from "src/components/ProfilePhotoSheet";
import ScreenHeader from "src/components/ScreenHeader";
import SheetDemo from "src/components/SettingsDrawer";
import { VersionInfoButton } from "src/components/VersionInfoButton";
import { ScrollView, Text, YStack } from "tamagui";

export default function Settings() {
  const SettingtStyles = {
    container: { flex: 1, backgroundColor: "$screenBackgroundcolor" },
    mainStack: {
      flex: 1,
      marginBlockStart: 20,
      marginInline: 20,
      paddingBlockEnd: 20,
    },
    appearanceContainer: {
      marginBlock: 20,
      marginInline: 20,
    },
    appearanceTitle: {
      fontWeight: "500" as any,
      fontSize: 16,
      marginBlockEnd: 10,
    },
    appearanceBlock: {
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 1,
      borderRadius: 8,
      marginBlockStart: 20,
    },
    appearenceBlock: {
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 1,
      borderRadius: 8,
    },
  };

  const router = useRouter();
  const [open, setOpen] = useState(false);
  const openSettings = () => setOpen(true);
  const navigateBack = () => router.back();
  const [isDropdownOpen, setDropdownOpen] = useState(false);
  const { setTheme } = useTheme();
  const [onOpenSheet, setOnOpenSheet] = useState(false);
  const themeValues = [
    { name: "Light Theme", id: "2" },
    { name: "Dark Theme", id: "3" },
  ];

  const setBackgroundTheme = (id: string) => {
    if (id === "2") {
      setTheme("light");
    } else if (id === "3") {
      setTheme("dark");
    }
  };

  return (
    <YStack {...SettingtStyles.container}>
      <YStack {...SettingtStyles.mainStack}>
        <ScreenHeader
          onAvatarPress={openSettings}
          screenName="Settings"
          onBackPress={navigateBack}
        />
        <ScrollView
          style={{ flex: 1 }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <YStack marginBlock={20}>
            <YStack>
              <Profile onOpenSheet={setOnOpenSheet} />
              <YStack marginBlockStart={20}>
                <Notifications />
              </YStack>
              <YStack {...SettingtStyles.appearanceBlock}>
                <YStack {...SettingtStyles.appearanceContainer}>
                  <Text {...SettingtStyles.appearanceTitle}>Appearance</Text>
                  <FacilityDrawer
                    data={themeValues}
                    placeholder="System Settings"
                    onSelect={(id: string) => setBackgroundTheme(id)}
                    onOpen={() => setDropdownOpen(true)}
                    onClose={() => setDropdownOpen(false)}
                  />
                </YStack>
              </YStack>
              <VersionInfoButton />
            </YStack>
          </YStack>
        </ScrollView>
        {open && <SheetDemo open={open} setOpen={setOpen} />}
      </YStack>
      <ProfilePhotoSheet open={onOpenSheet} setOpen={setOnOpenSheet} />
    </YStack>
  );
}
