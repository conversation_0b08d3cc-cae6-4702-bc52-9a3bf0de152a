import React, { useState, useEffect, useCallback } from "react";
import { TouchableOpacity } from "react-native";
import ProviderConsultations from "./ProviderConsultations";
import { View, Text, YStack } from "tamagui";
import SearchInput from "src/components/SearchInput";
import { useTheme } from "@/_layout";

export const ConsultationTabs = () => {
  const [activeTab, setActiveTab] = useState<"Pending" | "Completed">(
    "Pending"
  );
  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");

  const ConsultationTbaStyle = useConsultationTbaStyle();

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500);

    return () => clearTimeout(handler); // Cleanup previous timeout
  }, [searchQuery]);

  return (
    <View {...ConsultationTbaStyle.container}>
      <View {...ConsultationTbaStyle.tabContainer}>
        <TouchableOpacity
          style={[
            { ...ConsultationTbaStyle.tab },
            activeTab === "Pending" && { ...ConsultationTbaStyle.activeTab },
          ]}
          onPress={() => setActiveTab("Pending")}
        >
          <Text
            style={[
              { ...ConsultationTbaStyle.tabText },
              activeTab === "Pending" && {
                ...ConsultationTbaStyle.activeTabText,
              },
            ]}
          >
            Pending
          </Text>
        </TouchableOpacity>

        <View {...ConsultationTbaStyle.divider} />

        <TouchableOpacity
          style={[
            { ...ConsultationTbaStyle.tab },
            activeTab === "Completed" && { ...ConsultationTbaStyle.activeTab },
          ]}
          onPress={() => setActiveTab("Completed")}
        >
          <Text
            style={[
              { ...ConsultationTbaStyle.tabText },
              activeTab === "Completed" && {
                ...ConsultationTbaStyle.activeTabText,
              },
            ]}
          >
            Completed
          </Text>
        </TouchableOpacity>
      </View>

      <YStack marginBlockStart={20}>
        <SearchInput onSearchChange={setSearchQuery} />
      </YStack>

      <View {...ConsultationTbaStyle.contentContainer}>
        <ProviderConsultations
          tab={activeTab}
          searchQuery={debouncedSearchQuery}
        />
      </View>
    </View>
  );
};

const useConsultationTbaStyle = () => {
  const { theme } = useTheme();
  const isDarktheme = theme === "dark";
  return {
    container: {
      flex: 1,
    },
    tabContainer: {
      flexDirection: "row" as any,
      borderRadius: 7,
      borderWidth: 1,
      borderColor: !isDarktheme ? "#D0D5DD" : ("#697586" as any),
      backgroundColor: "$screenBackgroundcolor" as any,
      overflow: "hidden" as any,
    },
    tab: {
      flex: 1,
      alignItems: "center" as any,
      justifyContent: "center" as any,
      paddingVertical: 12,
      backgroundColor: !isDarktheme ? "#FFFFFF" : "#0C0E12",
    },
    activeTab: {
      backgroundColor: !isDarktheme ? "#E5E7EB" : "#22262F",
    },
    tabText: {
      fontSize: 14,
      color: !isDarktheme ? "#344054" : "#94979C",
      fontWeight: 600,
    },
    activeTabText: {
      color: !isDarktheme ? "#1D2939" : "#FFFFFF",
    },
    divider: {
      width: 1,
      backgroundColor: "$primaryBorderColor" as any,
      height: "100%" as any,
      alignSelf: "center" as any,
    },
    contentContainer: {
      marginBlockStart: 5,
      backgroundColor: "$screenBackgroundcolor",
      flex: 1,
    },
  };
};

export default ConsultationTabs;
