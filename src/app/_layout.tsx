import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Stack, useRouter } from "expo-router";
import { createContext, useContext, useEffect, useState } from "react";
import {
  ActivityIndicator,
  NativeModules,
  Platform,
  SafeAreaView,
  StatusBar,
  useColorScheme,
  View,
} from "react-native";
import { TamaguiProvider } from "tamagui";
import tamaguiConfig from "tamagui.config";
import { AuthProvider, useAuth } from "../context/AuthContext";
import { layoutStyles } from "./styles/LayoutStyle";
// import { ZoomVideoSdkProvider } from "@zoom/react-native-videosdk";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { requestAndUpdatePermissions } from "lib";
import RNCallKeep, { IOptions } from "react-native-callkeep";
import UpdateProvider from "src/hooks/UpdateProvider";
import { ConsultationRequestProvider } from "~/context/ConsultationRequestContext";
import { UpdateModal } from "../components/UpdateModal";
import { useUrlSchemeAuth } from "../hooks/useUrlSchemeAuth";
import { useKeepAwake } from "expo-keep-awake";
import * as SecureStore from "expo-secure-store";
import DeviceInfo from "react-native-device-info";

const options: IOptions = {
  ios: {
    appName: "VitalCare",
    supportsVideo: true,
    maximumCallGroups: "1",
    maximumCallsPerCallGroup: "1",
  },
  android: {
    alertTitle: "Permissions required",
    alertDescription: "This app needs to access your call accounts",
    cancelButton: "Cancel",
    okButton: "OK",
    additionalPermissions: [],
  },
};

// Define theme type
type Theme = "light" | "dark";

// Define ThemeContext type
interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
}

// Create Theme Context with default value
const ThemeContext = createContext<ThemeContextType>({
  theme: "dark",
  setTheme: () => {},
});

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 10 * 60 * 1000,
      refetchOnWindowFocus: false,
      retry: 2,
    },
  },
});

function RootContent() {
  const { user, loading } = useAuth();
  const router = useRouter();

  // Use the URL scheme authentication hook
  useUrlSchemeAuth();

  useEffect(() => {
    if (!loading) {
      if (!user) {
        console.log("redirecting to login page as no user is found");
        router.replace("/");
      } else if (user.role === "nurse") {
        router.replace("/nurse/dashboard");
      } else if (user.role === "provider") {
        router.replace("/provider/dashboard");
      } else if (user.role === "admin") {
        // Use a valid route for admin
        router.replace("/admin/dashboard");
      }
    }
  }, [loading, user]);

  if (loading) {
    return (
      <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  return (
    <>
      <Stack
        screenOptions={{
          headerShown: false,
          contentStyle: { backgroundColor: "white" },
        }}
      >
        <Stack.Screen name="index" />
      </Stack>
      <UpdateModal />
    </>
  );
}

export default function RootLayout() {
  const { container } = layoutStyles;
  const systemColorScheme = useColorScheme();
  const [theme, setTheme] = useState<Theme>("dark");
  const [isLoading, setIsLoading] = useState(true);
  useKeepAwake();

  useEffect(() => {
    requestAndUpdatePermissions();
  }, []);

  useEffect(() => {
    const storeDeviceUUID = async () => {
      try {
        const storedUUID = await SecureStore.getItemAsync("deviceUUID");

        if (!storedUUID) {
          const deviceUUID = await DeviceInfo.getUniqueId();
          await SecureStore.setItemAsync("deviceUUID", deviceUUID, {
            keychainAccessible: SecureStore.AFTER_FIRST_UNLOCK_THIS_DEVICE_ONLY,
          });
        }
      } catch (error) {
        console.log("Error storing/Fecthing device UUID:", error);
      }
    };

    storeDeviceUUID();
  }, []);

  useEffect(() => {
    if (Platform.OS === "ios") {
      RNCallKeep.setup(options);
      RNCallKeep.setAvailable(true);
      setTimeout(() => {
        RNCallKeep.getCalls()
          .then((calls: any) => {
            if (calls && Array.isArray(calls) && calls.length > 0) {
              calls.forEach((call: any) => {
                if (call && call.callUUID) {
                  RNCallKeep.endCall(call.callUUID);
                }
              });
            } else {
              console.log("No active calls found to end on app initialization");
            }
          })
          .catch((error: any) => {
            console.error("Error getting calls:", error);
          });
      }, 3000);

      return () => {
        try {
          const { RingtoneModule } = NativeModules;
          if (RingtoneModule && RingtoneModule.endCurrentCallKitCall) {
            RingtoneModule.endCurrentCallKitCall();
          }
        } catch (error) {
          console.error("Error ending CallKit calls during cleanup:", error);
        }
      };
    }
  }, []);

  useEffect(() => {
    const loadTheme = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem("appTheme");
        if (savedTheme) {
          const validThemes: Theme[] = ["light", "dark"];
          if (validThemes.includes(savedTheme as Theme)) {
            setTheme(savedTheme as Theme);
          }
        }
      } catch (error) {
        console.error("Error loading theme:", error);
      } finally {
        setIsLoading(false);
      }
    };
    loadTheme();
  }, []);

  const handleSetTheme = async (newTheme: Theme) => {
    try {
      setTheme(newTheme);
      await AsyncStorage.setItem("appTheme", newTheme);
    } catch (error) {
      console.error("Error saving theme:", error);
    }
  };

  const effectiveTheme: "light" | "dark" = theme === "light" ? "light" : "dark";

  const isDarkMode = effectiveTheme === "dark";

  if (isLoading) {
    return (
      <View style={{ flex: 1, alignItems: "center", justifyContent: "center" }}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  return (
    // <ZoomVideoSdkProvider
    //   config={{
    //     domain: "zoom.us",
    //     enableLog: true,
    //     // Disable CallKit integration in Zoom SDK
    //     enableCallKit: false,
    //   }}
    // >
    <AuthProvider>
      <QueryClientProvider client={queryClient}>
        <ThemeContext.Provider value={{ theme, setTheme: handleSetTheme }}>
          <UpdateProvider>
            <ConsultationRequestProvider>
              <TamaguiProvider
                config={tamaguiConfig}
                defaultTheme={effectiveTheme}
              >
                <StatusBar
                  barStyle={isDarkMode ? "light-content" : "dark-content"}
                  backgroundColor={isDarkMode ? "#000000" : "#FFFFFF"}
                />
                <SafeAreaView
                  style={[
                    container,
                    { backgroundColor: isDarkMode ? "#000000" : "#FFFFFF" },
                  ]}
                >
                  <RootContent />
                </SafeAreaView>
              </TamaguiProvider>
            </ConsultationRequestProvider>
          </UpdateProvider>
        </ThemeContext.Provider>
      </QueryClientProvider>
    </AuthProvider>
    // </ZoomVideoSdkProvider>
  );
}

export function useTheme(): ThemeContextType {
  return useContext(ThemeContext);
}
