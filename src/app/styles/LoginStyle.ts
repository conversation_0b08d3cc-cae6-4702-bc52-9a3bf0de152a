export const useLoginStyles = () => {
  return {
    parent: {
      flex: 1,
      backgroundColor: "$screenBackgroundcolor",
      display: "flex" as "flex",
      justifyContent: "space-between" as "space-between",
    },
    container: { marginInline: 15 },
    header: { alignItems: "center" as "center", marginBlockStart: 15 },
    image: (imageWidth: number) => ({
      width: imageWidth,
      height: imageWidth * 0.3,
      objectFit: "contain" as "contain",
    }),
    title: { fontSize: 24, fontWeight: "600" as any },
    subtitle: {
      fontSize: 18,
      fontWeight: "400" as any,
      marginBlockStart: 16,
    },
    formContainer: { marginBlockStart: 0 },
    rememberContainer: {
      alignItems: "center" as "center",
      justifyContent: "space-between" as "space-between",
      marginBlockStart: 20,
      justify: "flex-end" as any,
    },
    checkbox: {
      marginBlockStart: 12,
      backgroundColor: "$screenBackgroundcolor" as any,
      borderColor: "$primaryBorderColor" as any,
    },
    label: { marginInlineStart: 8, fontSize: 16 },
    forgotPassword: {
      fontSize: 14,
      color: "$loginForgotPasswordColor" as any,
      fontWeight: "600" as any,
    },
    buttonContainer: {
      marginBlockStart: 20,
      display: "flex" as "flex",
      justifyContent: "center" as "center",
      gap: 20,
    },
    signInButton: {
      backgroundColor: "$primaryColor",
      color: "$buttonWhiteColor" as any,
      // backgroundColor: "$buttonWhiteColor",
      // color: "$primaryColor" as any,
      fontSize: 16,
      borderColor: "$primaryColor" as any,
      borderStyle: "solid" as "solid",
      borderRadius: 8,
      borderWidth: 2,
      size: "$4" as "$4",
      fontWeight: "600" as any,
    },
    backButton: {
      backgroundColor: "$screenBackgroundcolor",
      color: "$textcolor" as any,
      fontSize: 16,
      size: "$4" as "$4",
      fontWeight: "600" as any,
      borderWidth: 2,
    },
    pccButton: {
      display: "flex" as "flex",
      justifyContent: "center" as "center",
      alignItems: "center" as "center",
      marginTop: 25,
      // add a shadow behind the button
      shadowColor: "$primaryBorderColor" as any,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.8,
      shadowRadius: 2,
      elevation: 1,
    },
    pccIcon: {
      width: "100%",
      maxWidth: 300,
      height: 60,
    },
    imageContainer: {
      alignItems: "center" as "center",
      justifyContent: "center" as "center",
    },
  };
};
