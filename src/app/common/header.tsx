import { useTheme } from "@/_layout";
import { HeaderStyle } from "@/styles/HeaderStyle";
import { useEffect, useState } from "react";
import { Pressable } from "react-native";
import { Avatar, Image, Text, XStack } from "tamagui";
import { useAuth } from "~/context/AuthContext";
import UserAlerts from "./userAlerts";
// Import styles

interface HeaderProps {
  onAvatarPress?: () => void;
}

export default function Header({ onAvatarPress }: HeaderProps) {
  const { localUser } = useAuth();
  const initials = localUser
    ? localUser?.firstName?.charAt(0) + localUser?.lastName?.charAt(0)
    : "User";
  const [profileImage, setProfileImage] = useState<string>("");
  useEffect(() => {
    if (
      localUser?.profilePicUrl !== null &&
      localUser?.profilePicUrl !== undefined &&
      localUser?.profilePicUrl !== ""
    ) {
      setProfileImage(localUser?.profilePicUrl);
    }
  }, [localUser?.profilePicUrl]);

  const { theme } = useTheme();
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    setIsDarkMode(theme === "dark");
  }, [theme]);

  return (
    <XStack {...HeaderStyle.container}>
      <Image
        source={
          isDarkMode
            ? require("../../assets/images/vital-care-logo-dark.png")
            : require("../../assets/images/vital-care-logo-light.png")
        }
        {...HeaderStyle.logo}
      />

      <XStack {...HeaderStyle.container}>
        <UserAlerts />
        <Pressable onPress={onAvatarPress}>
          <Avatar {...HeaderStyle.avatarContainer}>
            <Avatar.Image source={{ uri: profileImage }} />
            <Avatar.Fallback
              style={{
                backgroundColor: "#1570EF",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Text style={{ color: "white" }}>{initials}</Text>
            </Avatar.Fallback>
          </Avatar>
        </Pressable>
      </XStack>
    </XStack>
  );
}
