import React, { useEffect, useState } from "react";
import { Pressable } from "react-native";
import { XStack } from "tamagui";
import FontAwesome6 from "@expo/vector-icons/FontAwesome6";
import CustomBadge from "./customBadge";
import { useRouter } from "expo-router";
import { useAlertsContext } from "src/context/AlertsContext";
import { useAuth } from "~/context/AuthContext";
import { MessageCircle } from "lucide-react-native";
import { useTheme } from "@/_layout";

interface UseAlertProps {
  shouldNavibgateToChat?: boolean;
  consultationId?: string;
}

const UserAlerts = ({
  shouldNavibgateToChat = false,
  consultationId,
}: UseAlertProps) => {
  const router = useRouter();
  const { count, alerts } = useAlertsContext();
  const { user } = useAuth();
  const role = user?.role;
  const { theme } = useTheme();
  const isDarktheme = theme === "dark";
  const [currentNotificationCount, setCurrentConsultationMessageCount] =
    useState(0);

  useEffect(() => {
    if (
      !shouldNavibgateToChat ||
      consultationId === undefined ||
      !alerts ||
      !user?.id
    )
      return;

    const alert = alerts.find((a) => a?.consultationId === consultationId);
    if (!alert) return;

    const { lastMessage } = alert;
    const unRead = lastMessage?.user?.id !== user.id && !lastMessage?.read;
    setCurrentConsultationMessageCount(unRead ? 1 : 0);
  }, [shouldNavibgateToChat, consultationId, alerts, user?.id]);

  return (
    <XStack position="relative" style={{ marginRight: 18 }}>
      <Pressable
        onPress={() => {
          if (!role) return;
          if (shouldNavibgateToChat) {
            router.push({
              pathname: "/provider/chat",
              params: { consultationId },
            });
          } else {
            router.push(`/${role}/messages`);
          }
        }}
      >
        <MessageCircle size={30} color={isDarktheme ? "white" : "black"} />
        {!shouldNavibgateToChat && count > 0 && <CustomBadge count={count} />}

        {shouldNavibgateToChat && currentNotificationCount > 0 && (
          <CustomBadge count={currentNotificationCount} />
        )}
      </Pressable>
    </XStack>
  );
};

export default UserAlerts;
