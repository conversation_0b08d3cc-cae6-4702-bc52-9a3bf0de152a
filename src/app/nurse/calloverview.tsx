import { CommonActions, RouteProp, useRoute } from "@react-navigation/native";
import { Save } from "@tamagui/lucide-icons";
import { useNavigation, useRouter } from "expo-router";
import { Phone } from "lucide-react-native";
import { useEffect, useRef, useState } from "react";
import { Keyboard, Modal, TouchableOpacity } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import Consultation from "src/components/Consultation";
import { DialogBox } from "src/components/Dialog";
import RatingModule from "src/components/RatingModule";
import { ThemeToggle } from "src/components/ThemeToggle";
import { useConsultationV2 } from "src/hooks/useCOnsultationV2";
import { ConsultationV2 } from "src/types/consultationV2";
import { Button, Spinner, Text, TextArea, View, XStack, YStack } from "tamagui";
import { useSocket } from "~/context/NurseSocketContext";
import axiosConfig from "~/services/axiosConfig";
type CalloverViewRouteParams = {
  params: {
    consultationId: string;
  };
};

const logEvent = (
  level: string,
  status: string,
  message: string,
  metadata: object = {}
) => {
  axiosConfig.post("/log-event", {
    level,
    status,
    name: "CallOverview",
    message,
    metadata,
  });
};

const CalloverView = () => {
  const { socket } = useSocket();
  const router = useRouter();
  const route = useRoute<RouteProp<CalloverViewRouteParams, "params">>();
  const { consultationId } = route.params;
  const [dialogOpen, setDialogOpen] = useState(false);

  const navigation = useNavigation();
  const { consultation: initialConsultation, loading } =
    useConsultationV2(consultationId);
  const [consultation, setConsultation] = useState<ConsultationV2 | null>(null);
  const scrollViewRef = useRef<any>(null);
  const inputRef = useRef<any>(null);
  const [overallExperienceRating, setOverallExperienceRating] = useState(0);
  const [qualityOfCallRating, setQualityOfCallRating] = useState(0);
  const [providerRating, setProviderRating] = useState(0);
  const [otherFeedback, setOtherFeedback] = useState("");
  const [isEditable, setIsEditable] = useState(true);
  const [submittingRating, setSubmittingRating] = useState(false);
  const [commentsSubmitted, setCommentsSubmitted] = useState(false);
  const [submittingOverallRating, setSubmittingOverallRating] = useState(false);
  const [submittingQualityRating, setSubmittingQualityRating] = useState(false);
  const [submittingProviderRating, setSubmittingProviderRating] =
    useState(false);
  const [overallRatingSubmitted, setOverallRatingSubmitted] = useState(false);
  const [qualityRatingSubmitted, setQualityRatingSubmitted] = useState(false);
  const [providerRatingSubmitted, setProviderRatingSubmitted] = useState(false);

  useEffect(() => {
    if (initialConsultation) {
      setConsultation(initialConsultation);
    }
  }, [initialConsultation]);

  useEffect(() => {
    if (!socket || !consultationId) {
      return;
    }

    const handleOrderSubmitted = (data: {
      order: string;
      consultationId: string;
    }) => {
      const { order, consultationId: submittedId } = data;
      if (submittedId === consultationId) {
        setConsultation((prev) => {
          if (!prev) return prev;
          return {
            ...prev,
            order,
          };
        });
      }
    };

    socket.on("orderSubmitted", handleOrderSubmitted);

    return () => {
      socket.off("orderSubmitted", handleOrderSubmitted);
    };
  }, [socket]);

  const clearStackAndMove = () => {
    navigation.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{ name: "dashboard" }],
      })
    );
  };

  const onFinishLaterPress = () => {
    setDialogOpen(true);
  };
  const onFinishLaterDialog = () => {
    setDialogOpen(false);
    setTimeout(() => {
      clearStackAndMove();
    }, 100);
  };

  const onClosePress = () => {
    setDialogOpen(false);
    setTimeout(() => {
      clearStackAndMove();
    }, 100);
  };

  const autoSaveAllRatings = async (
    updatedField: "overall" | "quality" | "provider",
    newRating: number
  ) => {
    try {
      // Set the appropriate loading state based on which field is being updated
      if (updatedField === "overall") setSubmittingOverallRating(true);
      if (updatedField === "quality") setSubmittingQualityRating(true);
      if (updatedField === "provider") setSubmittingProviderRating(true);

      // Build payload with all current ratings
      const payload: any = {
        consultationId: consultationId,
      };

      // Add overall satisfaction if it has a value or is being updated
      const currentOverallRating =
        updatedField === "overall" ? newRating : overallExperienceRating;
      if (currentOverallRating > 0) {
        payload.overallSatisfaction = currentOverallRating;
      }

      // Add technical quality if it has a value or is being updated
      const currentQualityRating =
        updatedField === "quality" ? newRating : qualityOfCallRating;
      if (currentQualityRating > 0) {
        payload.technicalQuality = currentQualityRating;
      }

      // Add provider communication if it has a value or is being updated
      const currentProviderRating =
        updatedField === "provider" ? newRating : providerRating;
      if (currentProviderRating > 0) {
        payload.providerCommunication = currentProviderRating;
      }

      // Add comments if not empty
      if (otherFeedback && otherFeedback.trim() !== "") {
        payload.comments = otherFeedback.trim();
      }

      const result = await axiosConfig.post(`/consultation/rating`, payload);
      if (result.status === 201 || result.status === 200) {
        // Set the submitted state for the specific field that was updated
        if (updatedField === "overall") setOverallRatingSubmitted(true);
        if (updatedField === "quality") setQualityRatingSubmitted(true);
        if (updatedField === "provider") setProviderRatingSubmitted(true);

        // Clear the submitted state after 3 seconds
        setTimeout(() => {
          if (updatedField === "overall") setOverallRatingSubmitted(false);
          if (updatedField === "quality") setQualityRatingSubmitted(false);
          if (updatedField === "provider") setProviderRatingSubmitted(false);
        }, 3000);
      }
    } catch (error) {
      logEvent(
        "ERROR",
        "CALL_OVERVIEW_SAVE_RATING",
        `Error auto-saving ${updatedField} rating: ${error}`
      );
      throw error;
    } finally {
      // Clear all loading states
      setSubmittingOverallRating(false);
      setSubmittingQualityRating(false);
      setSubmittingProviderRating(false);
    }
  };

  const autoSaveOverallRating = async (rating: number) => {
    await autoSaveAllRatings("overall", rating);
  };

  const autoSaveQualityRating = async (rating: number) => {
    await autoSaveAllRatings("quality", rating);
  };

  const autoSaveProviderRating = async (rating: number) => {
    await autoSaveAllRatings("provider", rating);
  };

  const submitComments = async () => {
    if (!otherFeedback || otherFeedback.trim() === "") {
      return;
    }

    try {
      setSubmittingRating(true);
      const payload: any = {
        consultationId: consultationId,
        comments: otherFeedback.trim(),
      };
      if (overallExperienceRating > 0) {
        payload.overallSatisfaction = overallExperienceRating;
      }
      if (qualityOfCallRating > 0) {
        payload.technicalQuality = qualityOfCallRating;
      }
      if (providerRating > 0) {
        payload.providerCommunication = providerRating;
      }

      const result = await axiosConfig.post(`/consultation/rating`, payload);
      if (result.status === 201 || result.status === 200) {
        // Dismiss keyboard
        Keyboard.dismiss();
        setCommentsSubmitted(true);

        // Scroll to end after keyboard dismissal with a small delay
        setTimeout(() => {
          if (scrollViewRef.current) {
            scrollViewRef.current.scrollToEnd({ animated: true });
          }
        }, 100);

        // Clear the success message after 3 seconds
        setTimeout(() => {
          setCommentsSubmitted(false);
        }, 3000);
      }
    } catch (error) {
      console.log("Error submitting ratings with comments:", error);
    } finally {
      setSubmittingRating(false);
    }
  };

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        if (scrollViewRef.current) {
          setTimeout(() => {
            scrollViewRef.current.scrollToEnd({ animated: true });
          }, 10);
        }
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        console.log("Keyboard hidden");
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const handleInputFocus = () => {
    if (scrollViewRef.current) {
      setTimeout(() => {
        scrollViewRef.current.scrollToEnd({ animated: true });
      }, 100);
    }
  };

  const onRejoinCall = async () => {
    try {
      const consultationId = route.params.consultationId;
      const url = `/consultation/rejoin-request/${consultationId}`;
      const response = await axiosConfig.get(url);
      const { nurseSDKJWT } = response.data;
      router.push({
        pathname: `/nurse/call`,
        params: {
          consultationId: consultationId,
          sdkId: nurseSDKJWT,
          rejoin: "true",
        },
      });
    } catch (error) {}
  };

  if (loading) {
    return (
      <View {...calloverViewStyles.spinner}>
        <Spinner size="large" />
      </View>
    );
  }

  return (
    <View {...calloverViewStyles.screenContainer}>
      <YStack {...calloverViewStyles.mainContainer}>
        <View {...calloverViewStyles.headerContainer}>
          <Text {...calloverViewStyles.headerText}>Call Overview</Text>
          <ThemeToggle />
        </View>
        <View
          style={{
            alignItems: "flex-start",
            display: "flex",
          }}
        >
          <TouchableOpacity
            style={{ flexDirection: "row", alignItems: "center", gap: 8 }}
            onPress={() => {
              onRejoinCall();
            }}
          >
            <Phone size={18} style={{ margin: 0, padding: 0 }} color="blue" />
            <Text {...calloverViewStyles.rejoinText}>Rejoin Call</Text>
          </TouchableOpacity>
        </View>

        <KeyboardAwareScrollView
          innerRef={(ref) => (scrollViewRef.current = ref)}
          contentContainerStyle={{ flexGrow: 1 }}
          enableOnAndroid
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          {consultation && (
            <View>
              <Consultation
                data={consultation}
                isFromCallScreen={false}
                isFromProvider={false}
                isFromCallOverViewScreen={true}
                onFinishLaterPress={onFinishLaterPress}
                onClosePress={onClosePress}
              />
            </View>
          )}
          <View {...calloverViewStyles.feedbackContainer}>
            <Text {...calloverViewStyles.feedbackText}>Feedback</Text>
            {commentsSubmitted && (
              <Text {...calloverViewStyles.successText}>
                ✓ Comment submitted successfully!
              </Text>
            )}
            <View {...calloverViewStyles.divider}></View>
            <RatingModule
              title="Overall Experience"
              onRatingChange={(rating) => setOverallExperienceRating(rating)}
              onAutoSave={autoSaveOverallRating}
              isEditable={isEditable}
              rating={overallExperienceRating}
              isSubmitting={submittingOverallRating}
              isSubmitted={overallRatingSubmitted}
            />
            <RatingModule
              title="Quality of call"
              onRatingChange={(rating) => setQualityOfCallRating(rating)}
              onAutoSave={autoSaveQualityRating}
              isEditable={isEditable}
              rating={qualityOfCallRating}
              isSubmitting={submittingQualityRating}
              isSubmitted={qualityRatingSubmitted}
            />
            <RatingModule
              title="Provider Rating"
              onRatingChange={(rating) => setProviderRating(rating)}
              onAutoSave={autoSaveProviderRating}
              isEditable={isEditable}
              rating={providerRating}
              isSubmitting={submittingProviderRating}
              isSubmitted={providerRatingSubmitted}
            />
            <Text {...calloverViewStyles.otherText}>Other</Text>
            <XStack {...calloverViewStyles.otherInputContainer} flex={1}>
              <TextArea
                ref={inputRef}
                {...calloverViewStyles.complaintTextArea}
                flex={1}
                onFocus={handleInputFocus}
                onChangeText={setOtherFeedback}
                disabled={!isEditable}
                value={otherFeedback}
              />
              <Button {...calloverViewStyles.saveButton}>
                <Save
                  size={"$1"}
                  color={"$confirmOrderTextColor"}
                  onPress={submitComments}
                />
              </Button>
            </XStack>
          </View>
        </KeyboardAwareScrollView>
      </YStack>
      <Modal visible={dialogOpen} transparent animationType="fade">
        <View
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            flex: 1,
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            height: "100%",
            width: "100%",
          }}
        >
          <DialogBox
            open={dialogOpen}
            onClose={setDialogOpen}
            title="UnConfirmed Order"
            body="Some text here about needing to confirm order within a certain time or cant make other requests"
            btnText="Finish Later"
            onFinishLater={onFinishLaterDialog}
          />
        </View>
      </Modal>
    </View>
  );
};

export default CalloverView;

const calloverViewStyles = {
  screenContainer: {
    backgroundColor: "$screenBackgroundcolor",
    flex: 1,
    paddingBottom: 40,
  },
  spinner: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "$screenBackgroundcolor",
  },
  mainContainer: {
    paddingBlock: 20,
    paddingInline: 20,
    backgroundColor: "$screenBackgroundcolor",
    maxWidth: 800,
    alignSelf: "center" as "center",
    width: "100%" as "100%",
  },
  headerContainer: {
    backgroundColor: "$screenBackgroundcolor",
    flexDirection: "row" as "row",
    justifyContent: "space-between",
  },
  rejoinText: {
    fontSize: 16,
    fontWeight: 600 as any,
    color: "$primaryColor" as any,
    textAlign: "center" as "center",
    paddingVertical: 10,
    alignSelf: "center" as "center",
  },
  headerText: {
    fontWeight: 600 as any,
    fontSize: 20 as any,
  },
  feedbackContainer: {
    marginBlockStart: 20,
    backgroundColor: "$screenBackgroundcolor" as any,
    borderWidth: 1,
    borderColor: "$primaryBorderColor" as any,
    borderRadius: 10,
    padding: 20,
    marginBlockEnd: 30,
  },
  feedbackText: {
    fontSize: 16,
    fontWeight: 600 as any,
    color: "$textcolor" as any,
    marginBlockStart: 10,
    marginBlockEnd: 15,
  },
  successText: {
    fontSize: 14,
    fontWeight: 500 as any,
    color: "#10B981" as any,
    marginBlockEnd: 10,
  },
  divider: {
    height: 1,
    backgroundColor: "$primaryBorderColor",
  },
  otherText: {
    marginBlockStart: 10,
    marginBlockEnd: 15,
    fontSize: 14,
    fontWeight: 500 as any,
    color: "$textcolor" as any,
  },
  otherInput: {
    placeholder: "Other feedback comments",
    placeholderTextColor: "$textcolor" as any,
    numberOfLines: 1 as any,
    backgroundColor: "$screenBackgroundcolor" as any,
    borderColor: "$primaryBorderColor" as any,
    borderWidth: 1,
  },
  otherInputContainer: {
    justifyContent: "space-between",
    alignItems: "center",
  },
  saveButton: {
    borderWidth: 1,
    padding: 7,
    fontSize: 16,
    size: "$4" as any,
    fontWeight: "600" as any,
    backgroundColor: "$confirmOrderBlue",
    borderColor: "$confirmOrderBorderCOlor" as any,
    color: "$confirmOrderTextColor" as any,
  },
  complaintTextArea: {
    placeholder: "Other feedback comments",
    backgroundColor: "$screenBackgroundcolor" as any,
    borderColor: "$primaryBorderColor" as any,
    size: "$1" as "$1",
    bordeWidth: 1,
    borderRadius: 7,
    color: "$textcolor" as any,
    fontWeight: 500 as any,
    padding: 10,
    fontSize: 16,
    numberOfLines: 4,
    textAlignVertical: "top" as any,
    marginInlineEnd: 10,
    placeholderTextColor: "$textcolor",
  },
};
