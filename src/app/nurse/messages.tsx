import React, { useState } from "react";
import { FlatList, Pressable } from "react-native";
import { YStack, Text, View, XStack, Avatar } from "tamagui";
import { useRouter } from "expo-router";
import { useAlertsContext, Alert } from "src/context/AlertsContext";
import ScreenHeader from "src/components/ScreenHeader";
import SheetDemo from "src/components/SettingsDrawer";
import { useMessagesStyle } from "./Styles/MessagesStyle";
import { HorizontalDashedLine } from "src/components/DashedLine";
import { useAuth } from "~/context/AuthContext";
import { getTimeDifference } from "src/utils/timeUtils";

const MessageList = () => {
  const router = useRouter();
  const { alerts, loading, error, refreshAlerts } = useAlertsContext();
  const [open, setOpen] = useState(false);
  const openSettings = () => setOpen(true);
  const navigateBack = () => router.back();
  const { user } = useAuth();
  const style = useMessagesStyle();
  if (error) {
    return (
      <View {...style.mainContainer}>
        <YStack {...style.container}>
          <ScreenHeader
            onAvatarPress={openSettings}
            screenName="Messages"
            onBackPress={navigateBack}
          />
          <YStack
            style={{
              alignItems: "center",
              justifyContent: "center",
              padding: 20,
            }}
          >
            <Text style={{ fontSize: 16, color: "red" }}>
              Error loading messages.
            </Text>
          </YStack>
          {open && <SheetDemo open={open} setOpen={setOpen} />}
        </YStack>
      </View>
    );
  }

  const renderItem = ({ item, index }: { item: Alert; index: number }) => {
    const { lastMessage } = item;
    const dateTime = getTimeDifference(lastMessage.sent_at);
    const initials = lastMessage?.provider_name
      ? lastMessage.provider_name
          .split(" ")
          .map((word) => word.charAt(0))
          .join("")
      : "N/A";
    const unRead = lastMessage?.user?.id !== user?.id && !lastMessage?.read;
    const isLastItem = index === (alerts?.length || 0) - 1;
    return (
      <Pressable
        onPress={() => {
          router.push({
            pathname: "/nurse/chat",
            params: { consultationId: lastMessage.consultation_id },
          });
        }}
      >
        <XStack
          {...style.messageContainer}
          paddingInlineStart={10}
          paddingBlock={5}
        >
          <View position="relative">
            <Avatar {...style.avatarContainer}>
              <Avatar.Fallback
                style={{
                  backgroundColor: "#B2DDFF",
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                }}
              >
                <Text {...style.initials}>{initials}</Text>
              </Avatar.Fallback>
            </Avatar>
            {unRead && (
              <View
                style={{
                  position: "absolute",
                  top: 2,
                  right: 2,
                  width: 12,
                  height: 12,
                  borderRadius: 6,
                  backgroundColor: "red",
                }}
              />
            )}
          </View>
          <YStack
            style={{
              padding: 10,
              flex: 1,
            }}
          >
            <XStack {...style.header} maxW={"95%"}>
              <Text
                {...style.messageTitle}
                numberOfLines={1}
                maxW={"75%"}
                fontWeight={unRead ? "800" : "500"}
              >
                {lastMessage?.provider_name + " - " + lastMessage?.patient_name}
              </Text>
              <Text {...style.dateText} fontWeight={unRead ? "800" : "500"}>
                {dateTime}
              </Text>
            </XStack>
            <XStack maxW={"100%"}>
              <Text
                {...style.messageText}
                numberOfLines={2}
                ellipsizeMode="tail"
                fontWeight={unRead ? "600" : "400"}
              >
                {lastMessage.message}
              </Text>
            </XStack>
          </YStack>
        </XStack>
        {!isLastItem && <HorizontalDashedLine />}
      </Pressable>
    );
  };

  return (
    <View {...style.mainContainer}>
      <YStack {...style.container}>
        <ScreenHeader
          onAvatarPress={openSettings}
          screenName="Messages"
          onBackPress={navigateBack}
        />

        <YStack flex={1} marginBlockStart={20}>
          <FlatList
            data={alerts || []}
            keyExtractor={(item) => item.consultationId}
            renderItem={renderItem}
            showsVerticalScrollIndicator={false}
            onRefresh={refreshAlerts}
            refreshing={loading}
            ListEmptyComponent={
              <YStack
                style={{
                  alignItems: "center",
                  justifyContent: "center",
                  padding: 20,
                }}
              >
                <Text style={{ fontSize: 16, color: "#000" }}>
                  No unread messages.
                </Text>
              </YStack>
            }
            style={{ flex: 1 }}
          />
        </YStack>

        {open && <SheetDemo open={open} setOpen={setOpen} />}
      </YStack>
    </View>
  );
};

export default MessageList;
