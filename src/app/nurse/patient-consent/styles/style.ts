export const useStyles = () => {
  return {
    container: {
      flex: 1,
      backgroundColor: "$screenBackgroundcolor",
    },
    mainStack: {
      marginBlock: 20,
      marginInline: 20,
      backgroundColor: "$screenBackgroundcolor",
      flex: 1,
    },
    patinetCountText: {
      marginBlockStart: 30,
      marginBlockEnd: 20,
      color: "$textcolor" as any,
      fontSize: 14,
      fontWeight: 600 as any,
    },
    telehealthConsentText: {
      marginBlockEnd: 20,
      color: "$textcolor" as any,
      fontSize: 20,
      fontWeight: 600 as any,
    },
    patientCard: {
      borderWidth: 1,
      borderColor: "$primaryBorderColor" as any,
      borderRadius: 12,
      backgroundColor: "$screenBackgroundcolor" as any,
      padding: 15,
      marginBlockEnd: 15,
    },
    patientCardTitle: {
      fontSize: 16,
      fontWeight: 600 as any,
      color: "$textcolor" as any,
    },
    patientCardSubTitle: {
      fontSize: 14,
      fontWeight: 400 as any,
      color: "$textcolor" as any,
    },
    complaintText: {
      fontSize: 14,
      fontWeight: 500 as any,
      color: "$textcolor" as any,
      marginBlockStart: 10,
      marginBlockEnd: 10,
    },
    complaintTextArea: {
      backgroundColor: "$screenBackgroundcolor" as any,
      borderColor: "$primaryBorderColor" as any,
      size: "$1" as "$1",
      borderWidth: 1,
      borderRadius: 7,
      color: "$textcolor" as any,
      fontWeight: 500 as any,
      padding: 10,
      fontSize: 16,
      numberOfLines: 8,
      textAlignVertical: "top" as any,
    },
    nextBtnContainer: {
      marginBlock: 20,
      marginInline: 20,
    },
    nextBtnDisabled: {
      backgroundColor: "$disableButtonPrimaryColor" as any,
      borderColor: "$disableButtonPrimaryColor" as any,
      borderRadius: 8,
      color: "$buttonWhiteColor" as any,
      fontWeight: "600" as any,
      size: "$4" as any,
      fontSize: 16,
    },
    nextBtn: {
      backgroundColor: "$primaryColor" as any,
      borderColor: "$primaryColor" as any,
      borderRadius: 8,
      color: "$buttonWhiteColor" as any,
      fontWeight: "600" as any,
      size: "$4" as any,
      fontSize: 16,
    },
    attachmentsText: {
      fontSize: 14,
      fontWeight: 500 as any,
      color: "$textcolor" as any,
      marginBlockStart: 20,
      marginBlockEnd: 5,
    },
    telehealthConsentTextContainer: {
      marginBlock: 20,
      maxHeight: 400,
      borderWidth: 1,
      borderColor: "$primaryBorderColor" as any,
      borderRadius: "$5" as any,
      alignItems: "center" as any,
      justifyContent: "flex-start" as any,
      paddingHorizontal: 15,
      paddingVertical: 10,
      overflow: "scroll" as any,
      flexShrink: 1,
    },
  };
};
