export const useDashboardStyles = () => {
  return {
    container: { flex: 1, backgroundColor: "$screenBackgroundcolor" },
    mainStack: {
      marginInline: 15,
      marginBlockStart: 10,
      flex: 1,
      height: "100%" as "100%",
    },
    requestButton: {
      backgroundColor: "$primaryColor",
      color: "$buttonWhiteColor" as any,
      fontSize: 16,
      size: "$4" as any,
      fontWeight: "600" as any,
      marginBlockStart: 15,
    },
    requestIcon: { width: 20, height: 20 },
    consultationTitle: {
      fontSize: 20,
      fontWeight: "600" as any,
      marginBlockStart: 30,
      color: "$textcolor" as any,
    },
    searchContainer: { marginBlockStart: 15 },
    todayLabel: {
      fontSize: 14,
      fontWeight: "600" as "600",
      marginBlockStart: 25,
      color: "$textcolor" as any,
    },
    scrollContainer: { flexGrow: 1, paddingBottom: 20 },
    dialogContainer: {
      gap: "$2" as any,
    },
    dialogContent: {
      bordered: true,
      bg: "$screenBackgroundcolor" as any,
      br: "$4" as any,
      shadowColor: "transparent" as any,
      shadowOpacity: 0 as any,
      shadowRadius: 0 as any,
      width: "90%" as any,
      alignSelf: "center" as any,
      justifyContent: "center" as any,
      borderRadius: "$7" as any,
      position: "absolute" as any,
      top: "50%" as any,
      left: "50%" as any,
      transform: [{ translateX: "-50%" }, { translateY: "-50%" }] as any,
      borderColor: "$primaryBorderColor" as any,
    },
    overlay: {
      animation: "quick" as any,
      enterStyle: { opacity: 0 },
      exitStyle: { opacity: 0 },
      backgroundColor: "black" as any,
      opacity: 0.6,
    },
    headerContainer: {
      justifyContent: "space-between",
    },
    headerText: {
      fontSize: 24,
      fontWeight: "600" as any,
    },
    dialogCardContainer: {
      marginBlockStart: 50,
      justifyContent: "space-between",
      alignItems: "center",
      gap: "$2" as any,
    },
    card: {
      width: "48%",
      height: "200%",
    },
    cardBody: {
      backgroundColor: "$selectCardGrayBackground",
      borderRadius: 12,
      alignItems: "center",
      justifyContent: "center",
      flex: 1,
      borderWidth: 1,
      borderColor: "$selectCardBorderColor" as any,
    },
    labeltext: {
      color: "Black" as any,
      fontSize: 14,
      fontWeight: 500 as any,
      marginBlockStart: 10,
    },
    icon: {
      size: "$2",
      color: "black",
    },
    cancelBtn: {
      backgroundColor: "transparent",
      borderRadius: 7,
      borderWidth: 1,
      borderColor: "$primaryBorderColor" as any,
      marginBlockStart: 50,
      color: "$textcolor" as any,
    },
  };
};
