export const useTelehealthConsentStyle = () => {
  return {
    container: { flex: 1, backgroundColor: "$screenBackgroundcolor" },
    mainStack: { flex: 1, marginBlock: 20, marginInline: 20 },
    agreeAndConnectBtn: {
      backgroundColor: "$primaryColor" as any,
      color: "$buttonWhiteColor" as any,
      fontWeight: "600" as any,
      marginBlockStart: 10,
      fontSize: 16,
    },
    cancelBtn: {
      backgroundColor: "transparent" as any,
      color: "$textcolor" as any,
      fontWeight: "600" as any,
      marginBlockStart: 10,
      fontSize: 16,
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 1,
    },
    agreeAndConnectBtnContainer: {
      marginBlock: 10,
      marginInline: 20,
    },
    headerTextContainer: {
      marginBlock: 20,
    },
    headerText: {
      fontSize: 20,
      fontWeight: 600 as any,
    },
    consentContainer: {
      flex: 1,
      borderWidth: 1,
      borderColor: "$primaryBorderColor" as any,
      borderRadius: "$5" as any,
      alignItems: "center" as any,
      justifyContent: "flex-start" as any,
      paddingHorizontal: 15,
      paddingVertical: 10,
      overflow: "scroll" as any,
      flexShrink: 1,
    },
    patientCard: {
      borderWidth: 1,
      borderColor: "$primaryBorderColor" as any,
      borderRadius: 12,
      backgroundColor: "$screenBackgroundcolor" as any,
      padding: 15,
      marginBlockEnd: 15,
    },
    patientCardTitle: {
      fontSize: 16,
      fontWeight: 600 as any,
      color: "$textcolor" as any,
    },
    patientCardSubTitle: {
      fontSize: 14,
      fontWeight: 400 as any,
      color: "$textcolor" as any,
    },
    patientsText: {
      fontSize: 14,
      fontWeight: 600 as any,
      color: "$textcolor" as any,
      marginBlockEnd: 20,
    },
    patientsContainer: {
      marginBlockStart: 25,
      marginBlockEnd: 10,
      flex: 1,
    },
  };
};
