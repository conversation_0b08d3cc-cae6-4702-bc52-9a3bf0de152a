export const useTelehealthConsentStyle = () => {
  return {
    container: { flex: 1, backgroundColor: "$screenBackgroundcolor" },
    mainStack: { flex: 1, marginBlock: 20, marginInline: 20 },
    agreeAndConnectBtn: {
      backgroundColor: "$primaryColor" as any,
      color: "$buttonWhiteColor" as any,
      fontWeight: "600" as any,
      marginBlockStart: 10,
      fontSize: 16,
    },
    agreeAndConnectBtnContainer: {
      marginBlock: 10,
      marginInline: 20,
    },
    headerTextContainer: {
      marginBlock: 20,
    },
    headerText: {
      fontSize: 20,
      fontWeight: 600 as any,
    },
    consentContainer: {
      flex: 1,
      borderWidth: 1,
      borderColor: "$primaryBorderColor" as any,
      borderRadius: "$5" as any,
      alignItems: "center" as any,
      justifyContent: "flex-start" as any,
      paddingHorizontal: 15,
      paddingVertical: 10,
      overflow: "scroll" as any,
      flexShrink: 1,
    },
  };
};
