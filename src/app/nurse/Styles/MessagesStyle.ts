export const useMessagesStyle = () => {
  return {
    mainContainer: { flex: 1, backgroundColor: "$screenBackgroundcolor" },
    container: {
      flex: 1,
      marginBlock: 20,
      marginInline: 20,
    },
    mainStack: { flex: 1, marginBlockStart: "$1" as any },
    messageTitle: {
      fontSize: 14,
      color: "$textcolor" as any,
    },
    dateText: { fontSize: 14, color: "$textcolor" as any },
    messageText: {
      fontSize: 14,
      color: "$textcolor" as any,
      marginInlineEnd: 10,
    },
    avatarContainer: {
      circular: true,
      size: "$4" as const,
      border: "$primaryBorderColor",
      borderWidth: 0.4,
    },
    messageContainer: {
      alignItems: "center" as any,
      space: "$2" as any,
      flex: 1,
    },
    header: {
      justifyContent: "space-between" as any,
      alignItems: "space-between" as any,
    },
    initials: {
      color: "black" as any,
      fontSize: 18,
    },
  };
};
