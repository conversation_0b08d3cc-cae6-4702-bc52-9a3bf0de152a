import React, { useEffect } from "react";
import { FlatList } from "react-native";
import { Consultation } from "src/components/Consultation";
import { useConsultations } from "src/hooks/useConsultations";
import { Spinner, Text, YStack } from "tamagui";
import { useDashboardStyles } from "./Styles/DashboardStyle";
import { useSocket } from "~/context/NurseSocketContext";

const Consultations = ({ searchQuery }: { searchQuery: string }) => {
  const {
    consultations,
    loading,
    error,
    loadMore,
    hasMore,
    refreshConsultations,
    updateConsultationOrder,
  } = useConsultations(searchQuery);
  const { mainStack, scrollContainer } = useDashboardStyles();

  const pendingConsultations = React.useMemo(
    () => consultations?.pending?.data || [],
    [consultations]
  );

  const { socket } = useSocket();

  useEffect(() => {
    if (!socket) return;

    const handleOrderSubmitted = ({
      order,
      consultationId,
    }: {
      order: string;
      consultationId: string;
    }) => {
      if (!consultations) return;
      updateConsultationOrder(order, consultationId);
    };

    socket.on("orderSubmitted", handleOrderSubmitted);

    return () => {
      socket.off("orderSubmitted", handleOrderSubmitted);
    };
  }, [socket]);

  if (loading && !consultations) {
    return (
      <YStack {...ConsultationStyle.spinner}>
        <Spinner size="large" />
      </YStack>
    );
  }

  return (
    <FlatList
      data={pendingConsultations}
      keyExtractor={(item) => item.id.toString()}
      renderItem={({ item }) => (
        <Consultation
          data={item}
          isFromProvider={false}
          isFromCallScreen={false}
          shouldShowDay={true}
        />
      )}
      contentContainerStyle={scrollContainer}
      showsVerticalScrollIndicator={false}
      onEndReached={hasMore ? loadMore : null}
      onEndReachedThreshold={0.5}
      ListFooterComponent={
        hasMore && pendingConsultations.length > 0 ? (
          <Spinner size="large" style={{ marginBlockStart: 10 }} />
        ) : null
      }
      ListEmptyComponent={
        !loading && pendingConsultations.length === 0 ? (
          <YStack {...ConsultationStyle.spinner}>
            <Text style={ConsultationStyle.noConsultationsText}>
              No Consultations Available.
            </Text>
          </YStack>
        ) : null
      }
      refreshing={loading} // Pull-to-refresh state
      onRefresh={refreshConsultations} // Fetch data again when user pulls down
    />
  );
};

export default Consultations;

const ConsultationStyle = {
  spinner: {
    justifyContent: "center",
    alignItems: "center",
    flex: 1,
    color: "red",
  },
  noConsultationsText: {
    justifyContent: "center",
    alignItems: "center",
    color: "$textcolor",
    fontSize: 20,
    fontWeight: "600",
  },
};
