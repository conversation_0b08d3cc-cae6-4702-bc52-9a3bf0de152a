import { ArrowRight } from "@tamagui/lucide-icons";
import { useRouter } from "expo-router";
import { requestAndUpdatePermissions } from "lib";
import { useEffect, useRef, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Keyboard,
  KeyboardAvoidingView,
  NativeSyntheticEvent,
  Platform,
  ScrollView,
  TextInputFocusEventData,
} from "react-native";
import {
  check,
  openSettings,
  Permission,
  PERMISSIONS,
  request,
  RESULTS,
} from "react-native-permissions";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import AddDocuments from "src/components/AddDocuments";
import FacilityDrawer from "src/components/FacilityDrawer";
import ImageUploaderSheet from "src/components/ImageUploadersheet";
import PatientSearchDrawer from "src/components/PatientSearchDrawer";
import ScreenHeader from "src/components/ScreenHeader";
import SheetDemo from "src/components/SettingsDrawer";
import Title from "src/components/Title";
import { useFacilities } from "src/hooks/useFacilities";
import { usePatients } from "src/hooks/usePatientsByFacilities";
import { uploadToS3 } from "src/utils/s3Upload";
import { Avatar, Button, Card, Text, TextArea, View, YStack } from "tamagui";
import { useAuth } from "~/context/AuthContext";
import axiosConfig from "~/services/axiosConfig";
import ScanDocumentComponent from "../../components/ScanDocument";
import { useRequestNewVisitStyle } from "./Styles/RequestNewVisitStyle";
const getPermissionStatus = async (
  permission: Permission,
  label: string
): Promise<boolean> => {
  const status = await check(permission);

  if (status === RESULTS.GRANTED) {
    return true;
  }

  if (status === RESULTS.DENIED) {
    const result = await request(permission);
    return result === RESULTS.GRANTED;
  }

  if (status === RESULTS.BLOCKED) {
    Alert.alert(
      `${label} Permission Needed`,
      `To proceed, enable ${label.toLowerCase()} access in your device settings.`,
      [
        { text: "Cancel", style: "cancel" },
        { text: "Open Settings", onPress: () => openSettings() },
      ]
    );
    return false;
  }

  return false;
};

interface TelehealthConsentFileInfo {
  fileKeys?: string[];
  fileNames?: string[];
  fileTypes?: string[];
}

export default function RequestNewVisit() {
  const { user, signOut } = useAuth();
  const ehrType = user?.ehr || "";
  const initials = user?.firstName
    ? user?.firstName.charAt(0).toUpperCase() +
      user?.lastName.charAt(0).toUpperCase()
    : "User";
  const insets = useSafeAreaInsets();
  const screenHeight = Dimensions.get("window").height;
  const contentHeight = screenHeight - (insets.top + insets.bottom);
  const transcriptStyles = useRequestNewVisitStyle(contentHeight);
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const openSettings = () => setOpen(true);
  const navigateBack = () => router.back();
  const [patientNameSearch, setPatientNameSearch] = useState<string>("Al");
  const scrollViewRef = useRef<ScrollView>(null);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const textAreaRef = useRef<any>(null);
  const [patientsCount, setPatientsCount] = useState(0);
  const [showError, setShowError] = useState<string>("");
  const [showUploader, setShowUploader] = useState(false);
  const [showScanDocument, setShowScanDocument] = useState(false);
  const [scannedImages, setScannedImages] = useState<string[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadSuccess, setUploadSuccess] = useState(false);

  const {
    data: facilities,
    isLoading: facilitiesLoading,
    error: facilitiesError,
  } = useFacilities();

  const [openDrawer, setOpenDrawer] = useState<"facility" | "patient" | null>(
    null
  );

  const [selectedFacilityId, setSelectedFacilityId] = useState<string | null>(
    null
  );
  const [selectedPatientId, setSelectedPatientId] = useState<string | null>(
    null
  );
  const [selectedPatientFirstName, setSelectedPatientFirstName] =
    useState<string>("");
  const [selectedDOB, setSelectedDOB] = useState<string>("");
  const [reason, setReason] = useState<string>("");

  const {
    data: patients,
    isLoading: patientsLoading,
    error: patientsError,
  } = usePatients(selectedFacilityId || "", patientNameSearch);

  const { localUser } = useAuth();
  const handleFacilitySelect = (facilityId: string) => {
    setSelectedFacilityId(facilityId);
  };

  const handlePatientSelect = (
    patientId: string,
    patientFirstName: string,
    dob: string
  ) => {
    setSelectedPatientId(patientId);
    setSelectedPatientFirstName(patientFirstName);
    setSelectedDOB(dob);
  };

  const handlePatientConsent = async () => {
    const isManualEHR = ehrType === "manual";
    const isValid = isManualEHR
      ? !!selectedFacilityId &&
        !!selectedPatientFirstName &&
        !!selectedDOB &&
        !!reason
      : !!selectedPatientId &&
        !!selectedFacilityId &&
        !!reason &&
        !!selectedPatientFirstName;
    console.log("isValid:", isValid);

    let message = "";
    if (!selectedFacilityId) {
      message = "facility";
    } else if (!selectedPatientFirstName) {
      message = "patient name";
    } else if (!selectedDOB && isManualEHR) {
      message = "date of birth";
    } else if (!reason) {
      message = "chief complaint";
    }
    if (message.length === 0) message = "";

    if (!isValid) {
      setShowError(message);
      return;
    }
    setShowError("");
    setUploading(true);
    setUploadSuccess(false);

    try {
      const fileKeys: string[] = [];
      const fileNames: string[] = [];
      const fileTypes: string[] = [];
      for (let i = 0; i < scannedImages.length; i++) {
        const uri = scannedImages[i];
        const fileType = "image/jpeg";
        const fileName = `photo-${Date.now()}-${i}.jpg`;
        const response = await axiosConfig.get(
          `/user/upload-url?fileName=${encodeURIComponent(fileName)}&contentType=${encodeURIComponent(fileType)}&folder=consultations/documents`
        );
        console.log("Upload URL response:", response.data);
        console.log("Upload URL status:", response.status);

        if (response.status !== 200) {
          throw new Error("Failed to get S3 upload URL");
        }

        const { uploadUrl, key, fileUrl } = response.data;
        await uploadToS3(uploadUrl, uri, fileType);

        fileKeys.push(key);
        fileNames.push(fileName);
        fileTypes.push(fileType);
      }
      console.log("File keys:", fileKeys);
      console.log("File names:", fileNames);
      console.log("File types:", fileTypes);
      setUploading(false);
      setUploadSuccess(true);
      telehealthConsent({ fileKeys, fileNames, fileTypes });
    } catch (err) {
      setUploading(false);
      console.log(err);
      return;
    }
  };

  const telehealthConsent = async ({
    fileKeys = [],
    fileNames = [],
    fileTypes = [],
  }: TelehealthConsentFileInfo) => {
    const cameraPermission =
      Platform.OS === "ios"
        ? PERMISSIONS.IOS.CAMERA
        : PERMISSIONS.ANDROID.CAMERA;
    const micPermission =
      Platform.OS === "ios"
        ? PERMISSIONS.IOS.MICROPHONE
        : PERMISSIONS.ANDROID.RECORD_AUDIO;

    const hasCamera = await getPermissionStatus(cameraPermission, "Camera");
    const hasMic = await getPermissionStatus(micPermission, "Microphone");

    if (hasCamera && hasMic) {
      router.push({
        pathname: "/nurse/telehealthconsent",
        params: {
          patientId: selectedPatientId,
          facilityId: selectedFacilityId,
          reason,
          patientFirstName: selectedPatientFirstName,
          patientDOB: selectedDOB,
          fileKeys: JSON.stringify(fileKeys),
          fileNames: JSON.stringify(fileNames),
          fileTypes: JSON.stringify(fileTypes),
        },
      });
    } else {
      Alert.alert(
        "Permissions Required",
        "Please allow both camera and microphone access to continue."
      );
    }
  };

  useEffect(() => {
    console.log("Requesting permissions on app start");
    requestAndUpdatePermissions();
  }, []);

  useEffect(() => {
    if (patients?.length) {
      setPatientsCount(patients.length);
    }
  }, [patients]);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      "keyboardDidShow",
      () => {
        setKeyboardVisible(true);
        if (scrollViewRef.current) {
          scrollViewRef.current.scrollToEnd({ animated: true });
        }
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      "keyboardDidHide",
      () => {
        setKeyboardVisible(false);
      }
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  const handleTextAreaFocus = (
    e: NativeSyntheticEvent<TextInputFocusEventData>
  ) => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollToEnd({ animated: true });
    }
  };

  const handleDrawerOpen = (type: "facility" | "patient") => {
    setOpenDrawer(type);
    // For facility drawer, always add space
    if (type === "facility") {
      setTimeout(() => {
        scrollViewRef.current?.scrollToEnd({ animated: true });
      }, 100);
    }
  };

  // Calculate extra space needed for patient dropdown
  const getExtraSpace = () => {
    if (openDrawer === "patient" && patientsCount >= 3) {
      // Each patient item is approximately 50px height, plus some padding
      return Math.min(patientsCount * 22, 110); // Cap at 300px to prevent too much space
    }
    return 0;
  };

  const formatDOB = (raw: string) => {
    const digits = raw.replace(/\D/g, "").slice(0, 8);
    if (digits.length <= 2) return digits;
    if (digits.length <= 4) {
      return `${digits.slice(0, 2)}-${digits.slice(2)}`;
    }
    return `${digits.slice(0, 2)}-${digits.slice(2, 4)}-${digits.slice(4)}`;
  };

  useEffect(() => {
    if (showError) {
      const timer = setTimeout(() => {
        setShowError("");
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [showError]);

  const error = () => {
    if (showError) {
      if (showError.startsWith("You can only select")) {
        return (
          <YStack
            style={{
              padding: 10,
              backgroundColor: "rgb(251, 85, 85)",
              borderRadius: 5,
              margin: 10,
              position: "absolute",
              bottom: 50,
              left: 0,
              right: 0,
              zIndex: 1000,
            }}
          >
            <Text style={{ color: "white" }}>{showError}</Text>
          </YStack>
        );
      }
      return (
        <YStack
          style={{
            padding: 10,
            backgroundColor: "rgb(251, 85, 85)",
            borderRadius: 5,
            margin: 10,
            position: "absolute",
            bottom: 50,
            left: 0,
            right: 0,
            zIndex: 1000,
          }}
        >
          <Text style={{ color: "white" }}>
            Please fill in the {showError}.
          </Text>
        </YStack>
      );
    }
    return null;
  };

  const handleOpenScanDocument = () => {
    setShowUploader(false);
    setShowScanDocument(true);
  };

  const handleCloseScanDocument = (images: string[]) => {
    setShowScanDocument(false);
    setScannedImages(images);
  };

  const handleRemoveImage = (index: number) => {
    setScannedImages((prev) => prev.filter((_, i) => i !== index));
  };

  const handleAddDocument = () => {
    if (scannedImages.length >= 3) {
      setShowError("You can only select up to 3 images.");
      return;
    }
    setShowUploader(true);
  };

  const handleAddGalleryImage = (uri: string) => {
    if (scannedImages.length < 3) {
      setScannedImages((prev) => [...prev, uri]);
    } else {
      setShowError("You can only select up to 3 images.");
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
      keyboardVerticalOffset={Platform.OS === "ios" ? 50 : 0}
    >
      <View {...transcriptStyles.screenParent}>
        {uploading && (
          <View
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(0,0,0,0.3)",
              zIndex: 9999,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <ActivityIndicator size="large" color="blue" />
            <Text style={{ color: "blue", marginTop: 16, fontSize: 18 }}>
              Uploading images...
            </Text>
          </View>
        )}
        <View {...transcriptStyles.container}>
          <ScreenHeader
            onAvatarPress={openSettings}
            screenName="Back"
            onBackPress={navigateBack}
          />
          <View {...transcriptStyles.mainStack}>
            <ScrollView
              ref={scrollViewRef}
              contentContainerStyle={{
                flexGrow: 1,
                paddingBottom: 5,
              }}
              keyboardShouldPersistTaps="handled"
              showsVerticalScrollIndicator={false}
            >
              <YStack {...transcriptStyles.profileContainer}>
                <Card {...transcriptStyles.profileCard}>
                  <YStack {...transcriptStyles.profileContent}>
                    <Avatar circular {...transcriptStyles.avatar}>
                      <Avatar.Fallback
                        style={{
                          backgroundColor: "#1570EF",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center",
                        }}
                      >
                        <Text style={{ color: "white" }}>{initials}</Text>
                      </Avatar.Fallback>
                    </Avatar>
                    <Text {...transcriptStyles.profileText}>
                      {localUser?.firstName + " " + localUser?.lastName}
                    </Text>
                    <YStack {...transcriptStyles.title}>
                      <Title
                        text="Nurse"
                        backgroundColor={"$nurseBadgeBackgroundColor"}
                        borderColor={"$primaryBorderColor"}
                      />
                    </YStack>
                    <YStack>
                      <Button
                        {...transcriptStyles.signOut}
                        onPress={() => signOut()}
                      >
                        Sign Out
                      </Button>
                    </YStack>
                    {/* Facility Selection */}
                    <YStack {...transcriptStyles.facilityContainer}>
                      <Text {...transcriptStyles.facilityTitle}>Facility</Text>
                      <YStack>
                        {facilitiesLoading ? (
                          <Text>Loading facilities...</Text>
                        ) : facilitiesError ? (
                          <Text>Error loading facilities</Text>
                        ) : (
                          <FacilityDrawer
                            data={facilities || []}
                            placeholder="Select a Facility"
                            onSelect={(id: string) => handleFacilitySelect(id)}
                            onOpen={() => setOpenDrawer("facility")}
                            onClose={() => setOpenDrawer(null)}
                            isOpen={openDrawer === "facility"}
                          />
                        )}
                      </YStack>
                    </YStack>
                    {/* Patient Selection */}
                    <YStack {...transcriptStyles.facilityContainer}>
                      {ehrType !== "manual" && (
                        <Text {...transcriptStyles.facilityTitle}>Patient</Text>
                      )}
                      {ehrType === "manual" ? (
                        <YStack width="100%">
                          <Text {...transcriptStyles.facilityTitle}>
                            Patient
                          </Text>
                          <TextArea
                            {...transcriptStyles.patientNameTextArea}
                            placeholder="Type Patient Name"
                            placeholderTextColor={"$textcolor"}
                            overflow="hidden"
                            value={selectedPatientFirstName}
                            onChangeText={(text) => {
                              setSelectedPatientId(text);
                              setSelectedPatientFirstName(text);
                            }}
                            // no scroll here
                            onFocus={() => {}}
                          />
                          <Text {...transcriptStyles.facilityTitle} mt={"$4"}>
                            Date of Birth (MM/DD/YYYY)
                          </Text>
                          <TextArea
                            {...transcriptStyles.patientNameTextArea}
                            placeholder="MM-DD-YYYY"
                            placeholderTextColor={"$textcolor"}
                            keyboardType={
                              Platform.OS === "ios" ? "number-pad" : "numeric"
                            }
                            maxLength={10}
                            value={selectedDOB}
                            onFocus={() => {}}
                            onChangeText={(text) => {
                              const dob = formatDOB(text);
                              setSelectedDOB(dob);
                            }}
                          />
                        </YStack>
                      ) : (
                        <YStack>
                          <PatientSearchDrawer
                            data={patients || []}
                            placeholder="Select a Patient"
                            onSelect={(
                              id: string,
                              firstName: string,
                              dob: string
                            ) => handlePatientSelect(id, firstName, dob)}
                            onOpen={() => handleDrawerOpen("patient")}
                            onClose={() => setOpenDrawer(null)}
                            isOpen={openDrawer === "patient"}
                            onSearch={(query: string) => {
                              setPatientNameSearch(query);
                              return Promise.resolve();
                            }}
                            disabled={!selectedFacilityId}
                            loading={patientsLoading}
                            error={patientsError?.message || ""}
                          />
                        </YStack>
                      )}
                    </YStack>
                  </YStack>
                  <YStack {...transcriptStyles.complaintContainer}>
                    <Text {...transcriptStyles.facilityTitle}>
                      Chief complaint
                    </Text>
                    <YStack flex={1}>
                      <TextArea
                        ref={textAreaRef}
                        {...transcriptStyles.complaintTextArea}
                        placeholder="Please enter the details."
                        placeholderTextColor={"$textcolor"}
                        overflow="hidden"
                        value={reason}
                        onChangeText={setReason}
                        onFocus={handleTextAreaFocus}
                        disabled={showUploader}
                      />
                    </YStack>
                  </YStack>
                  <YStack {...transcriptStyles.complaintContainer}>
                    <Text {...transcriptStyles.facilityTitle}>Attachments</Text>
                    <AddDocuments
                      onAddDocument={handleAddDocument}
                      scannedImages={scannedImages}
                      onRemoveImage={handleRemoveImage}
                    />
                  </YStack>
                </Card>
              </YStack>
              {getExtraSpace() > 0 && (
                <View style={{ height: getExtraSpace() }} />
              )}
            </ScrollView>
          </View>
          {error()}
          <View>
            <Button
              iconAfter={<ArrowRight size={"$1"} />}
              {...transcriptStyles.patientConsentButton}
              onPress={handlePatientConsent}
            >
              Patient Consent
            </Button>
          </View>
          {open && <SheetDemo open={open} setOpen={setOpen} />}
        </View>
        {showScanDocument && (
          <View
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 9999,
              backgroundColor: "#fff",
            }}
          >
            <ScanDocumentComponent
              open={showScanDocument}
              initialImages={scannedImages}
              onClose={handleCloseScanDocument}
              maxImages={3}
            />
          </View>
        )}
        {!showScanDocument && (
          <ImageUploaderSheet
            open={showUploader}
            setOpen={setShowUploader}
            onOpenScanDocument={handleOpenScanDocument}
            onAddImage={handleAddGalleryImage}
          />
        )}
      </View>
    </KeyboardAvoidingView>
  );
}
