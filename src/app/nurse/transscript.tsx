import { MessageCircle } from "@tamagui/lucide-icons";
import { useLocalSearchParams, useRouter } from "expo-router";
import { useState } from "react";
import { FlatList } from "react-native";
import IconFirstButton from "src/components/IconfirstButton";
import ScreenHeader from "src/components/ScreenHeader";
import SheetDemo from "src/components/SettingsDrawer";
import TranscriptMessage from "src/components/TranscriptMessage";
import { View, YStack } from "tamagui";
import { useTranscriptStyles } from "./Styles/TranscriptStyle";

const messages = [
  {
    id: "1",
    messenger: "Provider",
    time: "10:30",
    message:
      "Lorem ipsum dolor sit amet ahhahs hash hsb ahbs hbs haBS HNB AHNBS HANBS HNB hnbsa hnbw ahnbs hnab dhsnb ahsnba hsnb ahn...",
  },
  {
    id: "2",
    messenger: "Provider",
    time: "10:32",
    message:
      "Lorem ipsum dolor sit amet ahhahs hash hsb ahbs hbs haBS HNB AHNBS HANBS HNB hnbsa hnbw ahnbs hnab dhsnb ahsnba hsnb ahn...",
  },
  {
    id: "3",
    messenger: "Provider",
    time: "10:35",
    message:
      "Lorem ipsum dolor sit amet ahhahs hash hsb ahbs hbs haBS HNB AHNBS HANBS HNB hnbsa hnbw ahnbs hnab dhsnb ahsnba hsnb ahn...",
  },
  {
    id: "4",
    messenger: "Provider",
    time: "10:40",
    message:
      "Lorem ipsum dolor sit amet ahhahs hash hsb ahbs hbs haBS HNB AHNBS HANBS HNB hnbsa hnbw ahnbs hnab dhsnb ahsnba hsnb ahn...",
  },
  {
    id: "5",
    messenger: "Provider",
    time: "10:40",
    message:
      "Lorem ipsum dolor sit amet ahhahs hash hsb ahbs hbs haBS HNB AHNBS HANBS HNB hnbsa hnbw ahnbs hnab dhsnb ahsnba hsnb ahn...",
  },
  {
    id: "6",
    messenger: "Provider",
    time: "10:40",
    message:
      "Lorem ipsum dolor sit amet ahhahs hash hsb ahbs hbs haBS HNB AHNBS HANBS HNB hnbsa hnbw ahnbs hnab dhsnb ahsnba hsnb ahn...",
  },
  {
    id: "7",
    messenger: "Provider",
    time: "10:40",
    message:
      "Lorem ipsum dolor sit amet ahhahs hash hsb ahbs hbs haBS HNB AHNBS HANBS HNB hnbsa hnbw ahnbs hnab dhsnb ahsnba hsnb ahn...",
  },
];

export default function Transcript() {
  const transcriptStyles = useTranscriptStyles();
  const {
    container,
    mainStack,
    chatButtonContainer,
    messageListContainer,
    flatListContent,
    listFooter,
  } = transcriptStyles;

  const router = useRouter();
  const [open, setOpen] = useState(false);
  const openSettings = () => setOpen(true);
  const navigateBack = () => router.back();
  const { consultationId } = useLocalSearchParams();
  const openChat = () => {
    router.push({
      pathname: "/nurse/chat",
      params: { consultationId },
    });
  };

  return (
    <View {...container}>
      <YStack {...mainStack}>
        <ScreenHeader
          onAvatarPress={openSettings}
          screenName="Transcript"
          onBackPress={navigateBack}
        />

        <YStack {...chatButtonContainer}>
          <IconFirstButton
            backgroundColor="$confirmOrderBlue"
            borderColor="$confirmOrderBorderCOlor"
            borderWidth={1}
            icon={MessageCircle}
            text="Chat"
            textColor="$confirmOrderTextColor"
            onPress={openChat}
          />
        </YStack>

        <YStack {...messageListContainer}>
          <FlatList
            data={messages}
            keyExtractor={(item) => item.id}
            contentContainerStyle={flatListContent}
            showsVerticalScrollIndicator={false}
            renderItem={({ item }) => (
              <TranscriptMessage
                messenger={item.messenger}
                time={item.time}
                message={item.message}
              />
            )}
            ListFooterComponent={<YStack {...listFooter} />}
          />
        </YStack>

        {open && <SheetDemo open={open} setOpen={setOpen} />}
      </YStack>
    </View>
  );
}
