import { useRouter, useLocalSearchParams } from "expo-router";
import { useState } from "react";
import ScreenHeader from "src/components/ScreenHeader";
import { Button, Text, View, YStack } from "tamagui";
import { useTelehealthConsentStyle } from "./Styles/TelehealthConsentStyle";
import SheetDemo from "src/components/SettingsDrawer";
import { ScrollView } from "react-native";
import TelehealthConsentForm from "src/components/TelehealthConsent";
import { PatientInfo } from "./requestVisit";

export default function TelehealthConsent() {
  const { patientData, selectedPatients, isSinglePatient } =
    useLocalSearchParams();
  const patients: PatientInfo[] = JSON.parse(selectedPatients as string);
  const patientDataObj: { [key: string]: any } = JSON.parse(
    patientData as string
  );
  const isSinglePatientVisit = isSinglePatient === "1";
  const telehealthConsentStyle = useTelehealthConsentStyle();
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const openSettings = () => setOpen(true);
  const navigateBack = () => router.back();
  const [sendingRequest, setSendingRequest] = useState(false);

  return (
    <View {...telehealthConsentStyle.container}>
      <YStack {...telehealthConsentStyle.mainStack}>
        <ScreenHeader
          onAvatarPress={openSettings}
          screenName="Back"
          onBackPress={navigateBack}
        />
        <YStack {...telehealthConsentStyle.headerTextContainer}>
          <Text {...telehealthConsentStyle.headerText}>
            Telehealth Informed Consent
          </Text>
        </YStack>
        <YStack {...telehealthConsentStyle.consentContainer}>
          <ScrollView showsVerticalScrollIndicator={false}>
            <TelehealthConsentForm />
          </ScrollView>
        </YStack>
      </YStack>

      <View {...telehealthConsentStyle.agreeAndConnectBtnContainer}>
        <Button
          {...telehealthConsentStyle.agreeAndConnectBtn}
          onPress={() => setSendingRequest(true)}
        >
          Agree & Connect
        </Button>
        {open && <SheetDemo open={open} setOpen={setOpen} />}
      </View>
    </View>
  );
}
