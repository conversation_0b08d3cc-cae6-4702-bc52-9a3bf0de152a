import { useRouter, useLocalSearchParams } from "expo-router";
import { useState } from "react";
import ScreenHeader from "src/components/ScreenHeader";
import { Button, Text, View, XStack, YStack } from "tamagui";
import { useTelehealthConsentStyle } from "./Styles/TelehealthConsentStyle";
import SheetDemo from "src/components/SettingsDrawer";
import { FlatList, ScrollView } from "react-native";
import TelehealthConsentForm from "src/components/TelehealthConsent";
import { PatientInfo } from "./requestVisit";
import Title from "src/components/Title";

export default function TelehealthConsent() {
  const { patientData, selectedPatients, isSinglePatient } =
    useLocalSearchParams();
  const patients: PatientInfo[] = JSON.parse(selectedPatients as string);
  const patientDataObj: { [key: string]: any } = JSON.parse(
    patientData as string
  );
  const isSinglePatientVisit = isSinglePatient === "1";
  const telehealthConsentStyle = useTelehealthConsentStyle();
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const openSettings = () => setOpen(true);
  const navigateBack = () => router.back();
  const [sendingRequest, setSendingRequest] = useState(false);

  return (
    <View {...telehealthConsentStyle.container}>
      <YStack {...telehealthConsentStyle.mainStack}>
        <ScreenHeader
          onAvatarPress={openSettings}
          screenName={
            isSinglePatientVisit ? "Back" : "Request Multi-call visit"
          }
          shouldShowBackButton={isSinglePatientVisit}
          shouldSHowChatIcon={false}
          onBackPress={navigateBack}
        />
        {isSinglePatientVisit ? (
          <View>
            {" "}
            <YStack {...telehealthConsentStyle.headerTextContainer}>
              <Text {...telehealthConsentStyle.headerText}>
                Telehealth Informed Consent
              </Text>
            </YStack>
            <YStack {...telehealthConsentStyle.consentContainer}>
              <ScrollView showsVerticalScrollIndicator={false}>
                <TelehealthConsentForm />
              </ScrollView>
            </YStack>
          </View>
        ) : (
          <View {...telehealthConsentStyle.patientsContainer}>
            <Text {...telehealthConsentStyle.patientsText}>
              PATIENTS ({patients.length})
            </Text>
            <FlatList
              data={patients}
              keyExtractor={(item, index) => item.id?.toString()}
              renderItem={({ item }) => (
                <View {...telehealthConsentStyle.patientCard}>
                  <YStack gap={"$2"}>
                    <XStack justify={"space-between"}>
                      <Text {...telehealthConsentStyle.patientCardTitle}>
                        {item.name}
                      </Text>
                      <Title
                        text="Consented"
                        backgroundColor="#ECFDF3"
                        borderColor="#ABEFC6"
                      />
                    </XStack>
                    <Text {...telehealthConsentStyle.patientCardSubTitle}>
                      DOB: {item.dob}
                    </Text>
                  </YStack>
                </View>
              )}
            />
          </View>
        )}
      </YStack>

      <View {...telehealthConsentStyle.agreeAndConnectBtnContainer}>
        <Button
          {...telehealthConsentStyle.agreeAndConnectBtn}
          onPress={() => setSendingRequest(true)}
        >
          Agree & Connect
        </Button>
        {!isSinglePatientVisit && (
          <Button
            {...telehealthConsentStyle.cancelBtn}
            onPress={() => router.back()}
          >
            Cancel
          </Button>
        )}
      </View>
      {open && <SheetDemo open={open} setOpen={setOpen} />}
    </View>
  );
}
