import { Stack } from "expo-router";
import React from "react";
import { AlertsProvider } from "~/context/AlertsContext";
import { SocketProvider } from "~/context/NurseSocketContext";
import InactivityWrapper from "../../components/InactivityWrapper"; // adjust the path accordingly

export default function NurseLayout() {
  return (
    <InactivityWrapper>
      <AlertsProvider>
        <SocketProvider>
          <Stack
            screenOptions={{
              headerShown: false,
              contentStyle: { backgroundColor: "white" },
            }}
          >
            <Stack.Screen name="dashboard" options={{ headerShown: false }} />
            <Stack.Screen name="chat" options={{ headerShown: false }} />
            <Stack.Screen
              name="consultations"
              options={{ headerShown: false }}
            />
            <Stack.Screen name="new-visit" options={{ headerShown: false }} />
            <Stack.Screen
              name="requestVisit"
              options={{ headerShown: false }}
            />
            <Stack.Screen name="settings" options={{ headerShown: false }} />
            <Stack.Screen name="verify-pin" options={{ headerShown: false }} />
            <Stack.Screen
              name="calloverview"
              options={{ headerShown: false, gestureEnabled: false }}
            />
            <Stack.Screen
              name="telehealthconsent"
              options={{ headerShown: false }}
            />
            <Stack.Screen
              name="patient-consent"
              options={{ headerShown: false, gestureEnabled: false }}
            />
            <Stack.Screen
              name="call"
              options={{ headerShown: false, gestureEnabled: false }}
            />
            <Stack.Screen name="messages" options={{ headerShown: false }} />
          </Stack>
        </SocketProvider>
      </AlertsProvider>
    </InactivityWrapper>
  );
}
