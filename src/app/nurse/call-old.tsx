import { <PERSON><PERSON><PERSON>, useRoute } from "@react-navigation/native";
import {
  <PERSON><PERSON><PERSON>,
  Mi<PERSON>,
  Mic<PERSON>ff,
  PhoneCall,
  Video,
  VideoOff,
} from "@tamagui/lucide-icons";
import {
  Errors,
  EventType,
  LiveTranscriptionStatus,
  VideoAspect,
  VideoResolution,
  ZoomVideoSdkLiveTranscriptionMessageInfo,
  ZoomVideoSdkLiveTranscriptionMessageInfoType,
  ZoomVideoSdkUser,
  ZoomView,
  useZoom,
} from "@zoom/react-native-videosdk";
import { useRouter } from "expo-router";
import React, { useEffect, useRef, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Platform,
  StyleSheet,
  TouchableOpacity,
  useWindowDimensions,
} from "react-native";
import { CustomNotification } from "src/components/CustomNotification";
import { Notification } from "src/components/Notification";
import useSendTranscript from "src/hooks/useSendConsultation";
import axiosConfig from "src/services/axiosConfig";
import { Text, View } from "tamagui";
import CallKitService from "~/services/CallKitService";
import { useAuth } from "../../context/AuthContext";

const FILE_NAME = "NurseCallView";
const logEvent = (
  level: string,
  status: string,
  message: string,
  metadata: object = {}
) => {
  axiosConfig.post("/log-event", {
    level,
    status,
    name: FILE_NAME,
    message,
    metadata,
  });
};

type NurseCallRouteParams = {
  params: {
    consultationId: string;
    sdkId: string;
    rejoin?: string;
  };
};

const NurseCallView = () => {
  const { width, height } = useWindowDimensions();
  const isLandscape = width > height;
  const {
    joinSession,
    leaveSession,
    addListener,
    session,
    audioHelper,
    videoHelper,
    liveTranscriptionHelper,
  } = useZoom();
  const { user } = useAuth();
  const [loading, setLoading] = useState<boolean>(true);
  const [callStarted, setCallStarted] = useState<boolean>(false);
  const [callEnded, setCallEnded] = useState<boolean>(false);
  const [videoOn, setVideoOn] = useState<boolean>(true);
  const [audioOn, setAudioOn] = useState<boolean>(true);
  const [localUser, setLocalUser] = useState<ZoomVideoSdkUser | null>(null);
  const [remoteUsers, setRemoteUsers] = useState<ZoomVideoSdkUser | null>(null);
  const [isRecovering, setIsRecovering] = useState(false);
  const [disableReload, setDisableReload] = useState(false);
  const [viewKey, setViewKey] = useState(0);
  const [remoteUserAudio, setRemoteUserAudio] = useState<boolean>(false);
  const [localUserName, setLocalUserName] = useState<string>("");
  const [remoteUserName, setRemoteUserName] = useState<string>("");
  const listeners = useRef<any[]>([]);
  const route = useRoute<RouteProp<NurseCallRouteParams, "params">>();
  const { consultationId, sdkId, rejoin } = route.params;
  const [localSDKId, setLocalSDKId] = useState<string>(sdkId || "123");
  const [retryCount, setRetryCount] = useState<number>(0);
  const { sendTranscript, error, responseData } = useSendTranscript();
  const [transcriptionMessages, setTranscriptionMessages] = useState<{
    mine: { content: string; timestamp: number }[];
    others: { content: string; timestamp: number }[];
  }>({ mine: [], others: [] });
  const [completeTranscriptHistory, setCompleteTranscriptHistory] = useState<
    {
      speaker: string;
      text: string;
      timestamp: string;
    }[]
  >([]);
  const router = useRouter();
  const [notification, setNotification] = useState<{
    message: string;
    visible: boolean;
  }>({
    message: "",
    visible: false,
  });
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const [isProviderInCall, setIsProviderInCall] = useState<boolean>(false);
  const [nurseJoined, setNurseJoined] = useState(false);
  const [isRejoinFromDashboard, setIsRejoinFromDashboard] = useState(
    String(rejoin) === "true"
  );

  useEffect(() => {
    const netListener = addListener(
      EventType.onUserVideoNetworkStatusChanged,
      async ({ user: netUser, result }) => {
        const me = await session.getMySelf();
        if (netUser.userId === me.userId && result.uplinkNetworkQuality < 2) {
          logEvent(
            "WARN",
            "NETWORK_DROP",
            "Network quality low, restarting video"
          );
          setIsRecovering(true);
          await videoHelper.stopVideo();
          await videoHelper.startVideo();
          setIsRecovering(false);
          logEvent(
            "WARN",
            "NETWORK_RECOVER",
            "Video restarted after network drop"
          );
        }
      }
    );
    const canvasFail = addListener(
      EventType.onVideoCanvasSubscribeFail,
      ({ canvasId }) => {
        logEvent(
          "WARN",
          "CANVAS_SUBSCRIBE_FAIL",
          `Canvas ${canvasId} failed, remounting view`
        );
        setViewKey((k) => k + 1);
      }
    );
    return () => {
      netListener.remove();
      canvasFail.remove();
    };
  }, []);

  const toggleAudio = async () => {
    try {
      const mySelf = await session.getMySelf();
      const muted = await mySelf.audioStatus.isMuted();
      if (muted) {
        await audioHelper.unmuteAudio(mySelf.userId);
      } else {
        await audioHelper.muteAudio(mySelf.userId);
      }
    } catch (err) {}
  };

  const toggleVideo = async () => {
    try {
      const mySelf = await session.getMySelf();
      const currentVideoOn = await mySelf.videoStatus.isOn();
      if (currentVideoOn) {
        await videoHelper.stopVideo();
      } else {
        await videoHelper.startVideo();
      }
    } catch (err) {}
  };

  const joinSessionHandler = async () => {
    if (!consultationId || !localSDKId) {
      logEvent("ERROR", "MISSING_PARAMS", "Missing consultationId or sdkJWT");
      Alert.alert("Error", "Consultation ID is missing.");
      setLoading(false);
      return;
    }
    setLoading(true);

    if (Platform.OS === "ios") {
      try {
        const result = await CallKitService.endCurrentCallKitCall();
      } catch (err) {}
    }

    try {
      const name = `${user?.firstName} ${user?.lastName.split("")[0]}`;
      await joinSession({
        sessionName: consultationId,
        userName: name,
        token: localSDKId,
        sessionIdleTimeoutMins: 10,
        audioOptions: {
          connect: true,
          mute: false,
          autoAdjustSpeakerVolume: false,
        },
        videoOptions: { localVideoOn: true },
      });

      setCallStarted(true);
      logEvent("INFO", "JOIN_SESSION_SUCCESS", "Joined session successfully");

      const sessionJoinListener = addListener(
        EventType.onSessionJoin,
        async () => {
          const mySelf = new ZoomVideoSdkUser(await session.getMySelf());
          const remoteUsers = await session.getRemoteUsers();
          const mySelfName = await mySelf.getUserName();
          const remoteUserName = await remoteUsers?.[0]?.getUserName();
          setLocalUserName(mySelfName);
          setRemoteUserName(remoteUserName);
          setRemoteUsers(remoteUsers?.[0]);
          setLocalUser(mySelf);
          setNurseJoined(true);
          await startTranscription();
        }
      );
      listeners.current.push(sessionJoinListener);

      const userJoinListener = addListener(
        EventType.onUserJoin,
        async (event) => {
          const mySelf = new ZoomVideoSdkUser(await session.getMySelf());
          const remoteUsers = await session.getRemoteUsers();
          console.log("user join listener ", event);
          remoteUsers?.length === 0
            ? setIsProviderInCall(false)
            : setIsProviderInCall(true);
          const mySelfName = await mySelf.getUserName();
          const remoteUserName = await remoteUsers?.[0]?.getUserName();
          setLocalUserName(mySelfName);
          setRemoteUserName(remoteUserName);
          setRemoteUsers(remoteUsers?.[0]);
          setLocalUser(mySelf);
          const joinedUser = event?.joinedUsers?.[0];
          if (joinedUser?.userId !== mySelf?.userId) {
            setNotification({
              message: `${joinedUser?.userName || "Provider"} joined the call`,
              visible: true,
            });
          }
        }
      );
      listeners.current.push(userJoinListener);

      const userLeaveListener = addListener(
        EventType.onUserLeave,
        async (event) => {
          const mySelf = new ZoomVideoSdkUser(await session.getMySelf());
          const remoteUsers = await session.getRemoteUsers();
          const mySelfName = await mySelf.getUserName();
          const remoteUserName = await remoteUsers?.[0]?.getUserName();
          setLocalUserName(mySelfName);
          setRemoteUserName(remoteUserName);
          setRemoteUsers(remoteUsers?.[0]);
          setLocalUser(mySelf);
          const leftuser = event?.leftUsers?.[0];
          if (leftuser?.userId !== mySelf.userId) {
            setNotification({
              message: `${leftuser?.userName || "Provider"} left the call`,
              visible: true,
            });
          }
        }
      );
      listeners.current.push(userLeaveListener);
      const userAudioListener = addListener(
        EventType.onUserAudioStatusChanged,
        async (event) => {
          const { changedUsers } = event;
          const mySelf = new ZoomVideoSdkUser(await session.getMySelf());
          const remoteUsers = await session.getRemoteUsers();
          console.log("Changed users:", changedUsers);

          changedUsers.forEach(async (changedUser) => {
            if (changedUser.userId === mySelf.userId) {
              const muted = await mySelf.audioStatus.isMuted();
              setAudioOn(!muted);
            } else {
              try {
                const foundRemoteUser = remoteUsers.find(
                  (u: ZoomVideoSdkUser) => u.userId === changedUser.userId
                );

                if (foundRemoteUser) {
                  const zoomRemoteUser = new ZoomVideoSdkUser(foundRemoteUser);
                  const muted = await zoomRemoteUser.audioStatus.isMuted();
                  setRemoteUserAudio(!muted);
                }
              } catch (err) {}
            }
          });
        }
      );

      listeners.current.push(userAudioListener);

      const userVideoListener = addListener(
        EventType.onUserVideoStatusChanged,
        async (event) => {
          const { changedUsers } = event;
          const mySelf = new ZoomVideoSdkUser(await session.getMySelf());
          if (changedUsers.find((u) => u.userId === mySelf.userId)) {
            const on = await mySelf.videoStatus.isOn();
            setVideoOn(on);
          }
        }
      );
      listeners.current.push(userVideoListener);

      const sessionLeaveListener2 = addListener(
        EventType.onSessionLeave,
        () => {
          setCallStarted(false);
          setLocalUserName("");
          setRemoteUserName("");
          setRemoteUsers(null);
          setLocalUser(null);
          listeners.current.forEach((l) => l.remove());
          listeners.current = [];
        }
      );
      listeners.current.push(sessionLeaveListener2);

      const sessionError = addListener(EventType.onError, (event) => {
        sessionLeaveListener2.remove();
      });
      listeners.current.push(sessionError);
      await audioHelper.startAudio();
    } catch (err) {
      if (retryCount >= 1) {
        logEvent(
          "ERROR",
          "JOIN_SESSION_ERROR",
          `Error joining session ${retryCount + 1} times`,
          err as object
        );
        onRejoinCall();
      } else {
        logEvent(
          "ERROR",
          "JOIN_SESSION_ERROR",
          `Error joining session 1 times`,
          err as object
        );
        try {
          await leaveSession();
        } catch (leaveErr) {
          logEvent(
            "ERROR",
            "LEAVE_SESSION_ERROR",
            "Error leaving session after join failure",
            leaveErr as object
          );
        }
        setRetryCount((prev) => prev + 1);
        setTimeout(() => {
          joinSessionHandler();
        }, 5000);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleLeaveSession = async () => {
    try {
      await fetchTranscriptionMessages();
      await liveTranscriptionHelper.stopLiveTranscription();
      listeners.current.forEach((listener) => listener.remove());
      listeners.current = [];

      // stop transcription
      try {
        await liveTranscriptionHelper.stopLiveTranscription();
      } catch {}
      // stop AV
      try {
        await audioHelper.stopAudio();
      } catch {}
      try {
        await videoHelper.stopVideo();
      } catch {}
      // clear timers

      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }

      await leaveSession();
      // small delay helps native teardown
      await new Promise((r) => setTimeout(r, 800));

      // reset UI state
      setVideoOn(true);
      setAudioOn(true);
      setLocalUser(null);
      setRemoteUsers(null);
      setIsRecovering(false);
      setRemoteUserAudio(false);
      setLocalUserName("");
      setRemoteUserName("");
      setCallStarted(false);
      setIsProviderInCall(false);
      setNurseJoined(false);
      logEvent("INFO", "LEAVE_SESSION_SUCCESS", "Left session successfully");
    } catch (err) {
      logEvent(
        "ERROR",
        "LEAVE_SESSION_ERROR",
        "Error leaving session",
        err as object
      );
      Alert.alert("Error", "Failed to leave the session");
    } finally {
      callOverView();
      setCallEnded(true);
    }
  };

  useEffect(() => {
    joinSessionHandler();
    return () => {};
  }, [localSDKId]);

  useEffect(() => {
    return () => {
      listeners.current.forEach((listener) => listener.remove());
      listeners.current = [];
    };
  }, []);

  const onRejoinCall = async () => {
    try {
      const consultationId = route.params.consultationId;
      const url = `/consultation/rejoin-request/${consultationId}`;
      const response = await axiosConfig.get(url);
      const { nurseSDKJWT } = response.data;
      setLocalSDKId(nurseSDKJWT);
    } catch (error) {}
  };

  const rejoinSession = async () => {
    setDisableReload(true);
    setIsRejoinFromDashboard(false);
    if (callEnded) {
      try {
        await joinSessionHandler();
      } catch (err) {
        logEvent(
          "ERROR",
          "REJOIN_SESSION_ERROR",
          "Error rejoining session",
          err as object
        );
        Alert.alert("Error", "Failed to rejoin the session");
      }
      setCallEnded(false);
    }

    try {
      listeners.current.forEach((listener) => listener.remove());
      listeners.current = [];

      // stop transcription
      try {
        await liveTranscriptionHelper.stopLiveTranscription();
      } catch {}
      // stop AV
      try {
        await audioHelper.stopAudio();
      } catch {}
      try {
        await videoHelper.stopVideo();
      } catch {}
      // clear timers

      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }

      leaveSession();
      // small delay helps native teardown
      await new Promise<void>((resolve) => {
        const sub = addListener(EventType.onSessionLeave, () => {
          sub.remove();
          resolve();
        });
      });
      await new Promise((r) => setTimeout(r, 800));

      // reset UI state
      setCallEnded(false);
      setVideoOn(true);
      setAudioOn(true);
      setLocalUser(null);
      setRemoteUsers(null);
      setIsRecovering(false);
      setRemoteUserAudio(false);
      setLocalUserName("");
      setRemoteUserName("");
      setCallStarted(false);
      setIsProviderInCall(false);
      setNurseJoined(false);

      // now rejoin
      await joinSessionHandler();
      // bump viewKey after join
      setViewKey((k) => k + 1);
    } catch (err) {
      logEvent(
        "ERROR",
        "REJOIN_CALL_ERROR",
        "Error rejoining call",
        err as object
      );
      onRejoinCall();
    } finally {
      setDisableReload(false);
    }
  };

  const startTranscription = async () => {
    try {
      const canStart =
        await liveTranscriptionHelper.canStartLiveTranscription();
      if (canStart) {
        const result = await liveTranscriptionHelper.startLiveTranscription();
        if (result === Errors.Success) {
          const liveTranscriptionStatusChangeListener = addListener(
            EventType.onLiveTranscriptionStatus,
            ({ status }: { status: LiveTranscriptionStatus }) => {}
          );
          listeners.current.push(liveTranscriptionStatusChangeListener);

          await liveTranscriptionHelper.setSpokenLanguage(0);
          await liveTranscriptionHelper.setTranslationLanguage(0);

          const liveTranscriptionMsgInfoReceivedListener = addListener(
            EventType.onLiveTranscriptionMsgInfoReceived,
            ({
              messageInfo,
            }: {
              messageInfo: ZoomVideoSdkLiveTranscriptionMessageInfoType;
            }) => {
              const message =
                messageInfo as ZoomVideoSdkLiveTranscriptionMessageInfo;
              setCompleteTranscriptHistory((prev) => [
                ...prev,
                {
                  speaker: message.speakerName ?? "Unknown",
                  text: message.messageContent || "N/A",
                  timestamp: new Date(
                    Number(message.timeStamp) * 1000
                  ).toISOString(),
                },
              ]);
            }
          );
          listeners.current.push(liveTranscriptionMsgInfoReceivedListener);

          await liveTranscriptionHelper.enableReceiveSpokenLanguageContent(
            true
          );

          const originalLanguageMsgInfoReceivedListener = addListener(
            EventType.onOriginalLanguageMsgReceived,
            ({
              messageInfo,
            }: {
              messageInfo: ZoomVideoSdkLiveTranscriptionMessageInfoType;
            }) => {}
          );
          listeners.current.push(originalLanguageMsgInfoReceivedListener);
        } else {
          logEvent(
            "ERROR",
            "TRANSCRIPTION_FAILED",
            `Failed to start transcription: ${result}`,
            { result }
          );
        }
      } else {
        logEvent(
          "ERROR",
          "TRANSCRIPTION_PERMISSION_DENIED",
          "Cannot start transcription"
        );
      }
    } catch (error) {
      logEvent(
        "ERROR",
        "TRANSCRIPTION_ERROR",
        "Error in startTranscription",
        error as object
      );
    }
  };

  const fetchTranscriptionMessages = async () => {
    try {
      const messages =
        await liveTranscriptionHelper.getHistoryTranslationMessageList();
      const formattedTranscript = messages.map(
        (msg: ZoomVideoSdkLiveTranscriptionMessageInfo) => {
          const isNurse = msg.speakerName === "Nurse";
          return {
            speaker: msg.speakerName ?? "Unknown",
            text: msg.messageContent || "N/A",
            timestamp: new Date(Number(msg.timeStamp) * 1000).toISOString(),
          };
        }
      );
      setCompleteTranscriptHistory((prev) => [...prev, ...formattedTranscript]);
      const allMessages = [
        ...completeTranscriptHistory,
        ...formattedTranscript,
      ].sort(
        (a: { timestamp: string }, b: { timestamp: string }) =>
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );

      await sendTranscript(consultationId, allMessages);
    } catch (error) {
      logEvent(
        "ERROR",
        "FETCH_TRANSCRIPTIONS_ERROR",
        "Error fetching transcription messages",
        error as object
      );
    }
  };

  const callOverView = () => {
    setCompleteTranscriptHistory([]);
    router.replace({
      pathname: "/nurse/calloverview",
      params: { consultationId },
    });
  };

  useEffect(() => {
    if (nurseJoined && isRejoinFromDashboard) {
      timerRef.current = setTimeout(async () => {
        if (session && consultationId && user?.id) {
          try {
            const remoteUsersArr = await session.getRemoteUsers();
            if (!remoteUsersArr || remoteUsersArr.length === 0) {
              console.log("Remote user absent");
              const response = await axiosConfig.post(
                `consultation/rejoin/notify/${consultationId}`
              );
            } else {
              console.log("Remote user present");
            }
          } catch (e) {}
        }
      }, 5000);
    }
    return () => {
      if (timerRef.current) clearTimeout(timerRef.current);
    };
  }, [nurseJoined, isRejoinFromDashboard]);

  if (loading || isRecovering) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#ffffff" />
        <View style={styles.loadingReloadVideoButtonContainer}>
          <TouchableOpacity
            style={styles.reloadVideoButton}
            onPress={() => {
              logEvent(
                "INFO",
                "MANUAL_RETRY",
                "User tapped retry in controls 1"
              );
              setDisableReload(true);
              rejoinSession();
              setTimeout(() => {
                setDisableReload(false);
              }, 5000);
            }}
          >
            <Text
              style={[
                styles.reloadButtonText,
                disableReload && { color: "grey" },
              ]}
            >
              Reload Video
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {callEnded ? (
        <View style={styles.callEndedContainer}>
          <View style={{ gap: 6 }}>
            <TouchableOpacity
              style={styles.callEndedButton}
              onPress={rejoinSession}
            >
              <PhoneCall size={24} color="#ffffff" />
            </TouchableOpacity>
            <Text>Rejoin Call</Text>
          </View>
          <View
            style={{ gap: 6, justifyContent: "center", alignItems: "center" }}
          >
            <TouchableOpacity
              style={styles.callDetailsButton}
              onPress={callOverView}
            >
              <ArrowRight size={24} color="#ffffff" />
            </TouchableOpacity>
            <Text>End Call</Text>
          </View>
        </View>
      ) : (
        <>
          <View style={styles.topSpacer} />
          <View
            style={[
              styles.videoContainer,
              isLandscape && { flexDirection: "row", gap: 10 },
            ]}
          >
            {remoteUsers ? (
              <>
                <View key={remoteUsers.userId} style={styles.videoWrapper}>
                  <ZoomView
                    style={styles.zoomView}
                    userId={remoteUsers.userId}
                    fullScreen={false}
                    videoAspect={VideoAspect.PanAndScan}
                    videoResolution={VideoResolution.ResolutionAuto}
                  />
                  <View style={styles.userInfoOverlay}>
                    <Text style={styles.userNameText}>{remoteUserName}</Text>
                    {remoteUserAudio ? (
                      <Mic size={16} color="#0f0" />
                    ) : (
                      <MicOff size={16} color="#f00" />
                    )}
                  </View>
                </View>
                {localUser && (
                  <View style={styles.videoWrapper}>
                    <ZoomView
                      key={viewKey + 1}
                      style={styles.zoomView}
                      userId={localUser.userId}
                      fullScreen={false}
                      videoAspect={VideoAspect.PanAndScan}
                      videoResolution={VideoResolution.ResolutionAuto}
                    />
                    <View style={styles.userInfoOverlay}>
                      <Text style={styles.userNameText}>{localUserName}</Text>
                      {audioOn ? (
                        <Mic size={16} color="#0f0" />
                      ) : (
                        <MicOff size={16} color="#f00" />
                      )}
                    </View>
                  </View>
                )}
              </>
            ) : localUser ? (
              <View style={[styles.videoWrapper, { flex: 1 }]}>
                <ZoomView
                  key={viewKey + 1}
                  style={styles.zoomView}
                  userId={localUser.userId}
                  fullScreen={true}
                  videoAspect={VideoAspect.PanAndScan}
                  videoResolution={VideoResolution.ResolutionAuto}
                />
                <View style={styles.userInfoOverlay}>
                  <Text style={styles.userNameText}>{localUserName}</Text>
                  {audioOn ? (
                    <Mic size={16} color="#0f0" />
                  ) : (
                    <MicOff size={16} color="#f00" />
                  )}
                </View>
              </View>
            ) : (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color="#ffffff" />
                <View style={styles.loadingReloadVideoButtonContainer}>
                  <TouchableOpacity
                    style={styles.reloadVideoButton}
                    onPress={() => {
                      logEvent(
                        "INFO",
                        "MANUAL_RETRY",
                        "User tapped retry in controls 2"
                      );
                      setDisableReload(true);
                      rejoinSession();
                      setTimeout(() => {
                        setDisableReload(false);
                      }, 5000);
                    }}
                  >
                    <Text
                      style={[
                        styles.reloadButtonText,
                        disableReload && { color: "grey" },
                      ]}
                    >
                      Reload Video
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>

          {callStarted && (
            <>
              <View style={styles.controlsContainer}>
                <TouchableOpacity
                  style={styles.controlButton}
                  onPress={toggleVideo}
                >
                  {videoOn ? (
                    <Video size={24} color="#ffffff" />
                  ) : (
                    <VideoOff size={24} color="#ffffff" />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.controlButton}
                  onPress={toggleAudio}
                >
                  {audioOn ? (
                    <Mic size={24} color="#ffffff" />
                  ) : (
                    <MicOff size={24} color="#ffffff" />
                  )}
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.controlButton, styles.endCallButton]}
                  onPress={handleLeaveSession}
                >
                  {/* <PhoneOff size={24} color="#ffffff" /> */}
                  <Text style={styles.reloadButtonText}>End</Text>
                </TouchableOpacity>
              </View>
              <View style={styles.reloadVideoButtonContainer}>
                <TouchableOpacity
                  disabled={disableReload}
                  style={styles.reloadVideoButton}
                  onPress={() => {
                    logEvent(
                      "INFO",
                      "MANUAL_RETRY",
                      "User tapped retry in controls 3"
                    );
                    setDisableReload(true);
                    rejoinSession();
                    setTimeout(() => {
                      setDisableReload(false);
                    }, 5000);
                  }}
                >
                  <Text
                    style={[
                      styles.reloadButtonText,
                      disableReload && { color: "grey" },
                    ]}
                  >
                    Reload Video
                  </Text>
                </TouchableOpacity>
              </View>
            </>
          )}
          <Notification
            message={notification.message}
            visible={notification.visible}
            onHide={() =>
              setNotification((prev) => ({ ...prev, visible: false }))
            }
          />

          {!loading && !isRecovering && (
            <CustomNotification
              message="Waiting for provider to join call"
              visible={!isProviderInCall}
              onHide={() => {}}
            />
          )}
        </>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  callEndedContainer: {
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "row",
    flex: 1,
    gap: 20,
  },
  buttonContainer: {
    flexDirection: "row",
    gap: 16,
    alignItems: "center",
    justifyContent: "center",
  },
  callDetailsButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "#555",
    justifyContent: "center",
    alignItems: "center",
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  callEndedButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    backgroundColor: "#4CAF50",
    alignItems: "center",
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  container: {
    flex: 1,
    backgroundColor: "#0C111D",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#0C111D",
  },
  topSpacer: {
    flex: 0,
  },
  videoContainer: {
    flex: 0.9,
    padding: 10,
  },
  videoWrapper: {
    flex: 1,
    marginVertical: 5,
    borderRadius: 10,
    overflow: "hidden",
    position: "relative",
    backgroundColor: "#1D2939",
  },
  zoomView: {
    width: "100%",
    height: "100%",
  },
  placeholder: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#333",
    borderRadius: 10,
    marginVertical: 5,
  },
  placeholderText: {
    color: "#ffffff",
    fontSize: 16,
    fontWeight: "500",
  },
  controlsContainer: {
    flex: 0.1,
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    paddingHorizontal: 20,
    backgroundColor: "#1D2939",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginInline: 10,
  },
  reloadVideoButtonContainer: {
    padding: 5,
    backgroundColor: "#1D2939",
    // borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
    marginInline: 10,
    flexDirection: "row",
    gap: 10,
  },
  loadingReloadVideoButtonContainer: {
    padding: 5,
    backgroundColor: "#1D2939",
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
    marginInline: 10,
    marginBottom: 20,
    marginTop: 40,
    flexDirection: "row",
    gap: 10,
  },
  reloadVideoButton: {
    // backgroundColor: "#555",
    padding: 6,
    paddingHorizontal: 15,
    borderRadius: 50,
  },
  reloadButtonText: {
    color: "#ffffff",
    fontWeight: "600",
  },
  controlButton: {
    padding: 15,
    backgroundColor: "#555",
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
  },
  retryText: {
    color: "#ffffff",
    fontWeight: "600",
  },
  endCallButton: {
    backgroundColor: "#ff4444",
    height: 50,
    width: 50,
    padding: 10,
  },
  userInfoOverlay: {
    position: "absolute",
    bottom: 8,
    left: 8,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.6)",
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },

  userNameText: {
    color: "#fff",
    fontSize: 12,
    marginRight: 4,
  },
});

export default NurseCallView;
