import * as Updates from "expo-updates";
import { Platform } from "react-native";
import * as Application from "expo-application";
import axiosConfig from "./axiosConfig";

export interface CheckUpdateResult {
  isAvailable: boolean;
  version?: string;
  currentVersion?: string;
}

export const checkForUpdates = async (): Promise<CheckUpdateResult> => {
  if (__DEV__) {
    console.log("Skipping update check in development mode.");
    return { isAvailable: false };
  }
  if (!Updates.isEnabled) {
    try {
      const eventBody = {
        level: "ERROR",
        name: "check-for-updates-services",
        message: "Updates are not enabled",
        metadata: JSON.stringify({ isEnabled: Updates.isEnabled }),
      };
      await axiosConfig.post("/log-event", eventBody);
    } catch {
      console.error("Failed to log update check error");
    }
    return { isAvailable: false };
  }

  try {
    const currentVersion = getAppVersion();
    const update = await Updates.checkForUpdateAsync();
    if (update.isAvailable) {
      return {
        isAvailable: true,
        version: currentVersion,
        currentVersion,
      };
    }
    return { isAvailable: false };
  } catch (error) {
    try {
      const eventBody = {
        level: "ERROR",
        name: "check-for-updates-services",
        message: "Error during update check",
        metadata: JSON.stringify(error),
      };
      await axiosConfig.post("/log-event", eventBody);
    } catch {
      console.error("Failed to log update check error");
    }
    console.error("Error checking for updates:", error);
  }

  return { isAvailable: false };
};

export const downloadAndReload = async (): Promise<void> => {
  if (__DEV__) {
    console.log("Skipping download in development mode.");
    return;
  }

  try {
    await Updates.fetchUpdateAsync();
    await Updates.reloadAsync();
  } catch (error) {
    console.error("Error downloading or reloading update:", error);
  }
};

export const getAppVersion = (): string => {
  return (
    Platform.select({
      ios: Application.nativeApplicationVersion,
      android: Application.nativeApplicationVersion,
      default: Application.nativeApplicationVersion,
    }) ?? "1.0.0"
  );
};
