import { NativeModules, Platform } from "react-native";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "react-native-callkeep";

// Safely access the RingtoneModule
const { RingtoneModule } = NativeModules;

/**
 * Service to interact with the native CallKit functionality
 */
class CallKitService {
  // Track active call UUIDs
  private activeCallUUIDs: string[] = [];

  constructor() {
    if (Platform.OS === "ios") {
      // Listen for new calls
      RNCallKeep.addEventListener("didReceiveStartCallAction", (data: any) => {
        if (data && data.callUUID) this.onCallStarted(data.callUUID);
      });

      RNCallKeep.addEventListener("answerCall", (data: any) => {
        if (data && data.callUUID) this.onCallAnswered(data.callUUID);
      });

      RNCallKeep.addEventListener("endCall", (data: any) => {
        if (data && data.callUUID) this.onCallEnded(data.callUUID);
      });
    }
  }

  private onCallStarted = (callUUID: string) => {
    console.log("Call started with UUID:", callUUID);
    if (callUUID && !this.activeCallUUIDs.includes(callUUID)) {
      this.activeCallUUIDs.push(callUUID);
    }
  };

  private onCallAnswered = (callUUID: string) => {
    console.log("Call answered with UUID:", callUUID);
    if (callUUID && !this.activeCallUUIDs.includes(callUUID)) {
      this.activeCallUUIDs.push(callUUID);
    }
  };

  private onCallEnded = (callUUID: string) => {
    console.log("Call ended with UUID:", callUUID);
    if (callUUID) {
      // Remove the UUID from our tracked list
      this.activeCallUUIDs = this.activeCallUUIDs.filter(
        (uuid) => uuid !== callUUID
      );

      // Ensure the call is properly ended in CallKit
      if (Platform.OS === "ios") {
        // Try to end the call using the native module as well for redundancy
        if (
          RingtoneModule &&
          typeof RingtoneModule.endCallWithUUID === "function"
        ) {
          console.log(`Ensuring call is ended with UUID: ${callUUID}`);
          RingtoneModule.endCallWithUUID(callUUID).catch((error: any) => {
            console.log(`Error ending call with UUID ${callUUID}:`, error);
          });
        }
      }
    }
  };

  /**
   * Get active calls for debugging
   * @returns Promise that resolves with the active calls
   */
  getActiveCalls(): Promise<any[]> {
    return new Promise((resolve) => {
      try {
        if (Platform.OS !== "ios") {
          resolve([]);
          return;
        }

        RNCallKeep.getCalls()
          .then((calls: any) => {
            if (calls && Array.isArray(calls)) {
              console.log(`Found ${calls.length} active calls from CallKit`);
              resolve(calls);
            } else {
              console.log(
                "No active calls found from CallKit, using tracked UUIDs"
              );
              // Return our tracked UUIDs as fallback
              const trackedCalls = this.activeCallUUIDs.map((uuid) => ({
                callUUID: uuid,
              }));
              resolve(trackedCalls);
            }
          })
          .catch((error: any) => {
            console.error("Error getting calls from CallKit:", error);
            // Return our tracked UUIDs as fallback
            const trackedCalls = this.activeCallUUIDs.map((uuid) => ({
              callUUID: uuid,
            }));
            resolve(trackedCalls);
          });
      } catch (error) {
        console.error("Error getting active calls:", error);
        resolve([]);
      }
    });
  }

  /**
   * Reset the CallKit provider - nuclear option
   * This completely resets the CallKit provider, which should end all active calls
   * @returns Promise that resolves when the provider is reset
   */
  resetCallKitProvider(): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        if (Platform.OS !== "ios") {
          resolve(false);
          return;
        }

        // First try the direct end all calls method
        if (
          RingtoneModule &&
          typeof RingtoneModule.directEndAllCalls === "function"
        ) {
          console.log("Using direct method to end all calls");
          RingtoneModule.directEndAllCalls()
            .then((result: boolean) => {
              console.log("Direct end all calls result:", result);
              // Clear our tracked UUIDs
              this.activeCallUUIDs = [];
              resolve(true);
            })
            .catch((error: any) => {
              console.log("Error using direct method to end calls:", error);

              // Fall back to resetCallKitProvider if available
              if (
                RingtoneModule &&
                typeof RingtoneModule.resetCallKitProvider === "function"
              ) {
                console.log(
                  "NUCLEAR OPTION: Falling back to resetting CallKit provider"
                );
                RingtoneModule.resetCallKitProvider()
                  .then(() => {
                    console.log("Successfully reset CallKit provider");
                    // Clear our tracked UUIDs
                    this.activeCallUUIDs = [];
                    resolve(true);
                  })
                  .catch((resetError: any) => {
                    console.error(
                      "Error resetting CallKit provider:",
                      resetError
                    );
                    resolve(false);
                  });
              } else {
                console.log("resetCallKitProvider method not available");
                resolve(false);
              }
            });
        } else if (
          RingtoneModule &&
          typeof RingtoneModule.resetCallKitProvider === "function"
        ) {
          console.log("NUCLEAR OPTION: Directly resetting CallKit provider");
          RingtoneModule.resetCallKitProvider()
            .then(() => {
              console.log("Successfully reset CallKit provider");
              // Clear our tracked UUIDs
              this.activeCallUUIDs = [];
              resolve(true);
            })
            .catch((error: any) => {
              console.error("Error resetting CallKit provider:", error);
              resolve(false);
            });
        } else {
          console.log(
            "Neither directEndAllCalls nor resetCallKitProvider methods are available"
          );
          resolve(false);
        }
      } catch (error) {
        console.error("Error in resetCallKitProvider:", error);
        resolve(false);
      }
    });
  }
  /**
   * Play the ringtone
   * @returns Promise that resolves when the ringtone starts playing
   */
  playRingtone(): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        if (Platform.OS !== "ios") {
          resolve(false);
          return;
        }

        if (
          RingtoneModule &&
          typeof RingtoneModule.playRingtone === "function"
        ) {
          RingtoneModule.playRingtone()
            .then(() => resolve(true))
            .catch((error: any) => {
              console.error("Error playing ringtone:", error);
              resolve(false);
            });
        } else {
          console.log("RingtoneModule not available for playing ringtone");
          resolve(false);
        }
      } catch (error) {
        console.error("Error in playRingtone:", error);
        resolve(false);
      }
    });
  }

  /**
   * Stop the ringtone
   * @returns Promise that resolves when the ringtone stops
   */
  stopRingtone(): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        if (Platform.OS !== "ios") {
          resolve(false);
          return;
        }

        if (
          RingtoneModule &&
          typeof RingtoneModule.stopRingtone === "function"
        ) {
          RingtoneModule.stopRingtone()
            .then(() => resolve(true))
            .catch((error: any) => {
              console.error("Error stopping ringtone:", error);
              resolve(false);
            });
        } else {
          console.log("RingtoneModule not available for stopping ringtone");
          resolve(false);
        }
      } catch (error) {
        console.error("Error in stopRingtone:", error);
        resolve(false);
      }
    });
  }

  /**
   * End the current CallKit call
   * This should be called when a Zoom call starts to ensure the CallKit UI is ended
   * @returns Promise that resolves when the CallKit call is ended
   */
  endCurrentCallKitCall(): Promise<boolean> {
    // Just use the same implementation as endAllCalls for consistency
    return this.endAllCalls();
  }

  /**
   * End all active CallKit calls using RNCallKeep
   * This is a more reliable way to end calls than using the native module
   * @returns Promise that resolves when all calls are ended
   */
  endAllCalls(): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        if (Platform.OS !== "ios") {
          resolve(false);
          return;
        }

        console.log("Attempting to end all active calls...");

        // Try to get current calls from CallKit directly
        RNCallKeep.getCalls()
          .then((calls: any) => {
            if (calls && Array.isArray(calls) && calls.length > 0) {
              console.log(`Found ${calls.length} active calls from CallKit`);

              // Use multiple approaches to ensure calls are ended

              // 1. Use RNCallKeep.endAllCalls() first
              console.log("Using RNCallKeep.endAllCalls() to end all calls");
              RNCallKeep.endAllCalls();

              // 2. Then try to end each call individually
              calls.forEach((call: any) => {
                if (call && call.callUUID) {
                  console.log(`Ending call with UUID: ${call.callUUID}`);
                  RNCallKeep.endCall(call.callUUID);

                  // 3. Also try to end the call using the native module if available
                  if (
                    RingtoneModule &&
                    typeof RingtoneModule.endCallWithUUID === "function"
                  ) {
                    console.log(
                      `Using native module to end call with UUID: ${call.callUUID}`
                    );
                    RingtoneModule.endCallWithUUID(call.callUUID).catch(
                      (error: any) => {
                        console.log(
                          `Error ending call with UUID ${call.callUUID}:`,
                          error
                        );
                      }
                    );
                  }
                }
              });

              // 4. Force end all calls using the native module
              if (
                RingtoneModule &&
                typeof RingtoneModule.forceEndAllCalls === "function"
              ) {
                console.log("Using native module to force end all calls");
                RingtoneModule.forceEndAllCalls().catch((error: any) => {
                  console.log("Error force ending all calls:", error);
                });
              }

              // 5. Try direct method to end all calls
              if (
                RingtoneModule &&
                typeof RingtoneModule.directEndAllCalls === "function"
              ) {
                console.log("Using direct method to end all calls");
                RingtoneModule.directEndAllCalls()
                  .then((result: boolean) => {
                    console.log("Direct end all calls result:", result);
                  })
                  .catch((error: any) => {
                    console.log(
                      "Error using direct method to end calls:",
                      error
                    );
                  });
              }

              // 6. Nuclear option - reset the CallKit provider
              if (
                RingtoneModule &&
                typeof RingtoneModule.resetCallKitProvider === "function"
              ) {
                console.log("NUCLEAR OPTION: Resetting CallKit provider");
                RingtoneModule.resetCallKitProvider()
                  .then((result: boolean) => {
                    console.log("Reset CallKit provider result:", result);
                  })
                  .catch((error: any) => {
                    console.log("Error resetting CallKit provider:", error);
                  });
              }
            } else {
              console.log(
                "No active calls found from CallKit, using tracked UUIDs"
              );
              // Use our tracked UUIDs as fallback
              if (this.activeCallUUIDs.length > 0) {
                // Use RNCallKeep.endAllCalls() first
                console.log("Using RNCallKeep.endAllCalls() to end all calls");
                RNCallKeep.endAllCalls();

                this.activeCallUUIDs.forEach((uuid) => {
                  console.log(`Ending call with tracked UUID: ${uuid}`);
                  RNCallKeep.endCall(uuid);
                });

                // Try direct method to end all calls
                if (
                  RingtoneModule &&
                  typeof RingtoneModule.directEndAllCalls === "function"
                ) {
                  console.log("Using direct method to end all calls");
                  RingtoneModule.directEndAllCalls()
                    .then((result: boolean) => {
                      console.log("Direct end all calls result:", result);
                    })
                    .catch((error: any) => {
                      console.log(
                        "Error using direct method to end calls:",
                        error
                      );
                    });
                }

                // Also try the nuclear option
                if (
                  RingtoneModule &&
                  typeof RingtoneModule.resetCallKitProvider === "function"
                ) {
                  console.log("NUCLEAR OPTION: Resetting CallKit provider");
                  RingtoneModule.resetCallKitProvider()
                    .then((result: boolean) => {
                      console.log("Reset CallKit provider result:", result);
                    })
                    .catch((error: any) => {
                      console.log("Error resetting CallKit provider:", error);
                    });
                }
              } else {
                // Last resort - use endAllCalls and reset provider
                console.log(
                  "No tracked UUIDs, using endAllCalls as last resort"
                );
                RNCallKeep.endAllCalls();

                if (
                  RingtoneModule &&
                  typeof RingtoneModule.directEndAllCalls === "function"
                ) {
                  console.log("Using direct method to end all calls");
                  RingtoneModule.directEndAllCalls()
                    .then((result: boolean) => {
                      console.log("Direct end all calls result:", result);
                    })
                    .catch((error: any) => {
                      console.log(
                        "Error using direct method to end calls:",
                        error
                      );
                    });
                }

                if (
                  RingtoneModule &&
                  typeof RingtoneModule.resetCallKitProvider === "function"
                ) {
                  console.log("NUCLEAR OPTION: Resetting CallKit provider");
                  RingtoneModule.resetCallKitProvider()
                    .then((result: boolean) => {
                      console.log("Reset CallKit provider result:", result);
                    })
                    .catch((error: any) => {
                      console.log("Error resetting CallKit provider:", error);
                    });
                }
              }
            }
          })
          .catch((error: any) => {
            console.error("Error getting calls from CallKit:", error);
            // Fallback to endAllCalls and reset provider
            console.log("Falling back to endAllCalls");
            RNCallKeep.endAllCalls();

            if (
              RingtoneModule &&
              typeof RingtoneModule.directEndAllCalls === "function"
            ) {
              console.log("Using direct method to end all calls");
              RingtoneModule.directEndAllCalls()
                .then((result: boolean) => {
                  console.log("Direct end all calls result:", result);
                })
                .catch((error: any) => {
                  console.log("Error using direct method to end calls:", error);
                });
            }

            if (
              RingtoneModule &&
              typeof RingtoneModule.resetCallKitProvider === "function"
            ) {
              console.log("NUCLEAR OPTION: Resetting CallKit provider");
              RingtoneModule.resetCallKitProvider()
                .then((result: boolean) => {
                  console.log("Reset CallKit provider result:", result);
                })
                .catch((error: any) => {
                  console.log("Error resetting CallKit provider:", error);
                });
            }
          });

        // Stop any playing ringtone if possible
        if (
          RingtoneModule &&
          typeof RingtoneModule.stopRingtone === "function"
        ) {
          RingtoneModule.stopRingtone().catch((error: any) => {
            console.log("Error stopping ringtone:", error);
          });
        }

        // Clear our tracked UUIDs
        this.activeCallUUIDs = [];

        // Resolve immediately since we've initiated the call ending process
        resolve(true);
      } catch (error) {
        console.error("Error ending all calls:", error);
        resolve(false);
      }
    });
  }
}

export default new CallKitService();
