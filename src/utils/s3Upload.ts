export async function uploadToS3(
  uploadUrl: string,
  fileUri: string,
  mimeType: string
) {
  console.log("Uploading to S3:", uploadUrl, fileUri, mimeType);
  const response = await fetch(fileUri);
  const blob = await response.blob();

  const uploadResponse = await fetch(uploadUrl, {
    method: "PUT",
    body: blob,
    headers: {
      "Content-Type": mimeType, // ← must match your presigned extraHeaders
    },
  });

  if (!uploadResponse.ok) {
    const xml = await uploadResponse.text();
    console.error("S3 error:", xml);
    throw new Error(`Upload failed ${uploadResponse.status}`);
  }
  return true;
}
