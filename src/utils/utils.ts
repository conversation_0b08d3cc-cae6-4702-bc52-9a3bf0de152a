export const displayConsultationStatus = (status: string) => {
  switch (status) {
    case "created":
      return "Pending";
    case "started":
      return "Pending";
    case "completed":
      return "Pending";
    case "submitted":
      return "Ready for Billing";
    default:
      return status;
  }
};

export const displayDate = (date: string) => {
  const d = new Date(date);
  return d.toLocaleDateString("en-US", {
    year: "numeric",
    month: "numeric",
    day: "numeric",
  });
};

export const displayTime = (time: string) => {
  const d = new Date(time);
  return d.toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "numeric",
    hour12: true,
  });
};

export const displayPhoneNumber = (phone: string) => {
  if (!phone) return "";
  const cleaned = phone.replace(/\D/g, "");
  const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
  if (match) {
    return `(${match[1]}) ${match[2]}-${match[3]}`;
  }
  return phone;
};
