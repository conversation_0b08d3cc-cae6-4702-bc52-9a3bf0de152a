import { useQuery } from "@tanstack/react-query";
import axios from "../services/axiosConfig";

export interface Patient {
  id: string;
  name: string;
  dob: string;
}

export const usePatients = (facilityId: string, patientName: string) => {
  return useQuery<Patient[], Error>({
    queryKey: ["patients", facilityId, patientName],
    queryFn: async () => {
      const response = await axios.get(
        `/clinic/patients?facilityId=${facilityId}&patientName=${patientName}`
      );
      return response.data.patients;
    },
    staleTime: 10 * 60 * 1000,
    enabled: !!facilityId,
  });
};
