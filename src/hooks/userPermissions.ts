// hooks/usePermissionStatus.ts
import { useEffect, useState } from "react";
import {
  check,
  request,
  openSettings,
  PermissionStatus,
  RESULTS,
  PERMISSIONS,
  // Platform,
} from "react-native-permissions";
import { Platform as RNPlatform } from "react-native";

type Status = "granted" | "denied" | "blocked" | "unavailable";

const getReadableStatus = (status: PermissionStatus): Status => {
  switch (status) {
    case RESULTS.GRANTED:
      return "granted";
    case RESULTS.BLOCKED:
      return "blocked";
    case RESULTS.DENIED:
      return "denied";
    default:
      return "unavailable";
  }
};

export const usePermissionStatus = () => {
  const [camera, setCamera] = useState<Status>("unavailable");
  const [mic, setMic] = useState<Status>("unavailable");

  const cameraPerm =
    RNPlatform.OS === "ios"
      ? PERMISSIONS.IOS.CAMERA
      : PERMISSIONS.ANDROID.CAMERA;

  const micPerm =
    RNPlatform.OS === "ios"
      ? PERMISSIONS.IOS.MICROPHONE
      : PERMISSIONS.ANDROID.RECORD_AUDIO;

  const refresh = async () => {
    const camStatus = getReadableStatus(await check(cameraPerm));
    const micStatus = getReadableStatus(await check(micPerm));
    setCamera(camStatus);
    setMic(micStatus);
  };

  const requestPermission = async (type: "camera" | "mic") => {
    const perm = type === "camera" ? cameraPerm : micPerm;
    const result = getReadableStatus(await request(perm));
    if (type === "camera") setCamera(result);
    else setMic(result);
  };

  const openAppSettings = () => {
    openSettings().catch(() => console.warn("Could not open settings"));
  };

  useEffect(() => {
    refresh();
  }, []);

  return {
    camera,
    mic,
    requestPermission,
    openAppSettings,
    refresh,
  };
};
