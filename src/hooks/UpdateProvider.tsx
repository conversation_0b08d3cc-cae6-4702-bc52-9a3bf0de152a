// src/components/UpdateProvider.tsx
import * as Updates from "expo-updates";
import { useEffect, useState, useRef } from "react";
import { Alert, AppState, AppStateStatus, Platform, View } from "react-native";
import InAppUpdates, { IAUUpdateKind } from "sp-react-native-in-app-updates";
import { ActivityIndicator } from "react-native";
import * as Application from "expo-application";
import axiosConfig from "~/services/axiosConfig";

// Instantiate once for efficiency
const inAppUpdates = new InAppUpdates(__DEV__);
const currentVersion = Application.nativeApplicationVersion || "1.0.2";

const LoadingScreen = () => (
  <View
    style={{
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      backgroundColor: "#ffffff",
    }}
  >
    <ActivityIndicator size="large" color="#0000ff" />
  </View>
);

const UpdateProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [isChecking, setIsChecking] = useState(false);
  const lastCheckTimestamp = useRef<number | null>(null);

  const checkForUpdates = async () => {
    // Skip updates during development
    if (__DEV__) {
      return;
    }

    const now = Date.now();
    // Skip if checked within the last 5 minutes
    if (
      lastCheckTimestamp.current &&
      now - lastCheckTimestamp.current < 5 * 60 * 1000
    ) {
      return;
    }

    // Set timestamp and loading state early to throttle even on errors
    lastCheckTimestamp.current = now;
    setIsChecking(true);

    try {
      // Check for OTA update
      if (Updates.isEnabled) {
        const update = await Updates.checkForUpdateAsync();
        if (update.isAvailable) {
          await Updates.fetchUpdateAsync();
          Alert.alert(
            "Update Available",
            "A new version of VitalCare is ready.",
            [{ text: "Apply Update", onPress: () => Updates.reloadAsync() }],
            { cancelable: false }
          );
          return; // exit early to avoid store update prompt
        }
      }

      // Check for App Store update
      const result = await inAppUpdates.checkNeedsUpdate({
        curVersion: currentVersion,
      });
      if (result?.shouldUpdate) {
        const updateOptions =
          Platform.OS === "ios" ? {} : { updateType: IAUUpdateKind.FLEXIBLE };
        Alert.alert(
          "New Version Available",
          "A new version of VitalCare is available in the App Store. Update now?",
          [
            { text: "Later", style: "cancel" },
            {
              text: "Update",
              onPress: () => inAppUpdates.startUpdate(updateOptions),
            },
          ],
          { cancelable: true }
        );
      }
    } catch (error: unknown) {
      try {
        const eventBody = {
          level: "ERROR",
          name: "check-for-updates",
          message: "Error during update check",
          metadata: JSON.stringify(error),
        };
        await axiosConfig.post("/log-event", eventBody);
      } catch {
        console.error("Failed to log update check error");
      }
      if (__DEV__) {
        console.error("Update Error:", error);
      }
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    checkForUpdates();
    const subscription = AppState.addEventListener(
      "change",
      (state: AppStateStatus) => {
        if (state === "active") {
          checkForUpdates();
        }
      }
    );
    return () => {
      subscription.remove();
    };
  }, []);

  return isChecking ? <LoadingScreen /> : <>{children}</>;
};

export default UpdateProvider;
