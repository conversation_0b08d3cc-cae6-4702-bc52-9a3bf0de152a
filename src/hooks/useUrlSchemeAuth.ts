import { useEffect, useState } from "react";
import { Linking, Platform, AppState, AppStateStatus } from "react-native";
import { useRouter } from "expo-router";
import * as SecureStore from "expo-secure-store";
import { useAuth } from "../context/AuthContext";
import axiosConfig from "../services/axiosConfig";

/**
 * Hook to handle URL scheme authentication
 * This hook will listen for URL scheme events and handle authentication
 * when the app is opened via a URL scheme with requireAuth=true
 */
export const useUrlSchemeAuth = () => {
  const router = useRouter();
  const { user, getUser } = useAuth();
  const [pendingAuthUrl, setPendingAuthUrl] = useState<string | null>(null);
  const [appState, setAppState] = useState<AppStateStatus>(
    AppState.currentState
  );

  // Function to handle URL scheme events
  const handleUrl = async (event: { url: string }) => {
    try {
      console.log(`[useUrlSchemeAuth] URL scheme event: ${event.url}`);

      // Log the URL scheme event
      const eventBody = {
        level: "INFO",
        status: "url_scheme",
        name: "useUrlSchemeAuth",
        message: `URL scheme event: ${event.url}`,
        metadata: { url: event.url, platform: Platform.OS },
      };
      axiosConfig
        .post("/log-event", eventBody)
        .catch((err) => console.error("Failed to log URL scheme event:", err));

      // Parse the URL
      const url = new URL(event.url);
      const requireAuth = url.searchParams.get("requireAuth");
      const path = url.pathname;

      console.log(
        `[useUrlSchemeAuth] URL path: ${path}, requireAuth: ${requireAuth}`
      );

      // If requireAuth is true, check authentication
      if (requireAuth === "true") {
        // Log authentication check
        const authCheckEventBody = {
          level: "INFO",
          status: "auth_check",
          name: "useUrlSchemeAuth",
          message: "Checking authentication for URL scheme",
          metadata: {
            url: event.url,
            user: user ? "exists" : "null",
            userRole: user?.role || "unknown",
            path,
          },
        };
        axiosConfig
          .post("/log-event", authCheckEventBody)
          .catch((err) =>
            console.error("Failed to log auth check event:", err)
          );

        // If user is already authenticated, navigate to the dashboard
        if (user) {
          console.log(
            `[useUrlSchemeAuth] User already authenticated, role: ${user.role}`
          );

          // User is already authenticated, navigate to the dashboard
          if (user.role === "provider") {
            console.log("[useUrlSchemeAuth] Navigating to provider dashboard");
            router.replace("/provider/dashboard");
          } else if (user.role === "nurse") {
            console.log("[useUrlSchemeAuth] Navigating to nurse dashboard");
            router.replace("/nurse/dashboard");
          }
          return;
        }

        console.log(
          "[useUrlSchemeAuth] User not authenticated, checking for token"
        );

        // Check if we have a token in secure storage
        try {
          const token = await SecureStore.getItemAsync("authToken");
          if (token) {
            console.log(
              "[useUrlSchemeAuth] Found token in secure storage, retrieving user data"
            );

            try {
              // We have a token, try to get the user
              const userData = await getUser();

              // Log user data retrieval
              const userDataEventBody = {
                level: "INFO",
                status: "user_retrieved",
                name: "useUrlSchemeAuth",
                message: "Retrieved user data from token",
                metadata: {
                  url: event.url,
                  userExists: userData ? "true" : "false",
                  role: userData?.role || "unknown",
                  tokenExists: "true",
                },
              };
              axiosConfig
                .post("/log-event", userDataEventBody)
                .catch((err) =>
                  console.error("Failed to log user data event:", err)
                );

              // If we got user data, navigate to the dashboard
              if (userData) {
                console.log(
                  `[useUrlSchemeAuth] Successfully retrieved user data, role: ${userData.role}`
                );

                if (userData.role === "provider") {
                  console.log(
                    "[useUrlSchemeAuth] Navigating to provider dashboard"
                  );
                  router.replace("/provider/dashboard");
                } else if (userData.role === "nurse") {
                  console.log(
                    "[useUrlSchemeAuth] Navigating to nurse dashboard"
                  );
                  router.replace("/nurse/dashboard");
                }
              } else {
                // Token is invalid, navigate to login
                console.log(
                  "[useUrlSchemeAuth] Failed to retrieve user data, navigating to login"
                );
                router.replace("/");
              }
            } catch (error) {
              console.error(
                "[useUrlSchemeAuth] Error retrieving user data:",
                error
              );

              // Check if the error is related to device being locked
              if (
                error instanceof Error &&
                (error.message.includes("User interaction is not allowed") ||
                  error.message.includes(
                    "Calling the 'getValueWithKeyAsync' function has failed"
                  ))
              ) {
                console.log(
                  "[useUrlSchemeAuth] Device appears to be locked, saving URL for later processing"
                );

                // Save the URL for processing when the app becomes active (device unlocked)
                setPendingAuthUrl(event.url);

                // Log the pending auth
                const pendingAuthEventBody = {
                  level: "INFO",
                  status: "pending_auth",
                  name: "useUrlSchemeAuth",
                  message: "Device locked, saving URL for later processing",
                  metadata: { url: event.url },
                };
                axiosConfig
                  .post("/log-event", pendingAuthEventBody)
                  .catch((err) =>
                    console.error("Failed to log pending auth event:", err)
                  );

                // Don't navigate to login yet, wait for device to be unlocked
                return;
              }

              // For other errors, navigate to login
              router.replace("/");
            }
          } else {
            // No token, navigate to login
            console.log(
              "[useUrlSchemeAuth] No token found, navigating to login"
            );
            router.replace("/");
          }
        } catch (error) {
          console.error(
            "[useUrlSchemeAuth] Error accessing secure storage:",
            error
          );

          // Check if the error is related to device being locked
          if (
            error instanceof Error &&
            (error.message.includes("User interaction is not allowed") ||
              error.message.includes(
                "Calling the 'getValueWithKeyAsync' function has failed"
              ))
          ) {
            console.log(
              "[useUrlSchemeAuth] Device appears to be locked, saving URL for later processing"
            );

            // Save the URL for processing when the app becomes active (device unlocked)
            setPendingAuthUrl(event.url);

            // Log the pending auth
            const pendingAuthEventBody = {
              level: "INFO",
              status: "pending_auth",
              name: "useUrlSchemeAuth",
              message: "Device locked, saving URL for later processing",
              metadata: { url: event.url },
            };
            axiosConfig
              .post("/log-event", pendingAuthEventBody)
              .catch((err) =>
                console.error("Failed to log pending auth event:", err)
              );

            // Don't navigate to login yet, wait for device to be unlocked
            return;
          }

          // For other errors, navigate to login
          router.replace("/");
        }
      }
    } catch (error) {
      // Log any errors
      console.error("[useUrlSchemeAuth] Error handling URL scheme:", error);

      const errorEventBody = {
        level: "ERROR",
        status: "url_scheme_error",
        name: "useUrlSchemeAuth",
        message: `Error handling URL scheme: ${error}`,
        metadata: {
          url: event.url,
          error: String(error),
          stack: error instanceof Error ? error.stack : "No stack trace",
        },
      };
      axiosConfig
        .post("/log-event", errorEventBody)
        .catch((err) => console.error("Failed to log error event:", err));

      // Navigate to login on error
      router.replace("/");
    }
  };

  // Handle app state changes (active, background, inactive)
  useEffect(() => {
    console.log("[useUrlSchemeAuth] Setting up AppState listener");

    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      console.log(
        `[useUrlSchemeAuth] App state changed from ${appState} to ${nextAppState}`
      );

      // Log app state change
      const appStateEventBody = {
        level: "INFO",
        status: "app_state_change",
        name: "useUrlSchemeAuth",
        message: `App state changed from ${appState} to ${nextAppState}`,
        metadata: {
          previousState: appState,
          newState: nextAppState,
          pendingAuthUrl: pendingAuthUrl ? "exists" : "none",
        },
      };
      axiosConfig
        .post("/log-event", appStateEventBody)
        .catch((err) => console.error("Failed to log app state event:", err));

      // If app becomes active and we have a pending auth URL, process it
      if (
        nextAppState === "active" &&
        pendingAuthUrl &&
        appState !== "active"
      ) {
        console.log(
          "[useUrlSchemeAuth] App became active with pending auth URL, processing"
        );

        // Process the pending auth URL
        setTimeout(() => {
          handleUrl({ url: pendingAuthUrl });
          setPendingAuthUrl(null);
        }, 1000); // Small delay to ensure the app is fully active
      }

      setAppState(nextAppState);
    };

    // Subscribe to app state changes
    const appStateSubscription = AppState.addEventListener(
      "change",
      handleAppStateChange
    );

    return () => {
      console.log("[useUrlSchemeAuth] Cleaning up AppState listener");
      appStateSubscription.remove();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [appState, pendingAuthUrl]);

  // Handle URL scheme events
  useEffect(() => {
    console.log("[useUrlSchemeAuth] Setting up URL scheme listener");

    // Log initialization
    const initEventBody = {
      level: "INFO",
      status: "init",
      name: "useUrlSchemeAuth",
      message: "Initializing URL scheme listener",
      metadata: {
        userExists: user ? "true" : "false",
        userRole: user?.role || "unknown",
        platform: Platform.OS,
        appState: AppState.currentState,
      },
    };
    axiosConfig
      .post("/log-event", initEventBody)
      .catch((err) => console.error("Failed to log init event:", err));

    // Add event listener for URL scheme events
    const subscription = Linking.addEventListener("url", handleUrl);

    // Check if app was opened with URL scheme
    Linking.getInitialURL()
      .then((url) => {
        console.log(`[useUrlSchemeAuth] Initial URL: ${url || "none"}`);

        if (url) {
          console.log(`[useUrlSchemeAuth] App was opened with URL: ${url}`);
          handleUrl({ url });
        }
      })
      .catch((error) => {
        console.error("[useUrlSchemeAuth] Error getting initial URL:", error);
      });

    // Clean up
    return () => {
      console.log("[useUrlSchemeAuth] Cleaning up URL scheme listener");
      subscription.remove();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);

  return null;
};
