import { useQuery } from "@tanstack/react-query";
import axiosConfig from "../services/axiosConfig";

type BillingCode = {
  id: string;
  code: string;
  description: string;
  // Add more fields as needed
};

export const useBillingCodes = (query: string) => {
  return useQuery<BillingCode[]>({
    queryKey: ["billingCodes", query],
    queryFn: async () => {
      const response = await axiosConfig.get(`/billing/codes?query=${query}`);
      return response.data.icd10Codes.map(
        ([code, description]: [string, string], index: number) => ({
          id: `${code}-${index}`,
          code,
          description,
        })
      );
    },
    enabled: !!query,
    staleTime: 5 * 60 * 1000,
  });
};
