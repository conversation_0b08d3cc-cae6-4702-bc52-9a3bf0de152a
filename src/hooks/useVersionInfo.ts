import { useState } from "react";
import * as Updates from "expo-updates";
import * as Application from "expo-application";
import { Platform, Linking } from "react-native";
import InAppUpdates from "sp-react-native-in-app-updates";

// Instantiate once for efficiency
const inAppUpdates = new InAppUpdates(__DEV__);

export const useVersionInfo = () => {
  const [isReloading, setIsReloading] = useState(false);

  // Get the current app version
  const getAppVersion = (): string => {
    return (
      Platform.select({
        ios: Application.nativeApplicationVersion,
        android: Application.nativeApplicationVersion,
        default: Application.nativeApplicationVersion,
      }) ?? "1.0.0"
    );
  };

  // Get the current patch/update ID
  const getPatchId = (): string => {
    try {
      if (Updates.isEnabled) {
        // Try to get update ID from the current update
        const currentUpdate = Updates.updateId;
        if (currentUpdate) {
          // Return first 8 characters of the update ID for display
          return currentUpdate.substring(0, 8);
        }

        // Fallback: try to get from manifest (deprecated but may still work)
        if (Updates.manifest?.id) {
          return Updates.manifest.id.substring(0, 8);
        }
      }
      console.log("No patch ID available");
      return "None";
    } catch (error) {
      console.log("Error getting patch ID:", error);
      return "None";
    }
  };

  // Reload the app
  const reloadApp = async (): Promise<void> => {
    setIsReloading(true);
    try {
      if (Updates.isEnabled) {
        await Updates.reloadAsync();
      } else {
        // If Updates is not enabled, just show a message
        console.log("Updates not enabled, cannot reload app");
      }
    } catch (error) {
      console.error("Error reloading app:", error);
    } finally {
      setIsReloading(false);
    }
  };

  // Open the App Store to check for updates
  const openAppStore = async (): Promise<void> => {
    try {
      const appId = "com.austinr47.VITALCARE";

      const url =
        Platform.OS === "ios"
          ? `https://apps.apple.com/us/app/vitalcare-health/id6744088872`
          : `market://details?id=${appId}`;

      const canOpen = await Linking.canOpenURL(url);

      if (canOpen) {
        await Linking.openURL(url);
      } else {
        // Fallback for Android if Google Play isn't available
        if (Platform.OS === "android") {
          await Linking.openURL(
            `https://play.google.com/store/apps/details?id=${appId}`
          );
        }
      }
    } catch (error) {
      console.error("Error opening app store:", error);
    }
  };

  return {
    appVersion: getAppVersion(),
    patchId: getPatchId(),
    reloadApp,
    openAppStore,
    isReloading,
  };
};
