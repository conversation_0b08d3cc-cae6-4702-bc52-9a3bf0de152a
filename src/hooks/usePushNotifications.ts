import { useEffect, useRef, useState } from "react";
import {
  Platform,
  AppState,
  Alert,
  Linking,
  Vibration,
  PermissionsAndroid,
} from "react-native";
import messaging, {
  AuthorizationStatus,
  FirebaseMessagingTypes,
} from "@react-native-firebase/messaging";
import notifee, {
  AndroidImportance,
  AndroidStyle,
} from "@notifee/react-native";
import { router, usePathname } from "expo-router";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Sound from "react-native-sound";
import axios from "../services/axiosConfig";
import { useConsultationRequest } from "~/context/ConsultationRequestContext";
import DeviceInfo from "react-native-device-info";
import { useAuth } from "~/context/AuthContext";

// Helper to stop alert (sound + vibration)
const stopAlert = (
  soundRef: React.MutableRefObject<Sound | null>,
  timerRef: React.MutableRefObject<NodeJS.Timeout | null>
) => {
  soundRef.current?.stop();
  soundRef.current?.release();
  soundRef.current = null;
  if (timerRef.current) clearTimeout(timerRef.current);
  timerRef.current = null;
  Vibration.cancel();
};

// Hook: manages ringtone playback and vibration
const useRingtone = () => {
  const soundRef = useRef<Sound | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    Sound.setCategory("Playback");
    return () => stopAlert(soundRef, timerRef);
  }, []);

  const play = () => {
    stopAlert(soundRef, timerRef);
    Vibration.vibrate([0, 500, 200, 500], true);
    const file = Platform.OS === "ios" ? "ringtone.caf" : "ringtone.mp3";
    soundRef.current = new Sound(file, Sound.MAIN_BUNDLE, (err) => {
      if (err) return;
      soundRef.current?.setNumberOfLoops(-1);
      soundRef.current?.setVolume(1);
      soundRef.current?.play();
      timerRef.current = setTimeout(() => stopAlert(soundRef, timerRef), 30000);
    });
  };

  return { play, stop: () => stopAlert(soundRef, timerRef) };
};

// Hook: requests permissions and updates push token
const useFirebaseMessaging = () => {
  const { user } = useAuth();
  const [token, setToken] = useState<string | null>(null);

  useEffect(() => {
    let mounted = true;

    const updatePushToken = async (
      userId: string,
      pushToken: string,
      deviceType: string
    ) => {
      if (!userId) return;
      try {
        console.log("Updating push token:", pushToken);
        await axios.post("/user/push-token", { userId, pushToken, deviceType });
        console.log("Push token updated:", pushToken);
      } catch (error) {
        console.error("Error updating push token:", error);
      }
    };

    const setup = async () => {
      // Android notification permission
      if (Platform.OS === "android" && Platform.Version >= 33) {
        const hasPermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
        );
        if (!hasPermission) {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
          );
          if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
            console.warn("Notification permission not granted.");
            return;
          }
        }
      }

      // Firebase permission
      const authStatus = await messaging().requestPermission();
      if (authStatus < AuthorizationStatus.AUTHORIZED) {
        if (Platform.OS === "ios") {
          const settings = await notifee.requestPermission({
            sound: true,
            alert: true,
            badge: true,
            provisional: true,
          });
          if (!settings.authorizationStatus) {
            Alert.alert(
              "Notifications Required",
              "Please enable notifications in Settings.",
              [
                {
                  text: "Open Settings",
                  onPress: () => Linking.openURL("app-settings:"),
                },
                { text: "Later", style: "cancel" },
              ]
            );
          }
        } else {
          await notifee.requestPermission();
        }
      }

      // Register and fetch token
      await messaging().registerDeviceForRemoteMessages();
      const fcmToken = await messaging().getToken();
      if (mounted && fcmToken) {
        setToken(fcmToken);
        await updatePushToken(
          user?.id || "",
          fcmToken,
          DeviceInfo.getSystemName()
        );
      }
    };

    setup();
    return () => {
      mounted = false;
    };
  }, [user?.id]);

  return token;
};

// Hook: handles incoming notifications and routing
const useNotificationRouting = (playRingtone: () => void) => {
  const { setRequestQueue, fromNotification } = useConsultationRequest();
  const pathname = usePathname();
  const lastId = useRef<string | null>(null);

  useEffect(() => {
    AsyncStorage.setItem("currentPathname", pathname || "");
  }, [pathname]);

  const handleTap = (remote: FirebaseMessagingTypes.RemoteMessage) => {
    if (remote.messageId === lastId.current) return;
    lastId.current = remote.messageId ?? null;
    setTimeout(() => {
      lastId.current = null;
    }, 5000);

    const type = remote.data?.type;
    if (type === "call") {
      playRingtone();
      setRequestQueue((prev) => [
        ...prev,
        {
          consultationRequestId: String(
            remote.data?.consultationRequestId ?? ""
          ),
          facilityId: String(remote.data?.facilityId ?? ""),
          patientName: String(remote.data?.patientName ?? ""),
          patientDOB: String(remote.data?.patientDOB ?? ""),
          patientGender: String(remote.data?.patientGender ?? ""),
          location: String(remote.data?.location ?? ""),
          caller: String(remote.data?.caller ?? ""),
          chief_complaint: String(remote.data?.chief_complaint ?? ""),
          timeLeft: 30,
        },
      ]);
    } else if (type === "chat") {
      AsyncStorage.getItem("currentPathname").then((current) => {
        if (current !== "/provider/reviewcall") {
          router.push({
            pathname: "/provider/reviewcall",
            params: {
              consultationId: String(remote.data?.consultationId ?? ""),
            },
          });
        }
      });
    }
  };

  useEffect(() => {
    const unsubFg = messaging().onMessage((msg) => {
      if (msg.messageId !== lastId.current) {
        notifee.displayNotification({
          title: msg.notification?.title,
          body: msg.notification?.body,
          android: {
            channelId: "default",
            smallIcon: "ic_notification",
            importance: AndroidImportance.HIGH,
            style: {
              type: AndroidStyle.BIGTEXT,
              text: msg.notification?.body ?? "",
            },
          },
          ios: {
            sound: msg.data?.type === "call" ? "ringtone.caf" : "default",
            foregroundPresentationOptions: {
              alert: true,
              badge: true,
              sound: true,
            },
          },
        });
      }
    });

    const unsubBg = messaging().onNotificationOpenedApp(handleTap);
    messaging()
      .getInitialNotification()
      .then((remote) => remote && handleTap(remote));

    return () => {
      unsubFg();
      unsubBg();
    };
  }, []);
};

// Main hook
export const usePushNotifications = () => {
  const { play, stop } = useRingtone();
  const token = useFirebaseMessaging();
  useNotificationRouting(play);

  useEffect(() => {
    const sub = AppState.addEventListener("change", (state) =>
      state === "active" ? stop() : null
    );
    return () => sub.remove();
  }, []);

  return { token };
};
