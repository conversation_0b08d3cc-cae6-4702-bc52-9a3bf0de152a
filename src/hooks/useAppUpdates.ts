import { useState, useEffect } from "react";
import { AppState, AppStateStatus } from "react-native";
import {
  checkForUpdates,
  downloadAndReload,
  getAppVersion,
} from "../services/updates";

export const useAppUpdates = () => {
  const [isUpdateAvailable, setIsUpdateAvailable] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [currentVersion, setCurrentVersion] = useState<string | null>(null);
  const [newVersion, setNewVersion] = useState<string | null>(null);

  const checkUpdate = async () => {
    setIsChecking(true);
    try {
      const update = await checkForUpdates();
      if (update.isAvailable) {
        setIsUpdateAvailable(true);
        setNewVersion(update.version ?? null);
        setCurrentVersion(update.currentVersion ?? null);
      }
    } catch (error) {
      console.error("Error checking for updates:", error);
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    // Check for updates when app comes to foreground
    const subscription = AppState.addEventListener(
      "change",
      (nextAppState: AppStateStatus) => {
        if (nextAppState === "active") {
          checkUpdate();
        }
      }
    );

    // Initial check
    checkUpdate();

    return () => {
      subscription.remove();
    };
  }, []);

  return {
    isUpdateAvailable,
    isChecking,
    currentVersion,
    newVersion,
    downloadAndReload,
  };
};
