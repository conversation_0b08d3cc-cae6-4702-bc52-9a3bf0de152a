import { useCallback, useEffect, useState } from "react";
import { ConsultationType } from "src/types/consultationTypes";
import axiosConfig from "../services/axiosConfig";

export interface UseConsultationsResult {
  consultations: ConsultationResponse | null;
  loading: boolean;
  error: Error | null;
  loadMore: () => void;
  hasMore: boolean;
  refreshConsultations: () => void;
  updateConsultationOrder: (newOrder: string, consultationId: string) => void;
}

type ConsultationResponse = {
  completed: {
    data: ConsultationType[];
    pagination: {
      totalPages: number;
      totalItems: number;
      currentPage: number;
    };
  };
  pending: {
    data: ConsultationType[];
    pagination: {
      totalPages: number;
      totalItems: number;
      currentPage: number;
    };
  };
};

export const useConsultations = (
  searchQuery: string
): UseConsultationsResult => {
  const [consultations, setConsultations] =
    useState<ConsultationResponse | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const limit = 10; // Load 10 consultations per request

  const fetchConsultations = useCallback(
    async (pageNum: number, resetData = false) => {
      if (loading) return; // Avoid multiple requests
      setLoading(true);

      try {
        const response = await axiosConfig.get<{
          consultations: ConsultationResponse;
        }>("/consultation", {
          params: { page: pageNum, limit, search: searchQuery },
        });

        const newCompleted = response.data.consultations.completed.data;
        const newPending = response.data.consultations.pending.data;

        setConsultations((prev) => ({
          completed: {
            data: resetData
              ? newCompleted
              : [...(prev?.completed?.data ?? []), ...newCompleted],
            pagination: response.data.consultations.completed.pagination,
          },
          pending: {
            data: resetData
              ? newPending
              : [...(prev?.pending?.data ?? []), ...newPending],
            pagination: response.data.consultations.pending.pagination,
          },
        }));

        // Update hasMore based on pagination
        const totalCompletedPages =
          response.data.consultations.completed.pagination.totalPages;
        const totalPendingPages =
          response.data.consultations.pending.pagination.totalPages;
        setHasMore(
          pageNum < totalCompletedPages || pageNum < totalPendingPages
        );
      } catch (err) {
        setError(err as Error);
      } finally {
        setLoading(false);
      }
    },
    [loading, searchQuery]
  );

  const updateConsultationOrder = (
    newOrder: string,
    consultationId: string
  ) => {
    setConsultations((prev) => {
      if (!prev) return prev;

      const updateOrderInList = (list: ConsultationType[]) =>
        list.map((item) =>
          item.id === consultationId ? { ...item, order: newOrder } : item
        );

      return {
        completed: {
          ...prev.completed,
          data: updateOrderInList(prev.completed.data),
        },
        pending: {
          ...prev.pending,
          data: updateOrderInList(prev.pending.data),
        },
      };
    });
  };

  useEffect(() => {
    // Reset state when searchQuery changes
    setConsultations(null);
    setPage(1);
    fetchConsultations(1, true); // Reset data on search
  }, [searchQuery]);

  useEffect(() => {
    // Fetch more data when page changes
    if (page > 1) {
      fetchConsultations(page);
    }
  }, [page]);

  const loadMore = () => {
    if (hasMore && !loading) {
      setPage((prevPage) => prevPage + 1);
    }
  };
  const refreshConsultations = () => {
    setPage(1);
    fetchConsultations(1, true); // Reset to page 1 and fetch again
  };

  return {
    consultations,
    loading,
    error,
    loadMore,
    hasMore,
    refreshConsultations,
    updateConsultationOrder,
  };
};
