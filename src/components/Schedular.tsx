import { Button, Text, YStack } from "tamagui";

export default function Schedular() {
  return (
    <YStack {...Style.card}>
      <YStack {...Style.mainStack}>
        <Text {...Style.scheduleText}>Scheduling</Text>
        <Button {...Style.datePicker}>Date Picker</Button>
        <Text {...Style.levelText}>Level</Text>
        <Button {...Style.datePicker}>Primary</Button>
      </YStack>
    </YStack>
  );
}

const Style = {
  card: {
    flex: 1,
    backgroundColor: "$screenBackgroundcolor",
    borderColor: "$primaryBorderColor" as any,
    borderWidth: 1,
    borderRadius: 8,
    padding: 10,
  },
  mainStack: {
    marginInline: 10,
    marginBlock: 10,
  },

  scheduleText: {
    fontWeight: "600" as any,
    fontSize: 20,
    color: "$textcolor" as any,
    marginBottom: 10,
  },
  levelText: {
    fontWeight: "400" as any,
    fontSize: 14,
    color: "$textcolor" as any,
    marginBlockStart: 20,
  },
  datePicker: {
    size: "$3" as any,
    backgroundColor: "$confirmOrderBlue" as any,
    color: "$confirmOrderTextColor" as any,
  },
};
