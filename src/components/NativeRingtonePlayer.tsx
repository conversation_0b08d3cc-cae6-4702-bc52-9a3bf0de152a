import React, { useState } from "react";
import { Button, View, StyleSheet, Text, Platform, Alert } from "react-native";
import * as Notifications from "expo-notifications";

// Fallback approach using Expo Notifications
const playNotificationSound = async () => {
  if (Platform.OS === "ios") {
    await Notifications.presentNotificationAsync({
      content: {
        title: "Ringtone Test",
        body: "Testing notification sound",
        sound: "ringtone.caf", // Use the same sound file with correct extension
      },
      trigger: null,
    });
    return true;
  }
  return false;
};

/**
 * A component that uses the native module to play a ringtone
 * This will work even when the app is in the background
 */
export const NativeRingtonePlayer = () => {
  const [isPlaying, setIsPlaying] = useState(false);

  const toggleRingtone = async () => {
    if (Platform.OS !== "ios") {
      Alert.alert("Not Supported", "This feature is only available on iOS.");
      return;
    }

    try {
      if (isPlaying) {
        // We can't really stop the notification sound once it's played
        // But we can update the UI state
        setIsPlaying(false);
      } else {
        // Play the notification sound
        await playNotificationSound();
        setIsPlaying(true);

        // Show an explanation to the user
        Alert.alert(
          "Ringtone Test",
          "A notification with the ringtone sound has been triggered. In a real VoIP call, this sound would play for 30 seconds.",
          [{ text: "OK" }]
        );

        // Automatically set isPlaying to false after 5 seconds (since we can't control the actual sound duration)
        setTimeout(() => {
          setIsPlaying(false);
        }, 5000);
      }
    } catch (error) {
      console.error("Error playing notification sound:", error);
      Alert.alert("Error", "Failed to play the notification sound.");
    }
  };

  if (Platform.OS !== "ios") {
    return (
      <View style={styles.container}>
        <Text style={styles.title}>Ringtone Test</Text>
        <Text style={styles.description}>
          This feature is only available on iOS devices.
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Native Ringtone Test</Text>
      <Text style={styles.description}>
        Press the button below to test the 30-second ringtone that will play
        when a VoIP notification arrives. This uses native iOS code and will
        work even when the app is in the background.
      </Text>
      <Button
        title={isPlaying ? "Stop Ringtone" : "Test Ringtone"}
        onPress={toggleRingtone}
        color={isPlaying ? "#FF3B30" : "#007AFF"}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: "#f0f0f0",
    borderRadius: 8,
    marginVertical: 10,
    marginHorizontal: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: "#666",
    marginBottom: 16,
    lineHeight: 20,
  },
});
