import React, { useRef, useState, useEffect } from "react";
import { Button, View, StyleSheet, Text, Platform, Alert } from "react-native";
import Sound from "react-native-sound";
import { Vibration } from "react-native";

/**
 * A test component that provides a button to test the ringtone functionality
 * without requiring actual VoIP notifications.
 */
export const RingtoneTestButton = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const soundRef = useRef<Sound | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // This is a completely different approach that doesn't rely on sound files
  // It uses the React Native Vibration API to create a pattern of vibrations
  useEffect(() => {
    // Clean up on unmount
    return () => {
      if (soundRef.current) {
        soundRef.current.release();
      }
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      Vibration.cancel();
    };
  }, []);

  const playRingtone = () => {
    // If already playing, stop it
    if (isPlaying) {
      stopRingtone();
      return;
    }

    setIsPlaying(true);

    // Start vibration pattern (vibrate for 500ms, pause for 200ms, repeat)
    // This creates a ringtone-like pattern with vibration
    const pattern = [0, 500, 200, 500, 200, 500, 200, 500, 200, 500];
    Vibration.vibrate(pattern, true);

    console.log("Using vibration pattern as ringtone");

    // Show a message to the user
    Alert.alert(
      "Ringtone Test",
      "Vibration pattern is simulating a ringtone. This will continue for 30 seconds.",
      [{ text: "OK" }]
    );

    // Stop after 30 seconds
    timerRef.current = setTimeout(() => {
      stopRingtone();
    }, 30000);
  };

  // This function is no longer used as we've simplified the approach

  const stopRingtone = () => {
    // Stop and release sound
    if (soundRef.current) {
      soundRef.current.stop();
      soundRef.current.release();
      soundRef.current = null;
    }

    // Clear timer
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }

    // Stop vibration
    Vibration.cancel();

    setIsPlaying(false);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Ringtone Test</Text>
      <Text style={styles.description}>
        Press the button below to test the 30-second ringtone that will play
        when a notification arrives.
      </Text>
      <Button
        title={isPlaying ? "Stop Ringtone" : "Test Ringtone"}
        onPress={playRingtone}
        color={isPlaying ? "#FF3B30" : "#007AFF"}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: "#f0f0f0",
    borderRadius: 8,
    marginVertical: 10,
    marginHorizontal: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: "#666",
    marginBottom: 16,
    lineHeight: 20,
  },
});
