import { Button, Card, Input, Text, YStack } from "tamagui";
import Title from "./Title";
import { Key } from "@tamagui/lucide-icons";
import { useRouter } from "expo-router";

const style = {
  card: {
    marginTop: 5,
    borderColor: "$primaryBorderColor" as any,
    borderWidth: 1,
    borderRadius: 8,
    backgroundColor: "transparent" as any,
  },
  container: {
    paddingBlock: 10,
    paddingInline: 20,
  },
  sectionTitle: {
    fontWeight: 600 as any,
    fontSize: 20,
  },
  label: {
    fontWeight: 500 as any,
    fontSize: 14,
  },
  inputContainer: {
    marginBlockStart: 5,
  },
  configuredTag: {
    position: "absolute" as any,
    right: 10,
    top: "50%" as any,
    transform: [{ translateY: -15 }],
    borderRadius: 10,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  button: {
    marginBlockStart: 10,
    size: "$4" as any,
    backgroundColor: "$confirmOrderBlue" as any,
    borderColor: "$confirmOrderBorderCOlor" as any,
    color: "$confirmOrderTextColor" as any,
    fontSize: 14,
    fontWeight: 600 as any,
  },
  passwordInput: {
    backgroundColor: "transparent" as any,
    paddingRight: 40,
    editable: false,
    size: "$4" as any,
    fontSize: 16,
    fontWeight: 400 as any,
  },
};

export default function ChangePin() {
  const router = useRouter();
  const ChangeVerificationPin = () => {
    router.push("/nurse/verify-pin");
  };
  return (
    <YStack>
      <Card {...style.card}>
        <YStack {...style.container}>
          <Text {...style.sectionTitle}>Security</Text>
          <YStack marginBlockStart={20} marginBlockEnd={10}>
            <Text {...style.label}>Pin Code</Text>

            <YStack {...style.inputContainer}>
              <Input secureTextEntry {...style.passwordInput}>
                1234
              </Input>

              <YStack {...style.configuredTag}>
                <Title
                  text="Configured"
                  borderColor="#ABEFC6"
                  backgroundColor="#ECFDF3"
                />
              </YStack>
            </YStack>

            <Button
              {...style.button}
              icon={<Key size="$1" />}
              onPress={ChangeVerificationPin}
            >
              Change Pin Code
            </Button>
          </YStack>
        </YStack>
      </Card>
    </YStack>
  );
}
