import { useState } from "react";
import { In<PERSON>, <PERSON><PERSON>, XS<PERSON>ck, Y<PERSON><PERSON><PERSON>, <PERSON>, Spinner } from "tamagui";
import { useAuth } from "~/context/AuthContext";
import { Alert } from "react-native"; // Add this import

export default function EditProfile({ onClose }: { onClose: () => void }) {
  const { localUser, updateUser } = useAuth();
  const [firstName, setFirstName] = useState(localUser?.firstName);
  const [lastName, setLastName] = useState(localUser?.lastName);
  const [isLoading, setIsLoading] = useState(false);

  const handleUpdateUser = async () => {
    // Check if names haven't changed
    if (
      firstName === localUser?.firstName &&
      lastName === localUser?.lastName
    ) {
      Alert.alert(
        "No Changes",
        "Please update either First Name or Last Name",
        [{ text: "OK" }]
      );
      return;
    }

    // Check if fields are empty
    if (!firstName || !lastName) {
      Alert.alert(
        "Missing Fields",
        "Please fill in both First Name and Last Name",
        [{ text: "OK" }]
      );
      return;
    }

    setIsLoading(true); // Show loader
    try {
      await updateUser(firstName, lastName);
      onClose();
    } catch (error) {
      console.error("Error updating user:", error);
      Alert.alert("Error", "Failed to update profile. Please try again.", [
        { text: "OK" },
      ]);
    } finally {
      setIsLoading(false); // Hide loader
    }
  };

  return (
    <>
      <YStack>
        <Text {...styles.firstNametxt}>First name</Text>
        <Input
          {...styles.firstName}
          value={firstName}
          onChangeText={setFirstName}
        />
        <Text {...styles.lastNameText}>Last name</Text>
        <Input
          {...styles.firstName}
          value={lastName}
          onChangeText={setLastName}
        />
        <XStack {...styles.btnContainer}>
          <Button {...styles.btnCancel} onPress={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            {...styles.btnSave}
            onPress={handleUpdateUser}
            disabled={isLoading}
          >
            Save
          </Button>
        </XStack>
      </YStack>

      {/* Full-screen loader */}
      {isLoading && (
        <YStack {...styles.spinnerContainer}>
          <Spinner size="large" color="$primaryColor" />
        </YStack>
      )}
    </>
  );
}

const styles = {
  firstName: {
    marginBlock: 10,
    backgroundColor: "transparent" as any,
    fontSize: 16,
    fontWeight: 400 as any,
  },
  firstNametxt: {
    fontSize: 14,
    fontWeight: 500 as any,
  },
  lastNameText: { marginBlockStart: 10, fontSize: 14, fontWeight: 500 as any },
  btnContainer: { marginBlock: 10, justify: "flex-end" as any },
  btnCancel: {
    fontWeight: 600 as any,
    borderColor: "$primaryBorderColor" as any,
    marginInline: 10,
    size: "$3" as any,
    backgroundColor: "transparent" as any,
    fontSize: 14,
    colo: "$textcolor",
  },
  btnSave: {
    size: "$3" as any,
    backgroundColor: "$primaryColor" as any,
    color: "$buttonWhiteColor" as any,
    fontSize: 14,
    fontWeight: 600 as any,
  },
  spinnerContainer: {
    position: "absolute" as any,
    top: 0 as any,
    left: 0 as any,
    right: 0 as any,
    bottom: 0 as any,
    justifyContent: "center" as any,
    alignItems: "center" as any,
    zIndex: 1000 as any,
  },
};
