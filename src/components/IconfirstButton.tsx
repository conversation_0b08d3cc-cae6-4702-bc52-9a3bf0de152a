import { Button, Text } from "tamagui";

interface IconFirstButtonProps {
  backgroundColor?: string;
  borderColor?: string;
  borderWidth?: number;
  icon?: any;
  text?: string;
  textColor?: string;
  onPress?: () => void;
}

export default function IconFirstButton({
  backgroundColor = "white",
  borderColor = "black",
  borderWidth = 1,
  icon: Icon,
  text = "",
  textColor = "black",
  onPress,
}: IconFirstButtonProps) {
  const Buttonprops = {
    backgroundColor,
    alignItems: "center" as "center",
  };
  return (
    <Button
      {...Buttonprops}
      borderColor={borderColor as any}
      borderWidth={borderWidth}
      flexDirection="row"
      size={"$5"}
      onPress={onPress}
    >
      {Icon && <Icon size={"$1"} color={textColor} />}
      {text && (
        <Text color={textColor as any} fontWeight={600} fontSize={18}>
          {text}
        </Text>
      )}
    </Button>
  );
}
