import { displayConsultationStatus } from "src/utils/utils";
import { Text, XStack } from "tamagui";

const badgeStyles = {
  container: {
    backgroundColor: "#ECFDF3",
    borderRadius: 4,
    paddingHorizontal: 1,
    paddingVertical: 1,
    alignItems: "center",
    justifyContent: "center",
    height: 20,
    borderColor: "#ABEFC6" as "#ABEFC6",
    borderWidth: 1,
    minWidth: 60,
  },
  text: {
    fontSize: 12,
    fontWeight: "500" as any,
    color: "#000" as "#000",
    textAlign: "center",
  },
};

export function Badge({ status = "Pending" }: { status: string }) {
  return (
    <XStack {...badgeStyles.container}>
      <Text {...badgeStyles.text}>{displayConsultationStatus(status)}</Text>
    </XStack>
  );
}
