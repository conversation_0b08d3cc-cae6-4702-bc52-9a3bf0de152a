import { useState } from "react";
import { useTheme } from "@/_layout";
import { ChevronDown } from "@tamagui/lucide-icons";
import { Accordion, Square, Text, View, XStack, YStack } from "tamagui";
import { HorizontalDashedLine } from "./DashedLine";

interface RecentConsultationsProps {
  title: string;
  data: Record<string, any>[];
}

export default function RecentConsultations({
  title,
  data,
}: RecentConsultationsProps) {
  const [isOpen, setIsOpen] = useState(false);
  const styles = getStyles();
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";

  const formatLabel = (key: string) =>
    key
      .replace(/([A-Z])/g, " $1")
      .replace(/[-_]/g, " ")
      .replace(/^./, (str) => str.toUpperCase());

  const renderContent = () => {
    if (typeof data === "string") {
      return <Text style={{ padding: 10 }}>{data}</Text>;
    }

    if (Array.isArray(data) && data.length > 0) {
      return data.map((item, idx) => (
        <YStack
          key={idx}
          style={{
            paddingRight: 10,
            paddingLeft: 10,
            paddingTop: 10,
          }}
        >
          {Object.entries(item).map(([key, value]) => (
            <XStack
              key={key}
              style={{ marginBottom: 4, alignItems: "flex-start" }}
            >
              <Text style={{ fontWeight: "600", marginRight: 4 }}>
                {formatLabel(key)}:
              </Text>
              <Text style={{ flexShrink: 1 }}>{String(value)}</Text>
            </XStack>
          ))}
          {idx + 1 < data.length && (
            <HorizontalDashedLine style={{ marginTop: 10 }} />
          )}
        </YStack>
      ));
    }

    return <Text style={{ padding: 10 }}>No data available.</Text>;
  };

  return (
    <View {...styles.container}>
      <Accordion
        overflow="hidden"
        borderWidth={1}
        type="multiple"
        {...styles.accordion}
        borderColor={isDarkMode ? "#697586" : "#D0D5DD"}
      >
        <Accordion.Item value="a1">
          <Accordion.Trigger
            {...styles.trigger}
            onPress={() => setIsOpen(!isOpen)}
          >
            {({ open }: { open: boolean }) => (
              <XStack {...styles.headerRow}>
                <YStack>
                  <Text {...styles.headerText}>{title}</Text>
                </YStack>
                <Square animation="quick" rotate={open ? "180deg" : "0deg"}>
                  <ChevronDown size="$1" />
                </Square>
              </XStack>
            )}
          </Accordion.Trigger>
          <Accordion.HeightAnimator animation="medium">
            <Accordion.Content {...styles.content}>
              <YStack {...styles.innerContainer}>{renderContent()}</YStack>
            </Accordion.Content>
          </Accordion.HeightAnimator>
        </Accordion.Item>
      </Accordion>
    </View>
  );
}

const getStyles = () => {
  return {
    container: {
      marginBlockStart: 20,
    },
    accordion: {
      borderRadius: "$6" as "$6",
    },
    trigger: {
      flexDirection: "row" as "row",
      borderWidth: 0,
      backgroundColor: "$screenBackgroundcolor",
    },
    headerRow: {
      justifyContent: "space-between",
      flex: 1,
    },
    content: {
      animation: "medium" as "medium",
      exitStyle: { opacity: 0 },
      backgroundColor: "$screenBackgroundcolor",
    },
    innerContainer: {
      marginBlockStart: -30,
      marginInlineStart: -15,
    },
    headerText: {
      fontSize: 16,
      fontWeight: 600 as any,
    },
    inputText: {
      backgroundColor: "$screenBackgroundcolor" as any,
      borderWidth: 0,
      color: "$textcolor" as any,
      fontWeight: 300 as 300,
      fontSize: 14,
      textAlignVertical: "top" as any,
    },
  };
};
