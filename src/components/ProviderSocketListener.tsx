import { CircleX, Phone } from "@tamagui/lucide-icons";
import { useRouter } from "expo-router";
import React, { useEffect } from "react";
import {
  Alert,
  Modal,
  NativeModules,
  Platform,
  Vibration,
  View,
} from "react-native";
import { displayDate } from "src/utils/utils";
import { Button, Dialog, Text, XStack, YStack } from "tamagui";
import { useConsultationRequest } from "~/context/ConsultationRequestContext"; // Adjust path
import { useSocket } from "~/context/ProviderSocketContext";
import axiosConfig from "~/services/axiosConfig";
import CallKitService from "~/services/CallKitService";
import { CustomNotification } from "./CustomNotification";

interface ConsultationRequestData {
  consultationRequestId: string;
  facilityId: string;
  patientName: string;
  patientDOB: string;
  patientGender: string;
  location: string;
  caller: string;
  chief_complaint: string;
}

interface TimedConsultationRequestData extends ConsultationRequestData {
  timeLeft: number;
}

const ProviderSocketListener: React.FC = () => {
  const styles = useCallRequestDialogStyle();
  const { socket } = useSocket();
  const router = useRouter();
  const {
    requestQueue,
    setRequestQueue,
    fromNotification,
    setFromNotification,
  } = useConsultationRequest();
  const currentRequest = requestQueue.length > 0 ? requestQueue[0] : null;
  const [showCallAcceptedAlert, setShowCallAcceptedAlert] =
    React.useState(false);

  useEffect(() => {
    const fetchConsultations = async () => {
      try {
        const response = await axiosConfig.get("/consultation/pending");
        if (response.status === 200) {
          if (response.data.length === 0) {
            if (fromNotification) {
              // show message that no pending consultations
              setShowCallAcceptedAlert;
            }
            setFromNotification(false);
            return;
          }
          setFromNotification(false);
          setRequestQueue((prevQueue) => [
            ...response.data.map((item: ConsultationRequestData) => ({
              ...item,
              timeLeft: 30,
            })),
          ]);
        }
        console.log("Pending consultations:", response.data);
      } catch (error) {
        console.error("Error fetching consultations", error);
      }
    };
    fetchConsultations();
  }, [fromNotification]);

  useEffect(() => {
    if (requestQueue.length === 0) return;

    const interval = setInterval(() => {
      try {
        setRequestQueue((prevQueue) => {
          if (prevQueue.length === 0) {
            return prevQueue;
          }
          const updatedQueue = prevQueue
            .map((req) => ({ ...req, timeLeft: req.timeLeft - 1 }))
            .filter((req) => req.timeLeft > 0);

          if (updatedQueue.length === 0) {
            Vibration.cancel();
            if (Platform.OS === "ios") {
              CallKitService.endAllCalls()
                .then(() =>
                  console.log(
                    "Ended all CallKit calls after request expiration"
                  )
                )
                .catch((err) =>
                  console.error("Error ending CallKit calls:", err)
                );
            }
          }

          return updatedQueue;
        });
      } catch (error) {
        clearInterval(interval);
        Vibration.cancel();
      }
    }, 1000);

    return () => {
      clearInterval(interval);
    };
  }, [requestQueue.length, setRequestQueue]);

  useEffect(() => {
    if (!socket) return;
    socket.on("consultationRequest", (data: ConsultationRequestData) => {
      setRequestQueue((prevQueue) => [...prevQueue, { ...data, timeLeft: 30 }]);
    });
    socket.on("nurseRejoinedCall", (data: ConsultationRequestData) => {
      setRequestQueue((prevQueue) => [...prevQueue, { ...data, timeLeft: 30 }]);
    });

    return () => {
      socket.off("consultationRequest");
    };
  }, [socket, setRequestQueue]);

  useEffect(() => {
    if (!socket) return;
    socket.on(
      "consultationAcceptedByOther",
      (data: { consultationRequestId: string }) => {
        const consultationRequestId = data.consultationRequestId;

        setRequestQueue((prevQueue) => {
          if (prevQueue.length === 0) {
            return prevQueue;
          }

          const currentRequestId = prevQueue[0].consultationRequestId;

          if (currentRequestId === consultationRequestId) {
            setTimeout(() => {
              setRequestQueue([]);
            }, 0);
            return [];
          }
          return prevQueue;
        });
      }
    );

    return () => {
      socket.off("consultationAcceptedByOther");
    };
  }, [socket, setRequestQueue, currentRequest]);

  useEffect(() => {
    if (!socket) return;
    socket.on(
      "consultationAccepted",
      (data: { consultationId: string; consultationRequestId: string }) => {
        setRequestQueue((prevQueue) =>
          prevQueue.filter(
            (req) => req.consultationRequestId !== data.consultationRequestId
          )
        );
      }
    );
    return () => {
      socket.off("consultationAccepted");
    };
  }, [socket, setRequestQueue]);

  const handleDeclineRequest = async () => {
    try {
      Vibration.cancel();

      if (Platform.OS === "ios") {
        try {
          const activeCalls = await CallKitService.getActiveCalls();

          const result = await CallKitService.endAllCalls();

          setTimeout(async () => {
            const remainingCalls = await CallKitService.getActiveCalls();

            if (remainingCalls.length > 0) {
              if (
                Platform.OS === "ios" &&
                NativeModules.RingtoneModule &&
                typeof NativeModules.RingtoneModule.directEndAllCalls ===
                  "function"
              ) {
                try {
                  const directResult =
                    await NativeModules.RingtoneModule.directEndAllCalls();
                } catch (error) {}
              }
              const resetResult = await CallKitService.resetCallKitProvider();
            }
          }, 500);
        } catch (error) {}
      }

      setRequestQueue([]);
    } catch (error) {
      setRequestQueue([]);
      Vibration.cancel();
    }
  };

  const handleAcceptRequest = async () => {
    if (!currentRequest) return;

    if (
      currentRequest.consultationId &&
      !currentRequest.consultationRequestId
    ) {
      try {
        const consultationId = currentRequest.consultationId;
        const rejoinRes = await axiosConfig.get(
          `/consultation/rejoin-request/${consultationId}`
        );
        const { providerSDKJWT } = rejoinRes.data;
        Vibration.cancel();
        router.push({
          pathname: "/provider/CallContainer",
          params: {
            consultationId,
            sdkId: providerSDKJWT,
            isComingFromCall: "true",
          },
        });
        setRequestQueue([]);
        return;
      } catch (error) {
        console.error("Error during provider rejoin call:", error);
      }
    }

    try {
      Vibration.cancel();

      if (Platform.OS === "ios") {
        try {
          const activeCalls = await CallKitService.getActiveCalls();

          const result = await CallKitService.endAllCalls();

          setTimeout(async () => {
            const remainingCalls = await CallKitService.getActiveCalls();

            if (remainingCalls.length > 0) {
              if (
                Platform.OS === "ios" &&
                NativeModules.RingtoneModule &&
                typeof NativeModules.RingtoneModule.directEndAllCalls ===
                  "function"
              ) {
                try {
                  const directResult =
                    await NativeModules.RingtoneModule.directEndAllCalls();
                } catch (error) {}
              }

              const resetResult = await CallKitService.resetCallKitProvider();
            }
          }, 500);
        } catch (error) {}
      }

      const response = await axiosConfig.post("/consultation/accept", {
        consultationRequestId: currentRequest.consultationRequestId,
      });
      console.log("Response from accept request:", response.status);

      if (response.status === 409) {
        console.log("Consultation already accepted by another provider");
        Alert.alert(
          "Consultation already accepted",
          "This consultation has already been accepted by another provider.",
          [{ text: "OK" }]
        );
        setRequestQueue((prevQueue) =>
          prevQueue.filter(
            (req) =>
              req.consultationRequestId !== currentRequest.consultationRequestId
          )
        );
        return;
      }

      const consultationId = response.data.consultationId;

      setRequestQueue([]);
      Vibration.cancel();
      const result = await axiosConfig.put(
        `/consultation/start/${consultationId}`
      );
      const { sdkJWT } = result.data;

      router.push({
        pathname: "/provider/CallContainer",
        params: { consultationId, sdkId: sdkJWT },
      });
    } catch (error: any) {
      if (error.response && error.response.status === 409) {
        Alert.alert(
          "Consultation not available",
          "This consultation has either been accepted by another provider.",
          [{ text: "OK" }]
        );
      } else {
        Alert.alert(
          "Error",
          "An error occurred while accepting the consultation request.",
          [{ text: "OK" }]
        );
      }
      setRequestQueue((prevQueue) =>
        prevQueue.filter(
          (req) =>
            req.consultationRequestId !== currentRequest.consultationRequestId
        )
      );
      Vibration.cancel();
    }
  };

  const renderInfoBlock = (label: string, value: string) => (
    <YStack marginBlockStart={20}>
      <Text {...styles.infoBlockLabel}>{label}:</Text>
      <Text {...styles.infoBlockValue}>{value}</Text>
    </YStack>
  );

  if (showCallAcceptedAlert) {
    <CustomNotification
      message="Call no longer available."
      visible={showCallAcceptedAlert}
      onHide={() => setShowCallAcceptedAlert(false)}
    />;
  }

  if (!currentRequest) {
    return null;
  }

  return (
    <Modal
      visible={!!currentRequest}
      transparent
      animationType="fade"
      onRequestClose={handleDeclineRequest}
    >
      <View
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          flex: 1,
          backgroundColor: "rgba(0, 0, 0, 0.5)",
          height: "100%",
          width: "100%",
        }}
      >
        <Dialog
          modal
          open={!!currentRequest}
          onOpenChange={handleDeclineRequest}
        >
          <Dialog.Overlay {...styles.overlay} />
          <Dialog.Content {...styles.dialogContent}>
            <YStack {...styles.container}>
              {currentRequest && (
                <>
                  <XStack {...styles.headerContainer}>
                    <Text {...styles.headerText}>Call Request</Text>
                    <Text {...styles.callEndsText}>
                      Call ends: {currentRequest.timeLeft}
                    </Text>
                  </XStack>
                  <YStack style={{ maxHeight: 400, overflow: "scroll" }}>
                    {renderInfoBlock("Caller", currentRequest.caller)}
                    {renderInfoBlock("Location", currentRequest.location)}
                    {renderInfoBlock(
                      "Patient",
                      `${currentRequest.patientName}${currentRequest.patientGender ? `, ${currentRequest.patientGender}` : ""}${currentRequest.patientDOB ? ` | DOB: ${displayDate(currentRequest.patientDOB)}` : ""}`
                    )}
                    {renderInfoBlock(
                      "Chief Complaint",
                      currentRequest.chief_complaint
                    )}
                  </YStack>
                  <Button
                    {...styles.button}
                    icon={<Phone size={"$1"} />}
                    onPress={handleAcceptRequest}
                  >
                    Accept Call
                  </Button>
                  <Button
                    {...styles.button}
                    icon={<CircleX size={"$1"} />}
                    onPress={handleDeclineRequest}
                  >
                    Decline Request
                  </Button>
                </>
              )}
            </YStack>
          </Dialog.Content>
        </Dialog>
      </View>
    </Modal>
  );
};

const useCallRequestDialogStyle = () => {
  return {
    dialogContent: {
      bordered: true,
      bg: "$screenBackgroundcolor" as any,
      br: "$4",
      shadowColor: "transparent" as any,
      shadowOpacity: 0,
      shadowRadius: 0,
      marginInline: 50,
      width: "90%" as any,
      alignSelf: "center",
      borderRadius: "$7",
    },
    container: {
      gap: "$2" as any,
      paddingHorizontal: "$4" as any,
    },
    headerContainer: {
      justifyContent: "space-between",
    },
    headerText: {
      fontSize: 24,
      fontWeight: "600" as any,
    },
    callEndsText: {
      fontSize: 16,
      fontWeight: "200" as any,
    },
    overlay: {
      animation: "quick" as any,
      enterStyle: { opacity: 0 },
      exitStyle: { opacity: 0 },
      backgroundColor: "$callAlertBackground" as any,
      opacity: 0.5,
    },
    infoBlockLabel: {
      fontSize: 18,
      fontWeight: "400" as any,
    },
    infoBlockValue: {
      fontSize: 18,
      fontWeight: "300" as any,
      marginBlockStart: 10,
    },
    button: {
      color: "$buttonWhiteColor" as any,
      backgroundColor: "$primaryColor" as any,
      marginBlock: 10,
      fontSize: 18,
      fontWeight: 500 as any,
    },
  };
};

export default ProviderSocketListener;
