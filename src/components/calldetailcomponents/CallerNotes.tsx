import { Card, Text, TextArea, YStack } from "tamagui";

export default function CallerNotes({
  value,
  onChangeText,
  isEditable = true,
}: {
  value: string;
  onChangeText?: (text: string) => void;
  isEditable: boolean;
}) {
  const callerNotesStyle = useCallerNotesStyle();

  const handleTextChange = (text: string) => {
    onChangeText?.(text);
  };

  return (
    <YStack {...callerNotesStyle.mainStack}>
      <Card>
        <YStack {...callerNotesStyle.container}>
          <Text {...callerNotesStyle.callerNotesText}>Call notes</Text>
          <YStack>
            <TextArea
              {...callerNotesStyle.writtenNotes}
              focusStyle={{ borderColor: "$primaryBorderColor" as any }}
              value={value}
              onChangeText={handleTextChange}
              placeholder="Please enter the details."
              placeholderTextColor={"$textcolor"}
              overflow="hidden"
              editable={isEditable}
            />
          </YStack>
        </YStack>
      </Card>
    </YStack>
  );
}

export const useCallerNotesStyle = () => {
  return {
    mainStack: {
      borderRadius: 11,
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 1,
      backgroundColor: "$screenBackgroundcolor" as any,
      paddingBlock: 15,
      paddingInline: 15,
    },
    generateSummaryBtn: {
      fontSize: 20 as 20,
      size: "$4" as "$4",
      fontWeight: "500" as "500",
      backgroundColor: "$confirmOrderBlue",
      borderColor: "$confirmOrderBorderCOlor" as any,
      color: "$confirmOrderTextColor" as any,
      borderWidth: 1,
    },
    starIcon: { width: 20, height: 20 },
    container: { backgroundColor: "$screenBackgroundcolor" as any },
    callerNotesText: {
      fontSize: 16,
      fontWeight: 600 as any,
      marginBlockEnd: 0,
    },
    writtenNotes: {
      backgroundColor: "$screenBackgroundcolor" as any,
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 1,
      marginBlockStart: 15,
      size: "$1" as "$1",
      bordeWidth: 1,
      color: "$textcolor" as any,
      fontWeight: 300 as 300,
      padding: 10,
      fontSize: 14,
      numberOfLines: 11,
      textAlignVertical: "top" as any,
      borderRadius: 7,
      height: 150,
    },
  };
};
