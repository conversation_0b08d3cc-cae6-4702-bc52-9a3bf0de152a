import React, { useEffect, useState } from "react";
import {
  Button,
  View,
  StyleSheet,
  Text,
  Platform,
  Alert,
  Vibration,
} from "react-native";
import { Audio } from "expo-av";

/**
 * A component that plays a ringtone using Expo's Audio API
 * This is a more reliable approach than using react-native-sound
 */
export const AudioPlayer = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [sound, setSound] = useState<Audio.Sound | null>(null);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (sound) {
        sound.unloadAsync();
      }
      Vibration.cancel();
    };
  }, [sound]);

  const playSound = async () => {
    // If already playing, stop it
    if (isPlaying) {
      stopSound();
      return;
    }

    try {
      console.log("Loading Sound...");
      setIsPlaying(true);

      // Start vibration as a fallback
      const pattern = [0, 500, 200, 500, 200, 500, 200, 500, 200, 500];
      Vibration.vibrate(pattern, true);

      // Initialize Audio
      await Audio.setAudioModeAsync({
        playsInSilentModeIOS: true,
        staysActiveInBackground: true,
        shouldDuckAndroid: true,
      });

      // Create a simple sound
      const { sound: newSound } = await Audio.Sound.createAsync(
        // Use a built-in system sound
        require("../../assets/sounds/ringtone.mp3"),
        {
          shouldPlay: true,
          isLooping: true,
          volume: 1.0,
        }
      );

      setSound(newSound);
      console.log("Sound playing!");

      // Stop after 30 seconds
      setTimeout(() => {
        stopSound();
      }, 30000);
    } catch (error) {
      console.error("Error playing sound:", error);
      Alert.alert(
        "Sound Error",
        "Failed to play ringtone. Using vibration only."
      );

      // Keep vibration going for 30 seconds
      setTimeout(() => {
        Vibration.cancel();
        setIsPlaying(false);
      }, 30000);
    }
  };

  const stopSound = async () => {
    console.log("Stopping sound...");

    // Stop and unload sound
    if (sound) {
      await sound.stopAsync();
      await sound.unloadAsync();
      setSound(null);
    }

    // Stop vibration
    Vibration.cancel();
    setIsPlaying(false);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Ringtone Test</Text>
      <Text style={styles.description}>
        Press the button below to test the 30-second ringtone that will play
        when a notification arrives.
      </Text>
      <Button
        title={isPlaying ? "Stop Ringtone" : "Test Ringtone"}
        onPress={playSound}
        color={isPlaying ? "#FF3B30" : "#007AFF"}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: "#f0f0f0",
    borderRadius: 8,
    marginVertical: 10,
    marginHorizontal: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: "#666",
    marginBottom: 16,
    lineHeight: 20,
  },
});
