import React from "react";
import { Input, Text, YStack } from "tamagui";

interface EmailAndPasswordProps {
  email: string;
  password: string;
  setEmail: (email: string) => void;
  setPassword: (password: string) => void;
}

export default function EmailAndPassword({
  email,
  password,
  setEmail,
  setPassword,
}: EmailAndPasswordProps) {
  const backgroundcolor = {
    backgroundColor: "$screenBackgroundcolor" as any,
    color: "$textcolor" as any,
  };
  return (
    <YStack mt={10}>
      <YStack>
        <Text fontSize={14} fontWeight="500">
          Email / Username
        </Text>
        <Input
          placeholder="Enter your email or username"
          marginBlockStart={6}
          inputMode="email"
          autoCapitalize="none"
          autoComplete="email"
          autoCorrect={false}
          submitBehavior="blurAndSubmit"
          {...backgroundcolor}
          fontSize={20}
          size="$5"
          value={email}
          onChangeText={setEmail}
          placeholderTextColor={"$placeholdertextColor"}
          borderColor={"$primaryBorderColor"}
          borderWidth={1}
        />
      </YStack>
      <YStack marginBlockStart={20}>
        <Text fontSize={14} fontWeight="500">
          Password
        </Text>
        <Input
          placeholder="••••••••••••"
          marginBlockStart={6}
          secureTextEntry
          autoCapitalize="none"
          {...backgroundcolor}
          fontSize={20}
          size="$5"
          value={password}
          onChangeText={setPassword}
          placeholderTextColor={"$placeholdertextColor"}
          borderColor={"$primaryBorderColor"}
          borderWidth={1}
        />
      </YStack>
    </YStack>
  );
}
