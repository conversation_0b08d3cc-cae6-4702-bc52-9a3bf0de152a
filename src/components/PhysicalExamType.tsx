import { Plus } from "lucide-react-native";
import React, { useEffect, useState } from "react";
import { FlatList } from "react-native";
import { Button, Input, Text, View, XStack } from "tamagui";
import { HorizontalDashedLine } from "./DashedLine";
import { X } from "@tamagui/lucide-icons";

interface PhysicalExamTypeProps {
  title: string;
  issues: { [key: string]: string };
  updatePhysicalExam: (title: string, issue: string, value: string) => void;
  isSubmitted: boolean;
  updateOtherIssues: (sectionTitle: string, newItem: string) => void;
  removeOtherItem: (sectionTitle: string, itemToRemove: string) => void;
}

const PhysicalExamType: React.FC<PhysicalExamTypeProps> = ({
  title,
  issues: initialIssues,
  updatePhysicalExam,
  isSubmitted = false,
  updateOtherIssues,
  removeOtherItem,
}) => {
  const styles = getStyles();
  const [issues, setIssues] = useState(initialIssues);
  const [activeButtons, setActiveButtons] = useState<{
    [key: string]: "plus" | "minus" | null;
  }>({});
  const [newIssue, setNewIssue] = useState("");

  const [otherItems, setOtherItems] = useState<string[]>(
    "Other" in initialIssues && Array.isArray(initialIssues.Other)
      ? initialIssues.Other
      : []
  );

  const handlePress = (issue: string, type: "plus" | "minus") => {
    const isAlreadyActive = activeButtons[issue] === type;
    const newState = {
      ...activeButtons,
      [issue]: isAlreadyActive ? null : type,
    };
    setActiveButtons(newState);

    const newValue = isAlreadyActive
      ? ""
      : type === "plus"
        ? "Positive"
        : "Negative";

    updatePhysicalExam(title, issue, newValue);
  };

  useEffect(() => {
    const initialActive: { [key: string]: "plus" | "minus" | null } = {};
    Object.keys(initialIssues).forEach((issue) => {
      if (initialIssues[issue] === "Positive") {
        initialActive[issue] = "plus";
      } else if (initialIssues[issue] === "Negative") {
        initialActive[issue] = "minus";
      } else {
        initialActive[issue] = null;
      }
    });
    setActiveButtons(initialActive);
  }, [initialIssues]);

  const handleAddIssue = () => {
    const trimmed = newIssue.trim();
    if (trimmed.length === 0) return;

    if ("Other" in initialIssues && Array.isArray(initialIssues.Other)) {
      updateOtherIssues(title, trimmed);
      setOtherItems((prev) => [...prev, trimmed]);
    }
    setNewIssue("");
  };

  const handleRemoveOtherItem = (itemToRemove: string) => {
    if (isSubmitted) return;
    removeOtherItem(title, itemToRemove);
    setOtherItems((prev) => prev.filter((item) => item !== itemToRemove));
  };

  const getButtonStyles = (issue: string, type: "plus" | "minus") => {
    const isActive = activeButtons[issue] === type;
    return {
      backgroundColor: isActive
        ? type === "plus"
          ? "$physicalExamPlusBackground"
          : "$physicalExamMinusBackground"
        : "$physicalExamItemColor",
      color: isActive ? "white" : ("black" as any),
    };
  };

  const renderItem = ({ item }: { item: string }) => {
    return (
      <XStack {...styles.issueContainer}>
        <Button
          disabled={isSubmitted}
          {...styles.actionButton}
          {...getButtonStyles(item, "plus")}
          onPress={() => handlePress(item, "plus")}
          pressStyle={{ ...styles.onBtnPress }}
        >
          +
        </Button>
        <Text {...styles.issueText}>{item}</Text>
        <Button
          disabled={isSubmitted}
          {...styles.actionButton}
          {...getButtonStyles(item, "minus")}
          onPress={() => handlePress(item, "minus")}
          pressStyle={{ ...styles.onBtnPress }}
        >
          -
        </Button>
      </XStack>
    );
  };
  const renderOtherItem = ({ item }: { item: string }) => {
    return (
      <XStack {...styles.issueContainer}>
        <Button
          disabled={true}
          {...styles.actionButton}
          {...getButtonStyles(item, "plus")}
          onPress={() => handlePress(item, "plus")}
          pressStyle={{ ...styles.onBtnPress }}
        ></Button>
        <Text {...styles.issueText}>{item}</Text>
        <Button
          disabled={isSubmitted}
          {...styles.actionButton}
          pressStyle={{ ...styles.onBtnPress }}
          icon={X}
          onPress={() => handleRemoveOtherItem(item)}
        ></Button>
      </XStack>
    );
  };

  return (
    <View {...styles.container}>
      <Text {...styles.titleText}>{title}</Text>
      <FlatList
        data={Object.keys(issues).filter((issue) => issue !== "Other")}
        renderItem={renderItem}
        keyExtractor={(item) => item}
        scrollEnabled={false}
      />
      {"Other" in issues && Array.isArray(issues.Other) && (
        <FlatList
          data={otherItems}
          renderItem={renderOtherItem}
          keyExtractor={(item, index) => `${item}-${index}`}
          scrollEnabled={false}
        />
      )}

      {!isSubmitted && (
        <XStack {...styles.addIssueContainer}>
          <Input
            {...styles.inputContainer}
            value={newIssue}
            onChangeText={setNewIssue}
            onSubmitEditing={handleAddIssue}
          />
          <Button icon={Plus} {...styles.addBtn} onPress={handleAddIssue}>
            Add
          </Button>
        </XStack>
      )}
      <HorizontalDashedLine
        height={1}
        dashLength={2}
        dashGap={2}
        color="#D2D6DB"
        style={{ marginTop: 16, marginBottom: 16 }}
      />
    </View>
  );
};

const getStyles = () => {
  return {
    container: {
      backgroundColor: "transparent",
    },
    onBtnPress: {
      backgroundColor: "$physicalExamItemColor" as any,
      borderWidth: 0,
      color: "black",
    },
    titleText: {
      fontWeight: 600 as any,
      fontSize: 16 as any,
      marginBottom: 8,
    },
    issueText: {
      fontSize: 14 as any,
      fontWeight: 500 as any,
      width: "70%" as any,
      textAlign: "center" as any,
    },
    actionButton: {
      size: "$3" as any,
      backgroundColor: "$physicalExamItemColor" as any,
      fontSize: 14 as any,
      fontWeight: 500 as any,
    },
    issueContainer: {
      justify: "space-between" as any,
      alignItems: "center" as any,
      flex: 1,
      backgroundColor: "$physicalExamItemColor" as any,
      marginBlock: 5,
      borderRadius: 10 as any,
      padding: 5,
    },
    addBtn: {
      fontSize: 16,
      size: "$4" as any,
      fontWeight: "600" as any,
      backgroundColor: "#EFF8FF",
      borderColor: "#B2DDFF" as any,
      color: "#175CD3" as any,
      borderWidth: 1,
    },
    addIssueContainer: {
      flex: 1,
      justifyContent: "space-between" as any,
      alignItems: "center" as any,
      backgroundColor: "transparent" as any,
      marginBlockStart: 5,
    },
    inputContainer: {
      backgroundColor: "transparent" as any,
      borderColor: "$primaryBorderColor" as any,
      flexGrow: 1 as any,
      marginRight: 8,
    },
  };
};

export default PhysicalExamType;
