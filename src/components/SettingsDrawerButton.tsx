import { ComponentType } from "react";
import { Button, Text, XStack, YStack } from "tamagui";

interface SettingsDrawerButtonProps {
  name: string;
  icon: ComponentType<{ size?: number }>;
  action?: () => void;
}

export default function SettingsDrawerButton({
  name,
  icon: Icon,
  action,
}: SettingsDrawerButtonProps) {
  const Buttonprops = {
    paddingHorizontal: "$4",
    justifyContent: "flex-start",
    backgroundColor: "$screenBackgroundcolor",
  };
  const alignItems = {
    alignItems: "center",
  };
  return (
    <YStack marginBlock={5}>
      <Button paddingInline={0} size="$5" {...Buttonprops} onPress={action}>
        <XStack space="$3" {...alignItems}>
          {Icon && <Icon size={20} />}
          <Text fontSize={18} fontWeight="500">
            {name}
          </Text>
        </XStack>
      </Button>
    </YStack>
  );
}
