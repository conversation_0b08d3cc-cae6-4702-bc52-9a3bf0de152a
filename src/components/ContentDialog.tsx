import { Di<PERSON>, Button, YS<PERSON>ck, Text, XStack, View } from "tamagui";
import { Modal } from "react-native";

interface ContentDialogProps {
  open: boolean;
  onClose: (open: boolean) => void;
  title: string;
  body: React.ReactNode;
  btnText: string;
}

export function ContentDialog({
  open,
  onClose,
  title,
  body,
  btnText,
}: ContentDialogProps): JSX.Element {
  const styles = useCallPendingDialogStyle();
  return (
    <Modal visible={true} transparent animationType="fade">
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: "rgba(0, 0, 0, 0.5)",
          width: "100%",
          height: "100%",
        }}
      >
        <Dialog modal open={open} onOpenChange={onClose}>
          <Dialog.Overlay {...styles.overlay} />
          <Dialog.Content {...styles.dialogContent}>
            <YStack {...styles.container}>
              <XStack {...styles.headerContainer}>
                <Text {...styles.headerText}>{title}</Text>
              </XStack>
              <YStack>{body}</YStack>

              <XStack
                style={{
                  display: "flex",
                  justifyContent: "space-around",
                  marginTop: 20,
                  gap: 10,
                }}
              >
                <Button
                  {...styles.button}
                  onPress={async () => {
                    onClose(false);
                  }}
                >
                  {btnText}
                </Button>
              </XStack>
            </YStack>
          </Dialog.Content>
        </Dialog>
      </View>
    </Modal>
  );
}

export const useCallPendingDialogStyle = () => {
  return {
    dialogContent: {
      bordered: true,
      bg: "$screenBackgroundcolor" as any,
      br: "$4" as any,
      shadowColor: "transparent" as any,
      shadowOpacity: 0 as any,
      shadowRadius: 0 as any,
      width: "90%" as any,
      alignSelf: "center" as any,
      justifyContent: "center" as any,
      borderRadius: "$7" as any,
      position: "absolute" as any,
      top: "50%" as any,
      left: "50%" as any,
      transform: [{ translateX: "-50%" }, { translateY: "-50%" }] as any,
      borderColor: "$primaryBorderColor" as any,
    },
    container: {
      gap: "$2" as any,
      paddingHorizontal: "$4" as any,
      marginHor: "$4" as any,
    },
    headerContainer: {
      justifyContent: "space-between",
    },
    headerText: {
      fontSize: 24,
      fontWeight: "600" as any,
    },
    overlay: {
      animation: "quick" as any,
      enterStyle: { opacity: 0 },
      exitStyle: { opacity: 0 },
      backgroundColor: "$callAlertBackground" as any,
      opacity: 0.5,
    },
    button: {
      color: "$buttonWhiteColor" as any,
      backgroundColor: "$primaryColor" as any,
      marginBlock: 10,
      fontSize: 18,
      fontWeight: 500 as any,
      flex: 1,
    },
    closeButton: {
      color: "$loginForgotPasswordColor" as any,
      backgroundColor: "$buttonWhiteColor" as any,
      marginBlock: 10,
      fontSize: 18,
      fontWeight: 500 as any,
      flex: 1,
    },
  };
};
