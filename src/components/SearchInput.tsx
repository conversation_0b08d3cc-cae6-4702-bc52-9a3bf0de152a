import React, { useState } from "react";
import { Input, XStack } from "tamagui";
import { Search } from "@tamagui/lucide-icons";

const SearchInput = ({
  onSearchChange,
}: {
  onSearchChange: (query: string) => void;
}) => {
  const [searchQuery, setSearchQuery] = useState("");

  const handleInputChange = (text: string) => {
    setSearchQuery(text);
    onSearchChange(text);
  };

  return (
    <XStack {...searchStyle.mainStack}>
      <Search size={20} color="$textcolor" />
      <Input
        {...searchStyle.searchInput}
        value={searchQuery}
        onChangeText={handleInputChange}
      />
    </XStack>
  );
};

export default SearchInput;

const searchStyle = {
  mainStack: {
    alignItems: "center" as any,
    borderRadius: "$4" as any,
    paddingHorizontal: "$3" as any,
    borderWidth: 1,
    height: 50,
    bg: "transparent" as any,
    borderColor: "$primaryBorderColor" as any,
  },
  searchInput: {
    flex: 1,
    placeholder: "Search patients",
    fontSize: 16,
    borderWidth: 0,
    bg: "transparent" as any,
    placeholderTextColor: "$textcolor" as any,
    fontWeight: "400" as any,
  },
};
