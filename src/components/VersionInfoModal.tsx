import React from "react";
import { <PERSON><PERSON>, <PERSON>ton, Y<PERSON><PERSON>ck, XStack, Text, Spinner } from "tamagui";
import { RefreshCw, ExternalLink, ArrowLeft } from "@tamagui/lucide-icons";
import { useVersionInfo } from "../hooks/useVersionInfo";
import { usePermissionStatus } from "../hooks/userPermissions";

interface VersionInfoModalProps {
  open: boolean;
  onClose: (open: boolean) => void;
}

export function VersionInfoModal({ open, onClose }: VersionInfoModalProps) {
  const { appVersion, patchId, reloadApp, openAppStore, isReloading } =
    useVersionInfo();
  const { camera, mic, requestPermission, openAppSettings } =
    usePermissionStatus();
  const styles = useVersionInfoModalStyle();

  const renderPermissionStatus = (
    label: string,
    status: string,
    onRequest: () => void
  ) => {
    if (status === "granted") {
      return (
        <XStack {...styles.versionContainer}>
          <Text {...styles.versionLabel}>
            {label}: <Text style={{ color: "green" }}>Enabled</Text>
          </Text>
        </XStack>
      );
    }

    return (
      <XStack
        {...styles.versionContainer}
        gap="$2"
        // alignItems="center"
      >
        <Text {...styles.versionLabel}>
          {label}:{" "}
          <Text style={{ color: "red" }}>
            {status === "blocked" ? "X" : "X"}
          </Text>
        </Text>
        <Button
          size="$2"
          {...styles.enableButton}
          // backgroundColor="red"
          onPress={status === "blocked" ? openAppSettings : onRequest}
        >
          {status === "blocked" ? "Enable in Settings" : "Allow"}
        </Button>
      </XStack>
    );
  };

  return (
    <Dialog modal open={open} onOpenChange={onClose}>
      <Dialog.Overlay {...styles.overlay} />
      <Dialog.Content {...styles.dialogContent}>
        <YStack {...styles.container}>
          <XStack {...styles.headerContainer}>
            <Text {...styles.headerText}>App Status</Text>
          </XStack>

          <XStack {...styles.versionContainer}>
            <Text {...styles.versionLabel}>App Version: </Text>
            <Text {...styles.versionLabel}>{appVersion}</Text>
          </XStack>

          {patchId !== "None" && (
            <XStack {...styles.versionContainer}>
              <Text {...styles.versionLabel}>Patch ID: </Text>
              <Text {...styles.versionLabel}>{patchId}</Text>
            </XStack>
          )}

          {renderPermissionStatus("Camera", camera, () =>
            requestPermission("camera")
          )}
          {renderPermissionStatus("Microphone", mic, () =>
            requestPermission("mic")
          )}

          <YStack gap={10} mt={20}>
            <Button
              {...styles.reloadButton}
              icon={<ExternalLink size={18} />}
              onPress={openAppStore}
            >
              Check for Updates
            </Button>

            <Button
              {...styles.updateButton}
              icon={
                isReloading ? undefined : <RefreshCw size={18} color="white" />
              }
              onPress={reloadApp}
              disabled={isReloading}
            >
              {isReloading ? (
                <XStack gap={8}>
                  <Spinner size="small" color="$primaryColor" />
                  <Text>Reloading...</Text>
                </XStack>
              ) : (
                "Reload App"
              )}
            </Button>

            <Button
              {...styles.reloadButton}
              // icon={<ArrowLeft size={18} color={"$textcolor"} />}
              onPress={() => onClose(false)}
            >
              Close
            </Button>
          </YStack>
        </YStack>
      </Dialog.Content>
    </Dialog>
  );
}

export const useVersionInfoModalStyle = () => {
  return {
    dialogContent: {
      bordered: true,
      bg: "$screenBackgroundcolor" as any,
      br: "$4" as any,
      shadowColor: "transparent" as any,
      shadowOpacity: 0 as any,
      shadowRadius: 0 as any,
      width: "90%" as any,
      alignSelf: "center" as any,
      justifyContent: "center" as any,
      borderRadius: "$7" as any,
      position: "absolute" as any,
      top: "50%" as any,
      left: "50%" as any,
      transform: [{ translateX: "-50%" }, { translateY: "-50%" }] as any,
      borderColor: "$primaryBorderColor" as any,
    },
    container: {
      gap: "$2" as any,
      paddingHorizontal: "$4" as any,
      paddingVertical: "$4" as any,
    },
    headerContainer: {
      justifyContent: "center",
      alignItems: "center",
      marginBottom: "$2" as any,
    },
    headerText: {
      fontSize: 24,
      fontWeight: "600" as any,
    },
    overlay: {
      animation: "quick" as any,
      enterStyle: { opacity: 0 },
      exitStyle: { opacity: 0 },
      backgroundColor: "$callAlertBackground" as any,
      opacity: 0.5,
    },
    versionContainer: {
      backgroundColor: "$screenBackgroundcolor" as any,
      borderRadius: "$4" as any,
      marginTop: "$1" as any,
      marginBottom: "$1" as any,
      alignItems: "center",
    },
    versionLabel: {
      fontSize: 18,
      fontWeight: "600" as any,
      color: "$textcolor" as any,
    },
    updateButton: {
      backgroundColor: "$primaryColor" as any,
      color: "white" as any,
      fontSize: 16,
      fontWeight: "600" as any,
    },
    enableButton: {
      backgroundColor: "$primaryColor" as any,
      color: "white" as any,
      fontSize: 12,
      // fontWeight: "600" as any,
    },
    reloadButton: {
      backgroundColor: "$screenBackgroundcolor" as any,
      color: "$textcolor" as any,
      borderColor: "$primaryColor" as any,
      borderWidth: 1,
      fontSize: 16,
      fontWeight: "600" as any,
    },
  };
};
