import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, H4, <PERSON>Stack } from "tamagui";
import { Phone } from "@tamagui/lucide-icons";
import { Platform, Vibration, NativeModules } from "react-native";
import CallKitService from "~/services/CallKitService";

interface CallRequestDialogProps {
  open: boolean;
  onClose: (open: boolean) => void;
}

export function CallRequestDialog({ open, onClose }: CallRequestDialogProps) {
  const styles = useCallRequestDialogStyle();

  const handleAcceptCall = async () => {
    try {
      // Cancel any ongoing vibration
      Vibration.cancel();
      console.log("Canceled vibration in CallRequestDialog handleAcceptCall");

      // End any active CallKit calls
      if (Platform.OS === "ios") {
        try {
          // Get active calls for debugging
          const activeCalls = await CallKitService.getActiveCalls();
          console.log(
            "Active calls before ending in CallRequestDialog:",
            activeCalls
          );

          // Use CallKitService to end all calls
          const result = await CallKitService.endAllCalls();
          console.log(
            "Ended all active CallKit calls from CallRequestDialog:",
            result
          );

          // If there are still active calls, use the nuclear option
          setTimeout(async () => {
            const remainingCalls = await CallKitService.getActiveCalls();
            console.log(
              "Remaining calls after ending in CallRequestDialog:",
              remainingCalls
            );

            if (remainingCalls.length > 0) {
              console.log(
                "Calls still active, using direct method to end calls"
              );

              // Try to directly end calls using the native module
              if (
                Platform.OS === "ios" &&
                NativeModules.RingtoneModule &&
                typeof NativeModules.RingtoneModule.directEndAllCalls ===
                  "function"
              ) {
                try {
                  const directResult =
                    await NativeModules.RingtoneModule.directEndAllCalls();
                  console.log("Direct end all calls result:", directResult);
                } catch (error) {
                  console.error(
                    "Error using direct method to end calls:",
                    error
                  );
                }
              }

              // Also try the nuclear option
              const resetResult = await CallKitService.resetCallKitProvider();
              console.log("Reset CallKit provider result:", resetResult);
            }
          }, 500);
        } catch (error) {
          console.error(
            "Error ending CallKit calls from CallRequestDialog:",
            error
          );
        }
      }

      // Close the dialog
      onClose(false);
    } catch (error) {
      console.error("Error in CallRequestDialog handleAcceptCall:", error);
      // Make sure vibration is canceled even if there's an error
      Vibration.cancel();
      onClose(false);
    }
  };

  const renderInfoBlock = (label: string, value: string) => (
    <YStack marginBlockStart={20}>
      <Text {...styles.infoBlockLabel}>{label}:</Text>
      <Text {...styles.infoBlockValue}>{value}</Text>
    </YStack>
  );

  return (
    <Dialog modal open={open} onOpenChange={onClose}>
      <Dialog.Overlay {...styles.overlay} />

      <Dialog.Content {...styles.dialogContent}>
        <YStack {...styles.container}>
          {/* Header */}
          <XStack {...styles.headerContainer}>
            <Text {...styles.headerText}>Call Request</Text>
            <Text {...styles.callEndsText}>Call ends: 29s</Text>
          </XStack>

          {/* Caller Info */}
          <YStack>
            {renderInfoBlock("Caller", "Medadmin")}
            {renderInfoBlock("Location", "*Patient Location*")}
            {renderInfoBlock(
              "Patient",
              "Samantha June, Female | DOB: 1964-08-30"
            )}
            {renderInfoBlock(
              "Chief Complaint",
              "Sore muscles, unusual amount of fatigue, and not sleeping."
            )}
          </YStack>

          {/* Accept Call Button */}
          <Button
            {...styles.button}
            icon={<Phone size={"$1"} />}
            onPress={handleAcceptCall}
          >
            Accept Call
          </Button>
        </YStack>
      </Dialog.Content>
    </Dialog>
  );
}

export const useCallRequestDialogStyle = () => {
  return {
    dialogContent: {
      bordered: true,
      bg: "$screenBackgroundcolor" as any,
      br: "$4" as any,
      shadowColor: "transparent" as any,
      shadowOpacity: 0 as any,
      shadowRadius: 0 as any,
      width: "90%" as any,
      alignSelf: "center" as any,
      justifyContent: "center" as any,
      borderRadius: "$7" as any,
      position: "absolute" as any,
      top: "50%" as any,
      left: "50%" as any,
      transform: [{ translateX: "-50%" }, { translateY: "-50%" }] as any,
      borderColor: "$primaryBorderColor" as any,
    },
    container: {
      gap: "$2" as any,
      paddingHorizontal: "$4" as any,
    },
    headerContainer: {
      justifyContent: "space-between",
    },
    headerText: {
      fontSize: 24,
      fontWeight: "600" as any,
    },
    callEndsText: {
      fontSize: 16,
      fontWeight: "200" as any,
    },
    overlay: {
      animation: "quick" as any,
      enterStyle: { opacity: 0 },
      exitStyle: { opacity: 0 },
      backgroundColor: "$callAlertBackground" as any,
      opacity: 0.5,
    },
    infoBlockLabel: {
      fontSize: 18,
      fontWeight: "400" as any,
    },
    infoBlockValue: {
      fontSize: 18,
      fontWeight: "300" as any,
      marginBlockStart: 10,
    },
    button: {
      color: "$buttonWhiteColor" as any,
      backgroundColor: "$primaryColor" as any,
      marginBlock: 10,
      fontSize: 18,
      fontWeight: 500 as any,
    },
  };
};
