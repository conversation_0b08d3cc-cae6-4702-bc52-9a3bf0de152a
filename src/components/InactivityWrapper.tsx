import React, { useEffect, useRef, useCallback } from "react";
import { View, AppState, StyleSheet } from "react-native";
import { useAuth } from "~/context/AuthContext";
import { useSegments } from "expo-router";

const INACTIVITY_TIME = 5 * 60 * 1000; // 5 minutes

const isDev = process.env.NODE_ENV === "development";

interface InactivityWrapperProps {
  children: React.ReactNode;
}

const InactivityWrapper = ({ children }: InactivityWrapperProps) => {
  const { signOut, user } = useAuth();
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const segments = useSegments();

  const bypassScreen = segments.some((segment) =>
    ["call", "calloverview", "telehealthconsent"].includes(segment)
  );

  const bypassTestUser =
    user?.email?.includes("nurse@") || user?.email?.includes("nurse1@");

  const resetTimer = useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    if (isDev) {
      // console.log("No timer set for dev environment.");
    } else if (!bypassScreen && !bypassTestUser) {
      timerRef.current = setTimeout(() => {
        signOut();
      }, INACTIVITY_TIME);
    }
  }, [signOut, bypassScreen, bypassTestUser]);

  useEffect(() => {
    resetTimer();

    const subscription = AppState.addEventListener("change", (nextAppState) => {
      if (nextAppState === "active") {
        resetTimer();
      }
    });

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      subscription.remove();
    };
  }, [resetTimer]);

  return (
    <View
      style={styles.container}
      onTouchStart={resetTimer}
      onTouchMove={resetTimer}
    >
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default InactivityWrapper;
