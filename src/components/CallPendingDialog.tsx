import AsyncStorage from "@react-native-async-storage/async-storage";
import { CircleX, UserCheck } from "@tamagui/lucide-icons";
import Constants from "expo-constants";
import { useRouter } from "expo-router";
import { useEffect, useState } from "react";
import { Modal, NativeModules, Platform, Vibration } from "react-native";
import { io, Socket } from "socket.io-client";
import { Button, Dialog, Spinner, Text, View, XStack, YStack } from "tamagui";
import axiosConfig from "~/services/axiosConfig";
import CallKitService from "~/services/CallKitService";

interface CallPendingDialogProps {
  open: boolean;
  onClose: (open: boolean) => void;
  requestData?: {
    facilityId: string;
    patientId: string;
    reason: string;
    patientFirstName?: string;
    patientDOB?: string;
    images?: string[];
    fileKeys?: string[];
    fileNames?: string[];
    fileTypes?: string[];
  };
  consultation_request_id?: string;
}

interface ConsultationRequest {
  consultationId: string;
  sdkId: string;
}

export function CallPendingDialog({
  open,
  onClose,
  requestData,
  consultation_request_id,
}: CallPendingDialogProps): JSX.Element {
  const styles = useCallPendingDialogStyle();
  const {
    facilityId,
    patientId,
    reason,
    patientDOB,
    patientFirstName,
    images,
  } = requestData ?? {};
  const [status, setStatus] = useState<string>("pending");
  const router = useRouter();
  const [requestId, setRequestId] = useState<string | null>(null);

  useEffect(() => {
    if (!open || !requestId) {
      return;
    }

    const pollingInterval = setInterval(async () => {
      try {
        const response = await axiosConfig.get(
          `/consultation/request/ping/${requestId}`
        );
        const { status, consultationId, nurse_key } = response.data;
        if (
          status === "accepted" &&
          consultationId !== null &&
          nurse_key !== null
        ) {
          router.push({
            pathname: `/nurse/call`,
            params: { consultationId, sdkId: nurse_key },
          });
          onClose(false);
        }
      } catch (error) {}
    }, 8000);

    return () => clearInterval(pollingInterval);
  }, [open, requestId, onClose, router]);

  useEffect(() => {
    let activeRequestId: string | null = null;
    const socket: Socket = io(Constants.expoConfig?.extra?.apiUrl, {
      transports: ["websocket"],
    });
    socket.on("connect", () => {});

    socket.on("consultationAccepted", (data: ConsultationRequest) => {
      setStatus("accepted");
    });

    socket.on("consultationStarted", async (data: ConsultationRequest) => {
      Vibration.cancel();

      try {
        const key = `sdkId_${data.consultationId}`;
        await AsyncStorage.setItem(key, data.sdkId);
      } catch (error) {}

      if (Platform.OS === "ios") {
        try {
          const activeCalls = await CallKitService.getActiveCalls();
          const result = await CallKitService.endAllCalls();

          setTimeout(async () => {
            const remainingCalls = await CallKitService.getActiveCalls();
            if (remainingCalls.length > 0) {
              if (
                Platform.OS === "ios" &&
                NativeModules.RingtoneModule &&
                typeof NativeModules.RingtoneModule.directEndAllCalls ===
                  "function"
              ) {
                try {
                  const directResult =
                    await NativeModules.RingtoneModule.directEndAllCalls();
                } catch (error) {}
              }
              const resetResult = await CallKitService.resetCallKitProvider();
            }
          }, 500);
        } catch (error) {}
      }

      await new Promise((resolve) => setTimeout(resolve, 4000));

      router.push({
        pathname: `/nurse/call`,
        params: { consultationId: data.consultationId, sdkId: data.sdkId },
      });

      onClose(false);
    });

    const sendRequest = async () => {
      if (requestData) {
        try {
          const fileKeys = requestData.fileKeys || requestData.images || [];
          const fileNames = requestData.fileNames || [];
          const fileTypes = requestData.fileTypes || [];
          const payload = {
            facilityId: facilityId || "",
            patientId: patientId || "",
            reason: reason || "",
            patientFirstName: patientFirstName || "",
            patientDOB: patientDOB || "",
            fileKeys,
            fileNames,
            fileTypes,
          };
          const response = await axiosConfig.post(
            "/consultation/request",
            payload
          );
          activeRequestId = response.data.consultationRequest.id;
          setRequestId(activeRequestId);
          socket.emit("joinRoom", `consultationRequest:${activeRequestId}`);
        } catch (error) {
          console.error(
            "[CallPendingDialog] Error sending consultation request:",
            error
          );
        }
      } else if (consultation_request_id) {
        try {
          const response = await axiosConfig.post(
            `/consultation/request/follow-up/${consultation_request_id}`
          );
          activeRequestId = response.data.consultationRequest.id;
          setRequestId(activeRequestId);
          socket.emit("joinRoom", `consultationRequest:${activeRequestId}`);
        } catch (error) {
          console.error(
            "[CallPendingDialog] Error in follow-up consultation flow:",
            error
          );
        }
      }
    };

    sendRequest();

    return () => {
      socket.disconnect();
    };
  }, [requestData, consultation_request_id]);

  const handleCancelRequest = async () => {
    try {
      Vibration.cancel();
      console.log(
        "Canceled vibration in CallPendingDialog handleCancelRequest"
      );

      if (Platform.OS === "ios") {
        try {
          const activeCalls = await CallKitService.getActiveCalls();
          console.log(
            "Active calls before ending in CallPendingDialog:",
            activeCalls
          );
          const result = await CallKitService.endAllCalls();
          console.log(
            "Ended all active CallKit calls from CallPendingDialog:",
            result
          );

          setTimeout(async () => {
            const remainingCalls = await CallKitService.getActiveCalls();
            console.log(
              "Remaining calls after ending in CallPendingDialog:",
              remainingCalls
            );

            if (remainingCalls.length > 0) {
              console.log(
                "Calls still active, using direct method to end calls"
              );

              if (
                Platform.OS === "ios" &&
                NativeModules.RingtoneModule &&
                typeof NativeModules.RingtoneModule.directEndAllCalls ===
                  "function"
              ) {
                try {
                  const directResult =
                    await NativeModules.RingtoneModule.directEndAllCalls();
                  console.log("Direct end all calls result:", directResult);
                } catch (error) {
                  console.error(
                    "Error using direct method to end calls:",
                    error
                  );
                }
              }

              const resetResult = await CallKitService.resetCallKitProvider();
              console.log("Reset CallKit provider result:", resetResult);
            }
          }, 500);
        } catch (error) {
          console.error(
            "Error ending CallKit calls from CallPendingDialog:",
            error
          );
        }
      }

      await axiosConfig.post("/consultation/request/cancel", {
        consultationRequestId: requestId,
      });

      onClose(false);
    } catch (error) {
      console.error("Error cancelling consultation request:", error);
      Vibration.cancel();
      onClose(false);
    }
  };

  return (
    <Modal visible={open} transparent animationType="fade">
      <View
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          flex: 1,
          backgroundColor: "rgba(0, 0, 0, 0.5)",
          height: "100%",
          width: "100%",
        }}
      >
        <Dialog modal open={open} onOpenChange={onClose}>
          <Dialog.Overlay {...styles.overlay} />
          <Dialog.Content {...styles.dialogContent}>
            <YStack {...styles.container}>
              <XStack {...styles.headerContainer}>
                <Text {...styles.headerText}>
                  {status === "pending"
                    ? "Pending Request"
                    : "A Provider is joining the call"}
                </Text>
              </XStack>
              <YStack>
                {status === "pending" ? (
                  <Spinner
                    size="large"
                    color="$loginForgotPasswordColor"
                    {...styles.spinner}
                  />
                ) : (
                  <View {...styles.userIcon}>
                    <UserCheck
                      size={"$7"}
                      strokeWidth={1}
                      color={"$primaryColor"}
                    />
                  </View>
                )}

                <Text {...styles.infoBlockValue}>
                  {status === "pending"
                    ? "Notifying providers"
                    : "A provider has accepted the request and is reviewing the patient details"}
                </Text>
              </YStack>
              {status === "pending" && (
                <Button
                  {...styles.button}
                  icon={<CircleX size={"$1"} />}
                  onPress={() => {
                    handleCancelRequest();
                  }}
                >
                  Cancel Request
                </Button>
              )}
            </YStack>
          </Dialog.Content>
        </Dialog>
      </View>
    </Modal>
  );
}

export const useCallPendingDialogStyle = () => {
  return {
    dialogContent: {
      bordered: true,
      bg: "$screenBackgroundcolor" as any,
      br: "$4" as any,
      shadowColor: "transparent" as any,
      shadowOpacity: 0 as any,
      shadowRadius: 0 as any,
      width: "90%" as any,
      maxWidth: 450 as any,
      alignSelf: "center" as any,
      justifyContent: "center" as any,
      borderRadius: "$7" as any,
      position: "absolute" as any,
      top: "50%" as any,
      left: "50%" as any,
      transform: [{ translateX: "-50%" }, { translateY: "-50%" }] as any,
      borderColor: "$primaryBorderColor" as any,
    },
    container: {
      gap: "$2" as any,

      marginHor: "$4" as any,
    },
    headerContainer: {
      justifyContent: "space-between",
    },
    headerText: {
      fontSize: 24,
      fontWeight: "600" as any,
    },
    callEndsText: {
      fontSize: 16,
      fontWeight: "200" as any,
    },
    overlay: {
      animation: "quick" as any,
      enterStyle: { opacity: 0 },
      exitStyle: { opacity: 0 },
      backgroundColor: "$callAlertBackground" as any,
      opacity: 0.5,
    },
    infoBlockLabel: {
      fontSize: 18,
      fontWeight: "400" as any,
    },
    infoBlockValue: {
      fontSize: 15,
      fontWeight: "400" as any,
      marginBlockStart: 10,
      textAlign: "center",
    },
    button: {
      color: "$buttonWhiteColor" as any,
      backgroundColor: "$primaryColor" as any,
      marginBlock: 10,
      fontSize: 18,
      fontWeight: 500 as any,
    },
    spinner: {
      marginBlock: 30,
    },
    userIcon: {
      alignItems: "center" as any,
      justiifyContent: "center" as any,
      marginBlock: 20,
    },
  };
};
