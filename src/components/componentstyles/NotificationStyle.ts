export const useNotificationStyle = () => {
  return {
    card: {
      flex: 1,
      backgroundColor: "$screenBackgroundcolor",
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 1,
      borderRadius: 8,
      padding: 10,
    },
    notificationText: {
      fontWeight: "600" as any,
      fontSize: 20,
      color: "$textcolor" as any,
      marginBottom: 10,
    },
    mainStack: {
      marginInline: 10,
      marginBlock: 10,
    },
    contactItem: {
      padding: 10,
      justifyContent: "center" as any,
    },
    noContacts: {
      marginBlock: 10,
      justifyContent: "center",
      alignItems: "center",
    },
    addContactText: {
      marginBlockStart: 10,
      color: "$textcolor" as any,
      fontSize: 14,
      fontWeight: 400 as any,
    },
    btnAdd: {
      fontSize: 14 as any,
      size: "$4" as "$4",
      fontWeight: 600 as any,
      backgroundColor: "$confirmOrderBlue",
      borderColor: "$confirmOrderBorderCOlor" as any,
      color: "$confirmOrderTextColor" as any,
      borderWidth: 1,
      marginBlockStart: 10,
    },
    addContactDrawer: {
      marginBlock: 10,
    },
    contactDetails: {
      marginBlockStart: 15,
    },
    contactDetailsInput: {
      color: "$textcolor" as any,
      backgroundColor: "transparent" as any,
      fontSize: 18,
      fontWeight: 400 as any,
      borderWidth: 1,
      borderColor: "$primaryBorderColor" as any,
    },
    titleText: {
      color: "$textcolor" as any,
      fontSize: 14,
      fontWeight: 500 as any,
      marginBlock: 10,
    },
    saveBtn: {
      fontSize: 14 as any,
      size: "$4" as "$4",
      fontWeight: 600 as any,
      backgroundColor: "$confirmOrderBlue",
      borderColor: "$confirmOrderBorderCOlor" as any,
      color: "$confirmOrderTextColor" as any,
      borderWidth: 1,
      marginBlockStart: 10,
    },
    closeBtn: {
      fontSize: 14 as any,
      size: "$4" as "$4",
      fontWeight: 600 as any,
      backgroundColor: "none" as any,
      borderColor: "transparent" as any,
      color: "$confirmOrderTextColor" as any,
      borderWidth: 1,
      marginBlockStart: 10,
    },
    contactRow: {
      alignItems: "center",
      space: "$2" as any,
      justifyContent: "space-between",
    },
    innerContactRow: {
      space: "$2" as any,
      alignItems: "center",
    },
    emailText: {
      maxWidth: "$15" as any,
    },
    removeText: {
      color: "$removeTextBlue" as any,
      marginInlineStart: 5,
    },
    checkbox: {
      marginInlineStart: 10,
      marginInlineEnd: 10,
      backgroundColor: "$screenBackgroundcolor" as any,
      borderColor: "$primaryBorderColor" as any,
    },
    consent: {
      marginBlock: 10,
      flexWrap: "wrap" as any,
      flex: 1,
      alignItems: "flex-start",
    },
  };
};
