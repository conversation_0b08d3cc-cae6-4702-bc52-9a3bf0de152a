export const useMultiPatientRequestVisitStyles = () => {
  return {
    addPatientCard: {
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 1,
      borderRadius: 12,
      paddingBlock: 40,
      paddingInline: "10%" as any,
      marginBlockStart: 10,
      marginBlockEnd: 10,
      backgroundColor: "$screenBackgroundcolor" as any,
      justifyContent: "center" as any,
    },
    addPatientText: {
      marginBlock: 20,
      color: "$textcolor" as any,
      fontSize: 14,
      fontWeight: 500 as any,
      self: "center" as any,
    },
    addPatientBtn: {
      borderWidth: 1,
      padding: 7,
      fontSize: 14,
      size: "$4" as any,
      fontWeight: "600" as any,
      backgroundColor: "$confirmOrderBlue",
      borderColor: "$confirmOrderBorderCOlor" as any,
      color: "$confirmOrderTextColor" as any,
      width: "150" as any,
      self: "center" as any,
    },
    dummyPatientCardsContainer: {
      justifyContent: "center" as any,
      alignItems: "center" as any,
      gap: "$4" as any,
    },
    demoPatientsCardSmall: {
      borderWidth: 1,
      shadowColor: "#000000" as any,
      shadowOpacity: 0.1,
      shadowRadius: 4,
      borderColor: "$primaryBorderColor" as any,
      borderRadius: 8,
      backgroundColor: "$screenBackgroundcolor" as any,
    },
    demoPatientsCardMedium: {
      borderWidth: 1,
      shadowColor: "#000000" as any,
      shadowOpacity: 0.1,
      shadowRadius: 4,
      borderColor: "$primaryBorderColor" as any,
      borderRadius: 8,
      backgroundColor: "$screenBackgroundcolor" as any,
    },
    demoPatientsCardLarge: {
      borderWidth: 1,
      shadowColor: "#000000" as any,
      shadowOpacity: 0.1,
      shadowRadius: 4,
      borderColor: "$primaryBorderColor" as any,
      borderRadius: 8,
      backgroundColor: "$screenBackgroundcolor" as any,
    },
    patientCard: {
      borderWidth: 1,
      borderColor: "$primaryBorderColor" as any,
      borderRadius: 12,
      backgroundColor: "$screenBackgroundcolor" as any,
      padding: 15,
      marginBlockEnd: 15,
    },
    patientCardTitle: {
      fontSize: 16,
      fontWeight: 600 as any,
      color: "$textcolor" as any,
    },
    patientCardSubTitle: {
      fontSize: 14,
      fontWeight: 400 as any,
      color: "$textcolor" as any,
    },
  };
};
