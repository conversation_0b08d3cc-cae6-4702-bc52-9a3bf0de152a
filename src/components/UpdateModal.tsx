import { Button, Dialog, H2, Paragraph, YStack } from "tamagui";
import { useAppUpdates } from "../hooks/useAppUpdates";

export const UpdateModal = () => {
  const { isUpdateAvailable, currentVersion, newVersion, downloadAndReload } =
    useAppUpdates();

  if (!isUpdateAvailable) return null;

  return (
    <Dialog modal>
      <Dialog.Portal>
        <Dialog.Overlay />
        <Dialog.Content>
          <YStack space="$4">
            <H2>Update Available</H2>
            <Paragraph>
              A new version ({newVersion}) is available. Your current version is{" "}
              {currentVersion}.
            </Paragraph>
            <YStack space="$2">
              <Button onPress={downloadAndReload}>Update Now</Button>
              <Dialog.Close asChild>
                <Button theme="alt1">Later</Button>
              </Dialog.Close>
            </YStack>
          </YStack>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog>
  );
};
