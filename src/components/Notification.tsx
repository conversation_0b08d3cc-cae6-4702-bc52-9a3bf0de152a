import { Animated, StyleSheet } from "react-native";
import React from "react";
import { Text } from "tamagui";

export const Notification = ({
  message,
  visible,
  onHide,
}: {
  message: string;
  visible: boolean;
  onHide: () => void;
}) => {
  const translateX = React.useRef(new Animated.Value(-200)).current;

  React.useEffect(() => {
    if (visible) {
      Animated.sequence([
        Animated.timing(translateX, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.delay(2000),
        Animated.timing(translateX, {
          toValue: 200,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => {
        onHide();
      });
    }
  }, [visible]);

  if (!visible) return null;

  return (
    <Animated.View
      style={[
        styles.notification,
        {
          transform: [{ translateX }],
        },
      ]}
    >
      <Text style={styles.notificationText}>{message}</Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  notification: {
    position: "absolute",
    top: 20,
    right: 20,
    backgroundColor: "rgba(0, 0, 0, 0.8)",
    padding: 12,
    borderRadius: 8,
    zIndex: 1000,
    maxWidth: 300,
  },
  notificationText: {
    color: "#ffffff",
    fontSize: 14,
    fontWeight: "500",
  },
});
