import { But<PERSON>, Card, Text, View, YStack } from "tamagui";
import { useMultiPatientRequestVisitStyles } from "./componentstyles/MultiPatientRequestVisitStyles";
import { Plus } from "@tamagui/lucide-icons";
import { PatientInfo } from "@/nurse/requestVisit";
import { FlatList } from "react-native";

interface MultiPatientRequestVisitProps {
  selectedPatients: PatientInfo[];
  onAddpatient: () => void;
}

const MultiPatientRequestVisit = ({
  selectedPatients,
  onAddpatient,
}: MultiPatientRequestVisitProps) => {
  const styles = useMultiPatientRequestVisitStyles();

  const renderItem = ({ item }: { item: string }) => {
    return <></>;
  };

  return (
    <View flex={1}>
      {selectedPatients && selectedPatients.length > 0 ? (
        <View>
          <FlatList
            data={selectedPatients}
            keyExtractor={(item, index) => item.id?.toString()}
            renderItem={({ item }) => (
              <View {...styles.patientCard}>
                <YStack gap={"$2"}>
                  <Text {...styles.patientCardTitle}>{item.name}</Text>
                  <Text {...styles.patientCardSubTitle}>DOB: {item.dob}</Text>
                </YStack>
              </View>
            )}
          />
        </View>
      ) : (
        <View flex={1}>
          <Card {...styles.addPatientCard} flex={1}>
            <DemoPatients />
            <Text {...styles.addPatientText}>
              Add a patient to this call request
            </Text>
            <Button
              icon={<Plus size={"$1"} />}
              {...styles.addPatientBtn}
              onPress={onAddpatient}
            >
              Add a patient
            </Button>
          </Card>
        </View>
      )}
    </View>
  );
};

export default MultiPatientRequestVisit;

const DemoPatients = () => {
  const styles = useMultiPatientRequestVisitStyles();
  return (
    <View {...styles.dummyPatientCardsContainer}>
      <Card {...styles.demoPatientsCardSmall} width={"70%"} padding={10}>
        <YStack>
          <Text color="$textcolor" opacity={0.5} fontSize={10} fontWeight={600}>
            Patient name
          </Text>
          <Text color="$textcolor" opacity={0.5} fontSize={10} fontWeight={400}>
            Gender: Female | DOB: YYYY-MM-DD
          </Text>
        </YStack>
      </Card>
      <Card {...styles.demoPatientsCardMedium} width={"85%"} padding={10}>
        <YStack>
          <Text color="$textcolor" opacity={0.7} fontSize={12} fontWeight={600}>
            Patient name
          </Text>
          <Text color="$textcolor" opacity={0.7} fontSize={12} fontWeight={400}>
            Gender: Female | DOB: YYYY-MM-DD
          </Text>
        </YStack>
      </Card>
      <Card {...styles.demoPatientsCardLarge} width={"100%"} padding={10}>
        <YStack gap={"$1"}>
          <Text color="$textcolor" opacity={1} fontSize={14} fontWeight={600}>
            Patient name
          </Text>
          <Text color="$textcolor" opacity={1} fontSize={14} fontWeight={400}>
            Gender: Female | DOB: YYYY-MM-DD
          </Text>
        </YStack>
      </Card>
    </View>
  );
};
