import { ChevronDown } from "@tamagui/lucide-icons";
import React, { useState } from "react";
import { Text, TouchableOpacity, View, StyleSheet } from "react-native";
import { YStack } from "tamagui";

interface DropdownProps {
  onSelect: (value: string) => void;
  selectedOption: string; // Add this prop to make it controlled
}

const Dropdown: React.FC<DropdownProps> = ({ onSelect, selectedOption }) => {
  const options = [
    // { id: "email", name: "Email" },
    { id: "mobile", name: "Mobile" },
  ];

  const [clicked, setClicked] = useState<boolean>(false);
  // Derive display value from selectedOption
  const displayValue =
    options.find((opt) => opt.id === selectedOption)?.name || "Email";

  const handleSelect = (item: { id: string; name: string }) => {
    onSelect(item.id); // Update parent state
    setClicked(false);
  };

  return (
    <YStack style={styles.container}>
      {/* Select Button */}
      <TouchableOpacity
        style={styles.selectButton}
        onPress={() => setClicked((prev) => !prev)}
      >
        <Text style={styles.selectedText}>{displayValue}</Text>
        <ChevronDown size={20} color="black" />
      </TouchableOpacity>

      {/* Dropdown List (Positioned Absolutely) */}
      {clicked && (
        <YStack style={styles.dropdownContainer}>
          {options.map((item) => (
            <TouchableOpacity
              key={item.id}
              style={styles.listItem}
              onPress={() => handleSelect(item)}
            >
              <Text style={styles.listItemText}>{item.name}</Text>
            </TouchableOpacity>
          ))}
        </YStack>
      )}
    </YStack>
  );
};

const styles = StyleSheet.create({
  container: {
    width: "100%",
    alignSelf: "stretch",
    position: "relative",
  },
  selectButton: {
    width: "100%",
    height: 50,
    borderRadius: 10,
    borderWidth: 0.5,
    borderColor: "#D0D5DD",
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 15,
    backgroundColor: "white",
  },
  selectedText: {
    fontWeight: "600",
  },
  dropdownContainer: {
    position: "absolute",
    top: 55,
    left: 0,
    right: 0,
    elevation: 5,
    zIndex: 10,
    backgroundColor: "#fff",
    borderRadius: 10,
    borderWidth: 0.5,
    borderColor: "#D0D5DD",
    overflow: "hidden",
  },
  listItem: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderBottomWidth: 0.5,
    borderColor: "#D0D5DD",
  },
  listItemText: {
    fontWeight: "600",
  },
});

export default Dropdown;
