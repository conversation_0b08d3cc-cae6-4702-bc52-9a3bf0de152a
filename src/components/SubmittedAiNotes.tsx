import React, { useState } from "react";
import { ScrollView, Dimensions } from "react-native";
import { Text, Card, YStack } from "tamagui";

type SubmittedAiNotesProps = {
  subjective: string;
  objective: string;
  assessment: string;
  plan: string;
  noteSignedBy: string;
  dateSignedByProvider: string;
  datePatientConsented: string;
};
const screenHeight = Dimensions.get("window").height;
const SubmittedAiNotes: React.FC<SubmittedAiNotesProps> = ({
  subjective,
  objective,
  assessment,
  plan,
  noteSignedBy,
  dateSignedByProvider,
  datePatientConsented,
}) => {
  const styles = useSubmittedAiNotesStyles();
  const [availableHeight, setAvailableHeight] = useState(screenHeight);
  return (
    <YStack {...styles.mainStack}>
      <Card>
        <YStack {...styles.container}>
          <Text {...styles.callerNotesText}>Call notes</Text>
          <YStack {...styles.innerContainer}>
            <ScrollView
              style={{
                maxHeight:
                  availableHeight / 2 <= 450 ? availableHeight / 2 : 450,
              }}
              contentContainerStyle={{ paddingBottom: 20 }}
              showsVerticalScrollIndicator={false}
            >
              <Text {...styles.heading}>Subjective:</Text>
              <Text {...styles.body}>{subjective || "N/A"}</Text>

              <Text {...styles.heading}>Objective:</Text>
              <Text {...styles.body}>{objective || "N/A"}</Text>

              <Text {...styles.heading}>Assessment:</Text>
              <Text {...styles.body}>{assessment || "N/A"}</Text>

              <Text {...styles.heading}>Plan:</Text>
              <Text {...styles.body}>{plan || "N/A"}</Text>

              <Text {...styles.heading}>Note Signed by:</Text>
              <Text {...styles.body}>{noteSignedBy || "N/A"}</Text>

              <Text {...styles.heading}>Date Signed:</Text>
              <Text {...styles.body}>{dateSignedByProvider || "N/A"}</Text>

              <Text {...styles.heading}>Date Patient Consented:</Text>
              <Text {...styles.body}>{datePatientConsented || "N/A"}</Text>
            </ScrollView>
          </YStack>
        </YStack>
      </Card>
    </YStack>
  );
};

export const useSubmittedAiNotesStyles = () => {
  return {
    mainStack: {
      borderRadius: 11,
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 1,
      backgroundColor: "$screenBackgroundcolor" as any,
      paddingBlock: 15,
      paddingInline: 15,
      marginBlockEnd: 20,
    },
    scrollContainer: {
      maxHeight: 450,
    },
    container: {
      backgroundColor: "$screenBackgroundcolor" as any,
    },
    callerNotesText: {
      fontSize: 16,
      fontWeight: 700 as any,
      marginBlockEnd: 10,
    },
    heading: {
      fontWeight: "600" as const,
      marginTop: 8,
      color: "$textcolor" as any,
    },
    body: {
      marginBottom: 8,
      marginTop: 6,
      color: "$soapNotesBodyText" as any,
    },
    innerContainer: {
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 1,
      backgroundColor: "$screenBackgroundcolor" as any,
      padding: 15,
      borderRadius: 10,
    },
  };
};

export default SubmittedAiNotes;
