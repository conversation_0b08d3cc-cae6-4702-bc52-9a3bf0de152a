import React, { useEffect, useState } from "react";
import { View, Text, StyleSheet, Modal, TouchableOpacity } from "react-native";
import io, { Socket } from "socket.io-client";
import { useAuth } from "../context/AuthContext";
import Constants from "expo-constants";

const SOCKET_SERVER_URL = Constants.expoConfig?.extra?.apiUrl;

interface ConsultationRequest {
  consultationId: string;
  // facilityId: string;
  // patientId: string;
  // reason: string;
  // Additional fields if needed
}

const RequestAcceptedSocketListener: React.FC = () => {
  const { user } = useAuth();
  const [socket, setSocket] = useState<Socket | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentNotification, setCurrentNotification] =
    useState<ConsultationRequest | null>(null);

  useEffect(() => {
    if (!user) return;

    const newSocket = io(SOCKET_SERVER_URL, {
      transports: ["websocket"],
      // For now, we're not using authentication.
      // In production, include an auth token if necessary.
    });

    newSocket.on("connect", () => {
      newSocket.emit("joinRoom", `user:${user.id}`);
    });

    newSocket.on("consultationAccepted", (data: ConsultationRequest) => {
      setCurrentNotification(data);
      setModalVisible(true);
    });

    setSocket(newSocket);

    return () => {
      newSocket.disconnect();
      setSocket(null);
    };
  }, [user]);

  const handleCloseModal = () => {
    setModalVisible(false);
  };

  // TODO: use tamagui components for the modal
  return (
    <>
      <Modal
        visible={modalVisible}
        transparent
        animationType="slide"
        onRequestClose={handleCloseModal}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            {currentNotification && (
              <>
                <Text style={styles.modalTitle}>Request Accepted</Text>
                <Text style={styles.modalTitle}>
                  {currentNotification.consultationId}
                </Text>
                <Text style={styles.modalTitle}>
                  The provider is reviewing the notes and will join the call
                  soon
                </Text>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={handleCloseModal}
                >
                  <Text style={styles.closeButtonText}>Close</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={handleCloseModal}
                >
                  <Text style={styles.closeButtonText}>Join Meeting</Text>
                </TouchableOpacity>
              </>
            )}
          </View>
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContent: {
    backgroundColor: "#fff",
    padding: 20,
    borderRadius: 10,
    width: "80%",
    alignItems: "center",
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 10,
  },
  modalText: {
    fontSize: 16,
    marginBottom: 10,
    textAlign: "center",
  },
  closeButton: {
    backgroundColor: "#175CD3",
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
  },
  closeButtonText: {
    color: "#fff",
    fontSize: 16,
  },
});

export default RequestAcceptedSocketListener;
