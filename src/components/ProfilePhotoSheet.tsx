import { Camera, Image as Gallery } from "@tamagui/lucide-icons";
import * as ImagePicker from "expo-image-picker";
import React, { useState } from "react";
import { ActivityIndicator, Alert, Pressable, View } from "react-native";
import { Sheet, Text, XStack, YStack } from "tamagui";
import { useAuth } from "~/context/AuthContext";
import axiosConfig from "../services/axiosConfig";

type ProfilePhotoSheetProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
};

const ProfilePhotoSheet: React.FC<ProfilePhotoSheetProps> = ({
  open,
  setOpen,
}) => {
  const styles = useStyles();
  const spModes = ["percent", "constant", "fit", "mixed"] as const;
  const [position, setPosition] = useState(0);
  const [snapPointsMode, setSnapPointsMode] =
    useState<(typeof spModes)[number]>("percent");
  const { setLocalUser } = useAuth();
  const [image, setImage] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const snapPoints =
    snapPointsMode === "percent"
      ? [25]
      : snapPointsMode === "constant"
        ? [256, 190]
        : snapPointsMode === "fit"
          ? undefined
          : ["80%", 256, 190];

  async function ensurePermissions() {
    const { status: camStatus } =
      await ImagePicker.requestCameraPermissionsAsync();
    const { status: libStatus } =
      await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (camStatus !== "granted" || libStatus !== "granted") {
      Alert.alert(
        "Permission needed",
        "Camera + Photo Library permissions are required to change your profile picture."
      );
      return false;
    }
    return true;
  }

  const handleCamera = async () => {
    if (!(await ensurePermissions())) return;
    let result = await ImagePicker.launchCameraAsync({
      mediaTypes: ["images"],
      allowsEditing: true,
      quality: 1,
    });

    if (!result.canceled) {
      setImage(result.assets[0].uri);
      uploadImage(result.assets[0]);
    }
  };

  const pickImage = async () => {
    if (!(await ensurePermissions())) return;
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ["images"],
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      setImage(result.assets[0].uri);
      uploadImage(result.assets[0]);
    }
  };
  const uploadImage = async (image: any) => {
    try {
      setOpen(false);
      setLoading(true);
      const formData = new FormData();
      formData.append("profilePicture", {
        uri: image.uri,
        name: "profile.jpg",
        type: image.type || "image/jpeg",
      } as any);

      const response = await axiosConfig.post(
        "/user/profile-picture",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      if (response?.status === 200 && response.data?.profilePicUrl) {
        setLocalUser((prev: any) =>
          prev ? { ...prev, profilePicUrl: response.data.profilePicUrl } : prev
        );
      } else {
        alert("Profile Picture Not Updated");
      }
    } catch (err) {
      alert("Profile Picture Not Updated");
    } finally {
      setLoading(false);
    }
  };

  const renderOptionCard = (
    IconComponent: React.ElementType,
    label: string,
    onPress: () => void
  ) => (
    <Pressable onPress={onPress} {...styles.card}>
      <YStack {...styles.cardBody}>
        <IconComponent size={24} color="$primaryColor" />
        <Text mt={12} fontSize={16} color="$primaryColor" fontWeight="600">
          {label}
        </Text>
      </YStack>
    </Pressable>
  );

  return (
    <>
      {loading && (
        <View style={styles.loadingOverlay}>
          <ActivityIndicator size="large" color="#1570EF" />
        </View>
      )}
      {open && <View style={styles.overlay} pointerEvents="none" />}
      <Sheet
        modal
        open={open}
        onOpenChange={setOpen}
        snapPoints={snapPoints}
        snapPointsMode={snapPointsMode}
        dismissOnSnapToBottom
        position={position}
        onPositionChange={setPosition}
        zIndex={100_000}
        animation="quickest"
      >
        <Sheet.Overlay
          animation="quickest"
          {...styles.sheetOverlay}
          enterStyle={{ opacity: 0 }}
          exitStyle={{ opacity: 0 }}
        />

        <Sheet.Handle />
        <Sheet.Frame {...styles.sheetFrame}>
          <YStack {...styles.sheetcontent}>
            <Text {...styles.sheetContenttext}>Profile Picture</Text>
            <XStack {...styles.options}>
              {renderOptionCard(Camera, "Camera", handleCamera)}
              {renderOptionCard(Gallery, "Gallery", pickImage)}
            </XStack>
          </YStack>
        </Sheet.Frame>
      </Sheet>
    </>
  );
};

export default ProfilePhotoSheet;

const useStyles = () => {
  return {
    sheetOverlay: {
      backgroundColor: "transparent" as "transparent",
    },
    sheetFrame: {
      padding: "$5",
      backgroundColor: "$screenBackgroundcolor",
    },
    sheetcontent: {
      alignItems: "center" as any,
      justifyContent: "center" as any,
    },
    sheetContenttext: {
      fontSize: 16,
      fontWeight: "700" as any,
    },
    sheetContentCard: {
      backgroundColor: "$screenBackgroundcolor" as any,
      padding: "$2" as any,
      borderRadius: 10,
      borderWidth: 1,
      borderColor: "$primaryBorderColor" as any,
    },
    options: {
      marginBlock: 20,
    },
    cardContent: {
      alignItems: "center" as any,
      justifyContent: "center" as any,
    },
    card: {
      width: 80,
      height: 80,
      marginInline: 5,
    },
    cardBody: {
      backgroundColor: "transparent",
      borderRadius: 12,
      alignItems: "center",
      justifyContent: "center",
      flex: 1,
      borderWidth: 1,
      borderColor: "$primaryBorderColor" as any,
    },
    overlay: {
      position: "absolute" as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(0,0,0,0.7)",
      zIndex: 100,
    },
    loadingOverlay: {
      position: "absolute" as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: "rgba(0,0,0,0.3)",
      zIndex: 200,
      alignItems: "center" as import("react-native").FlexAlignType,
      justifyContent:
        "center" as import("react-native").ViewStyle["justifyContent"],
    },
  };
};
