import { ArrowLeft } from "@tamagui/lucide-icons";
import React from "react";
import { StyleSheet, TouchableOpacity } from "react-native";
import { Text, View } from "tamagui";

// Define the prop type for Dialpad
interface DialpadProps {
  onKeyPress: (key: number | "backspace") => void;
}

const Dialpad: React.FC<DialpadProps> = ({ onKeyPress }) => {
  const numbers: (number | "backspace" | null)[] = [
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    null,
    0,
    "backspace",
  ];

  return (
    <View style={styles.keypad}>
      {numbers.map((number, index) => {
        if (number === null) {
          return <View key={`empty-${index}`} style={styles.emptyKey} />;
        }

        return (
          <TouchableOpacity
            key={`key-${number}-${index}`}
            style={[styles.key, number === "backspace" && styles.backKey]}
            onPress={() => onKeyPress(number)}
          >
            {number === "backspace" ? (
              <View style={styles.backKeyChild}>
                <ArrowLeft size={24} color="black" />
              </View>
            ) : (
              <Text style={styles.keyText}>{number}</Text>
            )}
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  keypad: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "center",
    backgroundColor: "transparent",
  },
  key: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "#D2D6DB",
    justifyContent: "center",
    alignItems: "center",
    margin: 10,
  },
  backKey: {
    backgroundColor: "transparent",
    width: 80,
    height: 80,
    borderRadius: 7,
  },
  emptyKey: {
    width: 80,
    height: 80,
    margin: 10,
  },
  keyText: {
    fontSize: 24,
    color: "#000",
  },
  backKeyChild: {
    alignItems: "center",
    justifyContent: "center",
    width: 40,
    height: 40,
    borderRadius: 7,
    borderColor: "#D0D5DD",
    borderWidth: 1,
  },
});

export default Dialpad;
