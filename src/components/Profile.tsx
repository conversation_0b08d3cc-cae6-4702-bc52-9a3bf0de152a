import { <PERSON><PERSON>, <PERSON><PERSON>, Card, Text, View, YStack } from "tamagui";
import { memo, useEffect, useState } from "react";
import Title from "./Title";
import EditProfile from "./EditProfile";
import { useAuth } from "~/context/AuthContext";
import { Pencil } from "@tamagui/lucide-icons";

type ProfileProps = {
  onOpenSheet: (value: boolean) => void;
};

export default function Profile({ onOpenSheet }: ProfileProps) {
  return <ProfileSection onOpenSheet={onOpenSheet} />;
}

const ProfileSection = memo(({ onOpenSheet }: ProfileProps) => {
  const { localUser } = useAuth();
  const initials = localUser
    ? localUser?.firstName?.charAt(0) + localUser?.lastName?.charAt(0)
    : "User";
  const [isEditing, setIsEditing] = useState(false);
  const [profileImage, setProfileImage] = useState<string>("");
  useEffect(() => {
    if (
      localUser?.profilePicUrl !== null &&
      localUser?.profilePicUrl !== undefined &&
      localUser?.profilePicUrl !== ""
    ) {
      setProfileImage(localUser?.profilePicUrl);
    }
  }, [localUser?.profilePicUrl]);

  return (
    <YStack {...styles.profileContainer}>
      <Card {...styles.profileCard}>
        <YStack {...styles.profileContent}>
          <View {...styles.cardContentView}>
            <Avatar
              circular
              size="$6"
              onPress={() =>
                isEditing ? onOpenSheet(true) : onOpenSheet(false)
              }
            >
              {profileImage ? (
                <Avatar.Image source={{ uri: profileImage }} />
              ) : (
                <Avatar.Fallback
                  style={{
                    backgroundColor: "#1570EF",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <Text style={{ color: "white" }}>{initials}</Text>
                </Avatar.Fallback>
              )}
            </Avatar>
            {isEditing && (
              <View
                {...styles.editPencil}
                onPress={() =>
                  isEditing ? onOpenSheet(true) : onOpenSheet(false)
                }
              >
                <Pencil size={16} color="#fff" />
              </View>
            )}
          </View>
          <Text {...styles.profileText}>
            {localUser?.firstName + " " + localUser?.lastName}
          </Text>
          {localUser?.role !== "nurse" && (
            <Text {...styles.emailText}>{localUser?.email}</Text>
          )}
          <YStack {...styles.badge}>
            <Title
              text={localUser?.role === "nurse" ? "Nurse" : "Provider"}
              backgroundColor="#F8F9FC"
              borderColor="#D5D9EB"
            />
          </YStack>
          {!isEditing && (
            <Button {...styles.editButton} onPress={() => setIsEditing(true)}>
              Edit Profile
            </Button>
          )}
        </YStack>
        {isEditing && (
          <YStack marginInline={20} marginBlock={5}>
            <EditProfile onClose={() => setIsEditing(false)} />
          </YStack>
        )}
      </Card>
    </YStack>
  );
});

const styles = {
  badge: {
    marginBlockStart: 10,
  },
  separator: {
    height: 1,
    backgroundColor: "$primaryBorderColor",
  },
  profileContainer: {
    borderRadius: "$5" as "$5",
    backgroundColor: "transparent",
    borderColor: "$primaryBorderColor" as any,
    borderWidth: 1,
  },
  profileCard: {
    paddingBlock: 0,
    backgroundColor: "transparent" as "transparent",
  },
  profileContent: {
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  profileText: {
    fontWeight: 500 as any,
    fontSize: 14,
    marginBlock: 10,
  },
  emailText: {
    fontWeight: 400 as any,
    fontSize: 14,
  },
  editButton: {
    backgroundColor: "$screenBackgroundcolor" as any,
    borderWidth: 1,
    borderColor: "$primaryBorderColor" as any,
    paddingInline: 25,
    marginBlock: 10,
    fontWeight: 600 as any,
    fontSize: 14,
    size: "$4" as any,
  },
  cardContentView: {
    position: "relative" as any,
    width: 96,
    height: 96,
    alignItems: "center" as any,
    justifyContent: "center" as any,
  },
  editPencil: {
    position: "absolute" as any,
    right: 12,
    bottom: 12,
    backgroundColor: "$primaryColor" as any,
    borderRadius: 12,
    padding: 2,
    borderWidth: 2,
    borderColor: "#FFFFFF" as any,
    alignItems: "center" as any,
    justifyContent: "center" as any,
  },
};
