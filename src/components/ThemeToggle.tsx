import { <PERSON>Left } from "@tamagui/lucide-icons";
import {
  Button,
  Label,
  Switch,
  Text,
  XStack,
  YStack,
  createSwitch,
} from "tamagui";
import { useTheme } from "@/_layout";
import { Moon, Sun } from "lucide-react-native";

export const ThemeToggle = () => {
  const { setTheme, theme } = useTheme();
  const dashboardStyles = useSwitchStyles();
  const { toggleStack, switchStyle, switchThumbStyle } = dashboardStyles;

  const handleThemeToggle = () => {
    const colorPalette = theme === "light" ? "dark" : "light";
    setTheme(colorPalette);
  };

  return (
    <XStack {...toggleStack}>
      <Switch
        {...switchStyle}
        checked={theme === "dark"}
        onCheckedChange={handleThemeToggle}
      >
        <Switch.Thumb {...switchThumbStyle}>
          {theme === "dark" ? (
            <Moon
              size={10}
              color="white"
              style={{
                backgroundColor: "black",
                borderRadius: "50%",
                padding: 8,
                margin: "auto",
              }}
            />
          ) : (
            <Sun
              size={10}
              color="black"
              style={{
                backgroundColor: "white",
                borderRadius: "50%",
                padding: 8,
                margin: "auto",
              }}
            />
          )}
        </Switch.Thumb>
      </Switch>
    </XStack>
  );
};

export const useSwitchStyles = () => {
  return {
    toggleStack: { alignItems: "center" as any, gap: "$2" as any },
    switchStyle: {
      size: "$2" as any,
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 0.5,
    },
    switchThumbStyle: {
      animation: "bouncy" as any,
      backgroundColor: "white" as any,
      size: "$2" as any,
      marginTop: "auto" as any,
      marginBottom: "auto" as any,
    },
  };
};
