import { useTheme } from "@/_layout";
import { ChevronDown } from "@tamagui/lucide-icons";
import { useState, useEffect } from "react";
import { Accordion, Square, Text, View, XStack, YStack } from "tamagui";
import PhysicalExamType from "./PhysicalExamType";
interface PhysicalExamProps {
  physicalExam: any;
  setPhysicalExam: (value: any) => void;
  isSubmitted: boolean;
}

export default function PhysicalExam({
  physicalExam,
  setPhysicalExam,
  isSubmitted = false,
}: PhysicalExamProps) {
  const [isOpen, setIsOpen] = useState(false);
  const styles = getStyles();
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";
  const updatePhysicalExam = (title: string, issue: string, value: string) => {
    setPhysicalExam((prev: any) => ({
      ...prev,
      [title]: {
        ...prev[title],
        [issue]: value,
      },
    }));
  };

  const updateOtherIssues = (sectionTitle: string, newItem: string) => {
    setPhysicalExam((prev: any) => {
      // Get current "Other" array for this section or initialize empty array
      const currentOther = Array.isArray(prev[sectionTitle]?.Other)
        ? prev[sectionTitle].Other
        : [];

      // Skip if empty or duplicate
      if (!newItem.trim() || currentOther.includes(newItem.trim())) {
        return prev;
      }

      return {
        ...prev,
        [sectionTitle]: {
          ...prev[sectionTitle],
          Other: [...currentOther, newItem.trim()],
        },
      };
    });
  };

  const removeOtherItem = (sectionTitle: string, itemToRemove: string) => {
    setPhysicalExam((prev: any) => {
      const currentOther = Array.isArray(prev[sectionTitle]?.Other)
        ? prev[sectionTitle].Other
        : [];

      // Filter out the item to remove
      const updatedOther = currentOther.filter(
        (item: any) => item !== itemToRemove
      );

      return {
        ...prev,
        [sectionTitle]: {
          ...prev[sectionTitle],
          Other: updatedOther,
        },
      };
    });
  };

  const sectionTitles = physicalExam ? Object.keys(physicalExam) : [];

  return (
    <View {...styles.container}>
      <Accordion
        overflow="hidden"
        borderWidth={1}
        type="multiple"
        {...styles.accordion}
        borderColor={isDarkMode ? "#697586" : "#D0D5DD"}
      >
        <Accordion.Item value="a1">
          <Accordion.Trigger
            {...styles.trigger}
            onPress={() => setIsOpen(!isOpen)}
          >
            {({ open }: { open: boolean }) => (
              <XStack {...styles.headerRow}>
                <YStack>
                  <Text {...styles.headerText}>Physical Exam</Text>
                </YStack>
                <Square animation="quick" rotate={open ? "180deg" : "0deg"}>
                  <ChevronDown size="$1" />
                </Square>
              </XStack>
            )}
          </Accordion.Trigger>
          <Accordion.HeightAnimator animation="medium">
            <Accordion.Content {...styles.content}>
              <YStack {...styles.innerContainer}>
                {sectionTitles?.map((sectionTitle) => (
                  <PhysicalExamType
                    key={sectionTitle}
                    title={sectionTitle}
                    issues={physicalExam[sectionTitle]}
                    updatePhysicalExam={updatePhysicalExam}
                    isSubmitted={isSubmitted}
                    updateOtherIssues={updateOtherIssues}
                    removeOtherItem={removeOtherItem}
                  />
                ))}
              </YStack>
            </Accordion.Content>
          </Accordion.HeightAnimator>
        </Accordion.Item>
      </Accordion>
    </View>
  );
}

const getStyles = () => {
  return {
    container: {
      marginBlockStart: 20,
    },
    accordion: {
      borderRadius: "$6" as "$6",
    },
    trigger: {
      flexDirection: "row" as "row",
      borderWidth: 0,
      backgroundColor: "$screenBackgroundcolor",
    },
    headerRow: {
      justifyContent: "space-between",
      flex: 1,
    },
    content: {
      animation: "medium" as "medium",
      exitStyle: { opacity: 0 },
      backgroundColor: "$screenBackgroundcolor",
    },
    innerContainer: {
      flex: 1,
    },
    headerText: {
      fontSize: 16,
      fontWeight: 600 as any,
    },
  };
};
