import { Card, Text, XStack, YStack } from "tamagui";

interface TranscriptMessageProps {
  messenger: string;
  time: string;
  message: string;
}

export default function TranscriptMessage({
  messenger,
  time,
  message,
}: TranscriptMessageProps) {
  const contentStyle = {
    justifyContent: "space-between",
  };

  const marginTop = {
    marginTop: "8",
  };

  return (
    <Card
      marginBlock={10}
      borderColor={"$primaryBorderColor"}
      paddingBlock={15}
      paddingInline={15}
      borderWidth={2}
      borderRadius={10}
      backgroundColor={"$screenBackgroundcolor"}
    >
      <YStack>
        <XStack {...contentStyle}>
          <Text fontWeight="bold" color={"$textcolor"}>
            {messenger}
          </Text>
          <Text color={"$primaryBorderColor"}>{time}</Text>
        </XStack>
        <XStack {...marginTop}>
          <Text color={"$textcolor"}>{message}</Text>
        </XStack>
      </YStack>
    </Card>
  );
}
