import { useTheme } from "@/_layout";
import { ChevronDown } from "@tamagui/lucide-icons";
import React, {
  useRef,
  useState,
  useMemo,
  useCallback,
  useEffect,
} from "react";
import {
  Text,
  TouchableOpacity,
  TextInput,
  FlatList,
  StyleSheet,
  View,
  Keyboard,
} from "react-native";
import { YStack, Square } from "tamagui";

interface SelectableItem {
  id: string;
  name: string;
}

interface FacilityDrawerProps {
  data: SelectableItem[] | undefined;
  placeholder?: string;
  onSelect: (id: string) => void;
  onOpen?: () => void; // Callback when the drawer is opened
  onClose?: () => void; // Callback when the drawer is closed
  isOpen?: boolean; // Controlled open state
}

const FacilityDrawer: React.FC<FacilityDrawerProps> = ({
  data = [],
  placeholder = "Select Facility",
  onSelect,
  onOpen,
  onClose,
  isOpen = false,
}) => {
  const [search, setSearch] = useState<string>("");
  const [clicked, setClicked] = useState<boolean>(false);
  const [selectedFacility, setSelectedFacility] = useState<string>("");
  const searchRef = useRef<TextInput>(null);
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";
  const styles = getStyles(isDarkMode);
  // Sync the `clicked` state with the `isOpen` prop
  useEffect(() => {
    setClicked(isOpen);
  }, [isOpen]);

  // Handle opening the drawer
  const handleOpen = () => {
    if (!clicked) {
      setClicked(true);
      onOpen?.(); // Notify parent that the drawer is open
    }
  };

  // Handle closing the drawer
  const handleClose = () => {
    setClicked(false);
    onClose?.(); // Notify parent that the drawer is closed
  };

  // Handle selecting an item
  const handleSelect = useCallback(
    (item: SelectableItem) => {
      onSelect(item.id);
      setSelectedFacility(item.name);
      handleClose(); // Close the drawer after selection
      setSearch("");
      Keyboard.dismiss();
    },
    [onSelect]
  );

  // Filter data based on search input
  const filteredData = useMemo(() => {
    return search
      ? data?.filter((item) =>
          item.name.toLowerCase().includes(search.toLowerCase())
        )
      : data;
  }, [search, data]);

  return (
    <View style={styles.container}>
      {/* Select Button */}
      <TouchableOpacity
        style={styles.selectButton}
        onPress={() => {
          if (clicked) {
            handleClose();
          } else {
            handleOpen();
          }
          Keyboard.dismiss();
        }}
      >
        <Text style={styles.selectedText}>
          {selectedFacility === "" ? placeholder : selectedFacility}
        </Text>
        <Square animation="quick" rotate={clicked ? "180deg" : "0deg"}>
          <ChevronDown size={20} color={isDarkMode ? "white" : "black"} />
        </Square>
      </TouchableOpacity>

      {/* Dropdown List */}
      {clicked && (
        <YStack style={styles.dropdownContainer}>
          <YStack style={styles.dropdownContainerChild}>
            {/* Show Search Input only if data has more than 5 items */}
            {data.length > 5 && (
              <TextInput
                placeholder="Search..."
                value={search}
                ref={searchRef}
                onChangeText={setSearch}
                style={styles.searchInput}
              />
            )}

            {/* List of Facilities */}
            <FlatList
              data={filteredData}
              keyExtractor={(item) => item.id}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.listItem}
                  onPress={() => handleSelect(item)}
                >
                  <Text style={styles.listItemText}>{item.name}</Text>
                </TouchableOpacity>
              )}
              scrollEnabled
              keyboardShouldPersistTaps="handled"
              ListEmptyComponent={() => (
                <Text style={styles.emptyText}>No results found</Text>
              )}
            />
          </YStack>
        </YStack>
      )}
    </View>
  );
};

const getStyles = (isDarkMode: boolean) =>
  StyleSheet.create({
    container: {
      width: "100%",
      position: "relative",
    },
    selectButton: {
      width: "100%",
      height: 50,
      borderRadius: 10,
      borderWidth: 0.5,
      borderColor: isDarkMode ? "#697586" : "#D0D5DD",
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingHorizontal: 15,
      backgroundColor: "transparent",
    },
    selectedText: {
      fontWeight: "500",
      fontSize: 16,
      color: isDarkMode ? "white" : "black",
    },
    dropdownContainer: {
      position: "absolute",
      top: 60,
      left: 0,
      right: 0,
      zIndex: 999,
      maxHeight: 250,
      shadowColor: "#000",
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.1,
      shadowRadius: 5,
      elevation: 5,
    },
    dropdownContainerChild: {
      borderWidth: 1,
      borderColor: isDarkMode ? "#697586" : "#D0D5DD",
      borderRadius: 8,
      backgroundColor: isDarkMode ? "#1E1E1E" : "white",
    },
    searchInput: {
      height: 50,
      borderWidth: 0.5,
      borderColor: isDarkMode ? "#697586" : "#D0D5DD",
      paddingHorizontal: 15,
      borderRadius: 7,
      backgroundColor: isDarkMode ? "#1E1E1E" : "white",
      margin: 15,
      color: isDarkMode ? "white" : "black",
    },
    listItem: {
      paddingVertical: 15,
      paddingHorizontal: 20,
    },
    listItemText: {
      fontWeight: "600",
      color: isDarkMode ? "white" : "black",
    },
    emptyText: {
      textAlign: "center",
      padding: 10,
      fontSize: 16,
      color: isDarkMode ? "#aaa" : "#888",
    },
  });

export default FacilityDrawer;
