import React, { useState } from "react";
import { Button, YStack, Text } from "tamagui";
import { Info } from "@tamagui/lucide-icons";
import { Modal, View } from "react-native";
import { VersionInfoModal } from "./VersionInfoModal";

export const VersionInfoButton = () => {
  const [modalOpen, setModalOpen] = useState(false);

  const styles = {
    container: {
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 1,
      borderRadius: 8,
      marginBlockStart: 20,
    },
    contentContainer: {
      marginBlock: 20,
      marginInline: 20,
    },
    title: {
      fontWeight: "600" as any,
      fontSize: 20,
      marginBlockEnd: 10,
    },
    button: {
      backgroundColor: "$primaryColor" as any,
      color: "white" as any,
      size: "$4" as any,
      fontWeight: "600" as any,
    },
  };

  return (
    <>
      <YStack {...styles.container}>
        <YStack {...styles.contentContainer}>
          <Text {...styles.title}>App Status</Text>

          <Button
            {...styles.button}
            icon={<Info size={18} color="white" />}
            onPress={() => setModalOpen(true)}
          >
            View App Status
          </Button>
        </YStack>
      </YStack>

      <Modal visible={modalOpen} transparent animationType="fade">
        <View
          style={{
            flex: 1,
            justifyContent: "center",
            alignItems: "center",
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            width: "100%",
            height: "100%",
          }}
        >
          <VersionInfoModal open={modalOpen} onClose={setModalOpen} />
        </View>
      </Modal>
    </>
  );
};
