import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  SwitchCamera,
  Video,
  VideoOff,
} from "@tamagui/lucide-icons";
import {
  Errors,
  EventType,
  VideoAspect,
  VideoResolution,
  ZoomVideoSdkLiveTranscriptionMessageInfo,
  ZoomVideoSdkProvider,
  ZoomVideoSdkUser,
  ZoomView,
  useZoom,
} from "@zoom/react-native-videosdk";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  ActivityIndicator,
  Alert,
  Platform,
  StyleSheet,
  TouchableOpacity,
  useWindowDimensions,
} from "react-native";
import useSendTranscript from "src/hooks/useSendConsultation";
import { Text, View } from "tamagui";
import CallKitService from "~/services/CallKitService";
import { useAuth } from "../context/AuthContext";
import axiosConfig from "../services/axiosConfig";
import { CustomNotification } from "./CustomNotification";
import { Notification } from "./Notification";

// Types
interface NurseCallProps {
  consultationId: string;
  sdkToken: string;
  onCallEnd: () => void;
  onCallOverview: () => void;
  onRetryRequest?: () => Promise<string>; // Returns new SDK token
}

interface TranscriptMessage {
  speaker: string;
  text: string;
  timestamp: string;
}

interface CallState {
  loading: boolean;
  callStarted: boolean;
  callEnded: boolean;
  videoOn: boolean;
  audioOn: boolean;
  isRecovering: boolean;
  showRetryButton: boolean;
  disableRetry: boolean;
  retryCount: number;
  nurseJoined: boolean;
  isProviderInCall: boolean;
  providerLeft: boolean;
  endingCall: boolean;
}

// Logging utility
const logEvent = (
  level: string,
  status: string,
  message: string,
  metadata: object = {}
) => {
  axiosConfig.post("/log-event", {
    level,
    status,
    name: "NurseCallComponent",
    message,
    metadata,
  });
};

const NurseCallComponent: React.FC<NurseCallProps> = ({
  consultationId,
  sdkToken,
  onCallEnd,
  onCallOverview,
  onRetryRequest,
}) => {
  const { width, height } = useWindowDimensions();
  const isLandscape = width > height;
  const { user } = useAuth();

  const {
    joinSession,
    leaveSession,
    addListener,
    session,
    audioHelper,
    videoHelper,
    liveTranscriptionHelper,
  } = useZoom();

  // State
  const [callState, setCallState] = useState<CallState>({
    loading: true,
    callStarted: false,
    callEnded: false,
    videoOn: true,
    audioOn: true,
    isRecovering: false,
    showRetryButton: false,
    disableRetry: true,
    retryCount: 0,
    nurseJoined: false,
    isProviderInCall: false,
    providerLeft: false,
    endingCall: false,
  });

  const [localUser, setLocalUser] = useState<ZoomVideoSdkUser | null>(null);
  const [remoteUsers, setRemoteUsers] = useState<ZoomVideoSdkUser | null>(null);
  const [remoteUserAudio, setRemoteUserAudio] = useState<boolean>(false);
  const [localUserName, setLocalUserName] = useState<string>("");
  const [remoteUserName, setRemoteUserName] = useState<string>("");
  const [viewKey, setViewKey] = useState(0);
  // const [transcriptHistory, setTranscriptHistory] = useState<
  //   TranscriptMessage[]
  // >([]);
  const { sendTranscript } = useSendTranscript();
  const [notification, setNotification] = useState<{
    message: string;
    visible: boolean;
  }>({
    message: "",
    visible: false,
  });

  const listeners = useRef<any[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Update call state helper
  const updateCallState = useCallback((updates: Partial<CallState>) => {
    if (updates.callStarted) {
      setTimeout(() => {
        setCallState((prev) => ({
          ...prev,
          showRetryButton: true,
        }));
      }, 9000);
    }
    setCallState((prev) => ({ ...prev, ...updates }));
  }, []);

  // Audio controls
  const toggleAudio = useCallback(async () => {
    try {
      const mySelf = await session.getMySelf();
      const muted = await mySelf.audioStatus.isMuted();
      if (muted) {
        await audioHelper.unmuteAudio(mySelf.userId);
      } else {
        await audioHelper.muteAudio(mySelf.userId);
      }
    } catch (err) {
      logEvent(
        "ERROR",
        "TOGGLE_AUDIO_FAILED",
        "=== NURSE === Failed to toggle audio",
        {
          error: err,
        }
      );
    }
  }, [session, audioHelper]);

  const toggleVideo = useCallback(async () => {
    try {
      const mySelf = await session.getMySelf();
      const currentVideoOn = await mySelf.videoStatus.isOn();
      if (currentVideoOn) {
        await videoHelper.stopVideo();
      } else {
        await videoHelper.startVideo();
      }
    } catch (err) {
      logEvent(
        "ERROR",
        "TOGGLE_VIDEO_FAILED",
        "=== NURSE === Failed to toggle video",
        {
          error: err,
        }
      );
    }
  }, [session, videoHelper]);

  const switchCamera = useCallback(async () => {
    try {
      const mySelf = await session.getMySelf();
      await videoHelper.switchCamera(mySelf.userId);
    } catch (err) {
      logEvent(
        "ERROR",
        "SWITCH_CAMERA_FAILED",
        "=== NURSE === Failed to switch camera",
        {
          error: err,
        }
      );
    }
  }, [session, videoHelper]);

  // Transcription
  const startTranscription = useCallback(async () => {
    try {
      const canStart =
        await liveTranscriptionHelper.canStartLiveTranscription();
      if (!canStart) {
        logEvent(
          "WARN",
          "TRANSCRIPTION_PERMISSION_DENIED",
          "=== NURSE === Cannot start transcription"
        );
        return;
      }

      const result = await liveTranscriptionHelper.startLiveTranscription();
      if (result !== Errors.Success) {
        logEvent(
          "ERROR",
          "TRANSCRIPTION_FAILED",
          `=== NURSE === Failed to start transcription: ${result}`,
          { result }
        );
        return;
      }

      // Configure transcription
      await liveTranscriptionHelper.setSpokenLanguage(0);
      await liveTranscriptionHelper.setTranslationLanguage(0);
      await liveTranscriptionHelper.enableReceiveSpokenLanguageContent(true);

      logEvent(
        "INFO",
        "TRANSCRIPTION_STARTED",
        "=== NURSE === Live transcription started successfully"
      );
    } catch (error) {
      logEvent(
        "ERROR",
        "TRANSCRIPTION_ERROR",
        "=== NURSE === Error in startTranscription",
        error as object
      );
    }
  }, [liveTranscriptionHelper, addListener]);

  // Session management
  const setupSessionListeners = useCallback(async () => {
    try {
      // Session join listener
      const sessionJoinListener = addListener(
        EventType.onSessionJoin,
        async () => {
          const mySelf = new ZoomVideoSdkUser(await session.getMySelf());
          const remoteUsers = await session.getRemoteUsers();
          const mySelfName = await mySelf.getUserName();
          const remoteUserName = await remoteUsers?.[0]?.getUserName();

          setLocalUserName(mySelfName);
          setRemoteUserName(remoteUserName);
          setRemoteUsers(remoteUsers?.[0]);
          setLocalUser(mySelf);
          updateCallState({ nurseJoined: true });

          await startTranscription();
          logEvent(
            "INFO",
            "SESSION_JOINED",
            "=== NURSE === Successfully joined session"
          );
        }
      );

      // User join listener
      const userJoinListener = addListener(
        EventType.onUserJoin,
        async (event) => {
          const mySelf = new ZoomVideoSdkUser(await session.getMySelf());
          const remoteUsers = await session.getRemoteUsers();
          const joinedUser = event?.joinedUsers?.[0];

          updateCallState({
            isProviderInCall: remoteUsers?.length > 0,
            providerLeft: false,
          });
          setRemoteUsers(remoteUsers?.[0]);

          if (joinedUser?.userId !== mySelf?.userId) {
            setNotification({
              message: `${joinedUser?.userName || "Provider"} joined the call`,
              visible: true,
            });
          }
        }
      );

      // User leave listener
      const userLeaveListener = addListener(
        EventType.onUserLeave,
        async (event) => {
          const mySelf = new ZoomVideoSdkUser(await session.getMySelf());
          const remoteUsers = await session.getRemoteUsers();
          const leftUser = event?.leftUsers?.[0];

          updateCallState({
            isProviderInCall: remoteUsers?.length > 0,
            providerLeft: true,
          });
          setRemoteUsers(remoteUsers?.[0]);

          if (leftUser?.userId !== mySelf.userId) {
            setNotification({
              message: `${leftUser?.userName || "Provider"} left the call`,
              visible: true,
            });
          }
        }
      );

      // Audio status listener
      const userAudioListener = addListener(
        EventType.onUserAudioStatusChanged,
        async (event) => {
          const { changedUsers } = event;
          const mySelf = new ZoomVideoSdkUser(await session.getMySelf());
          const remoteUsers = await session.getRemoteUsers();

          changedUsers.forEach(async (changedUser) => {
            if (changedUser.userId === mySelf.userId) {
              const muted = await mySelf.audioStatus.isMuted();
              updateCallState({ audioOn: !muted });
            } else {
              try {
                const foundRemoteUser = remoteUsers.find(
                  (u: ZoomVideoSdkUser) => u.userId === changedUser.userId
                );
                if (foundRemoteUser) {
                  const zoomRemoteUser = new ZoomVideoSdkUser(foundRemoteUser);
                  const muted = await zoomRemoteUser.audioStatus.isMuted();
                  setRemoteUserAudio(!muted);
                }
              } catch (err) {
                logEvent(
                  "ERROR",
                  "REMOTE_AUDIO_STATUS_ERROR",
                  "=== NURSE === Error checking remote audio",
                  { error: err }
                );
              }
            }
          });
        }
      );

      // Video status listener
      const userVideoListener = addListener(
        EventType.onUserVideoStatusChanged,
        async (event) => {
          const { changedUsers } = event;
          const mySelf = new ZoomVideoSdkUser(await session.getMySelf());
          if (changedUsers.find((u) => u.userId === mySelf.userId)) {
            const on = await mySelf.videoStatus.isOn();
            updateCallState({ videoOn: on });
          }
        }
      );

      // Session leave listener
      const sessionLeaveListener = addListener(EventType.onSessionLeave, () => {
        updateCallState({
          callStarted: false,
          nurseJoined: false,
          isProviderInCall: false,
          providerLeft: false,
        });
        setLocalUserName("");
        setRemoteUserName("");
        setRemoteUsers(null);
        setLocalUser(null);
        cleanupListeners();
      });

      // Error listener
      const errorListener = addListener(EventType.onError, (errEvent) => {
        logEvent(
          "ERROR",
          "ZOOM_SDK_ERROR",
          `=== NURSE === ZoomSDK error occurred: ${JSON.stringify(errEvent, null, 2)}`,
          {
            error: errEvent,
          }
        );
        updateCallState({ showRetryButton: true });
      });

      // Network quality listener
      const networkListener = addListener(
        EventType.onUserVideoNetworkStatusChanged,
        async ({ user: netUser, result }) => {
          const me = await session.getMySelf();
          if (netUser.userId === me.userId && result.uplinkNetworkQuality < 2) {
            logEvent(
              "WARN",
              "NETWORK_DROP",
              "=== NURSE === Network quality low, restarting video"
            );
            updateCallState({ isRecovering: true });
            try {
              await videoHelper.stopVideo();
              await videoHelper.startVideo();
            } finally {
              updateCallState({ isRecovering: false });
            }
            logEvent(
              "INFO",
              "NETWORK_RECOVER",
              "=== NURSE === Video restarted after network drop"
            );
          }
        }
      );

      // Canvas fail listener
      const canvasFailListener = addListener(
        EventType.onVideoCanvasSubscribeFail,
        ({ canvasId }) => {
          logEvent(
            "WARN",
            "CANVAS_SUBSCRIBE_FAIL",
            `=== NURSE === Canvas ${canvasId} failed, remounting view`
          );
          setViewKey((k) => k + 1);
        }
      );

      listeners.current.push(
        sessionJoinListener,
        userJoinListener,
        userLeaveListener,
        userAudioListener,
        userVideoListener,
        sessionLeaveListener,
        errorListener,
        networkListener,
        canvasFailListener
      );

      logEvent(
        "INFO",
        "LISTENERS_SETUP",
        "=== NURSE === All session listeners set up successfully"
      );
    } catch (error) {
      logEvent(
        "ERROR",
        "LISTENERS_SETUP_ERROR",
        "=== NURSE === Error setting up session listeners",
        error as object
      );
      throw error;
    }
  }, [addListener, session, videoHelper, startTranscription, updateCallState]);

  useEffect(() => {
    const errListener = addListener(EventType.onError, (errEvent) => {
      logEvent(
        "ERROR",
        "ZOOM_SDK_ERROR",
        `ZoomSDK error occurred - nurse ==== ${JSON.stringify(errEvent, null, 2)}`,
        {
          error: errEvent,
        }
      );
    });
    return () => {
      errListener.remove();
    };
  }, [addListener]);

  const cleanupListeners = useCallback(() => {
    listeners.current.forEach((listener) => listener.remove());
    listeners.current = [];
  }, []);

  const joinSessionHandler = useCallback(async () => {
    if (!consultationId || !sdkToken) {
      logEvent(
        "ERROR",
        "MISSING_PARAMS",
        "=== NURSE === Missing consultationId or sdkToken"
      );
      Alert.alert("Error", "Missing required parameters for joining session.");
      updateCallState({ loading: false });
      return false;
    }

    updateCallState({ loading: true });

    // End CallKit call on iOS
    if (Platform.OS === "ios") {
      try {
        await CallKitService.endCurrentCallKitCall();
      } catch (err) {
        logEvent(
          "WARN",
          "CALLKIT_END_FAILED",
          "=== NURSE === Failed to end CallKit call",
          {
            error: err,
          }
        );
      }
    }

    try {
      const name = `${user?.firstName} ${user?.lastName?.charAt(0) || ""}`;

      await joinSession({
        sessionName: consultationId,
        userName: name,
        token: sdkToken,
        sessionIdleTimeoutMins: 10,
        audioOptions: {
          connect: true,
          mute: false,
          autoAdjustSpeakerVolume: false,
        },
        videoOptions: { localVideoOn: true },
      });

      await setupSessionListeners();
      await audioHelper.startAudio();

      updateCallState({ callStarted: true });
      logEvent(
        "INFO",
        "JOIN_SESSION_SUCCESS",
        "=== NURSE === Successfully joined session"
      );
      return true;
    } catch (error) {
      logEvent(
        "ERROR",
        "JOIN_SESSION_ERROR",
        "=== NURSE === Failed to join session",
        error as object
      );
      updateCallState({
        showRetryButton: true,
        retryCount: callState.retryCount + 1,
      });

      if (callState.retryCount >= 2) {
        logEvent(
          "ERROR",
          "JOIN_SESSION_MAX_RETRIES",
          "=== NURSE === Nurse failed to join session after multiple attempts"
        );
        Alert.alert(
          "Connection Error - steps required",
          "Failed to join the call after multiple attempts. Please try the following steps:\n\n1. Close and clear the app completely by swiping up and swiping the app away.\n2. Reopen the app and try joining the call again."
        );
      }
      return false;
    } finally {
      updateCallState({ loading: false });
      enableRetryButton();
    }
  }, [
    consultationId,
    sdkToken,
    user,
    joinSession,
    setupSessionListeners,
    audioHelper,
    updateCallState,
    callState.retryCount,
  ]);

  const fetchTranscriptionMessages = async () => {
    try {
      const messages =
        await liveTranscriptionHelper.getHistoryTranslationMessageList();
      const formattedTranscript = messages.map(
        (msg: ZoomVideoSdkLiveTranscriptionMessageInfo) => {
          const isNurse = msg.speakerName === "Nurse";
          return {
            speaker: msg.speakerName ?? "Unknown",
            text: msg.messageContent || "N/A",
            timestamp: new Date(Number(msg.timeStamp) * 1000).toISOString(),
          };
        }
      );
      const allMessages = formattedTranscript.sort(
        (a: { timestamp: string }, b: { timestamp: string }) =>
          new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );

      await sendTranscript(consultationId, allMessages);
    } catch (error) {
      logEvent(
        "ERROR",
        "FETCH_TRANSCRIPTIONS_ERROR",
        "Error fetching transcription messages",
        error as object
      );
    }
  };

  const leaveSessionHandler = useCallback(async () => {
    try {
      updateCallState({ loading: true, endingCall: true });

      try {
        await fetchTranscriptionMessages();
      } catch (err) {
        logEvent(
          "WARN",
          "FETCH_TRANSCRIPTION_FAILED",
          "=== NURSE === Failed to fetch transcription messages",
          { error: err }
        );
      }
      // Stop transcription
      try {
        await liveTranscriptionHelper.stopLiveTranscription();
      } catch (err) {
        logEvent(
          "WARN",
          "STOP_TRANSCRIPTION_FAILED",
          "=== NURSE === Failed to stop transcription",
          { error: err }
        );
      }

      // Stop audio/video
      try {
        await audioHelper.stopAudio();
        await videoHelper.stopVideo();
      } catch (err) {
        logEvent(
          "WARN",
          "STOP_AV_FAILED",
          "=== NURSE === Failed to stop audio/video",
          {
            error: err,
          }
        );
      }

      // Clear timers
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }

      // Cleanup listeners
      cleanupListeners();

      // Leave session
      await leaveSession();

      // Small delay for native teardown
      await new Promise((r) => setTimeout(r, 800));

      // Reset state
      updateCallState({
        callStarted: false,
        callEnded: true,
        videoOn: true,
        audioOn: true,
        isRecovering: false,
        nurseJoined: false,
        isProviderInCall: false,
        providerLeft: false,
      });

      setLocalUser(null);
      setRemoteUsers(null);
      setRemoteUserAudio(false);
      setLocalUserName("");
      setRemoteUserName("");

      logEvent(
        "INFO",
        "LEAVE_SESSION_SUCCESS",
        "=== NURSE === Successfully left session"
      );
    } catch (error) {
      logEvent(
        "ERROR",
        "LEAVE_SESSION_ERROR",
        "=== NURSE === Error leaving session",
        error as object
      );
      Alert.alert("Error", "Failed to leave the session properly");
    } finally {
      // updateCallState({ loading: false });
      onCallEnd();
    }
  }, [
    liveTranscriptionHelper,
    audioHelper,
    videoHelper,
    leaveSession,
    cleanupListeners,
    updateCallState,
    onCallEnd,
  ]);

  const retryConnection = useCallback(async () => {
    logEvent("INFO", "MANUAL_RETRY", "=== NURSE === User initiated retry");
    updateCallState({
      loading: true,
      disableRetry: true,
      showRetryButton: false,
      callEnded: false,
    });

    try {
      // First try to leave cleanly
      try {
        cleanupListeners();
        await leaveSession();
        await new Promise((r) => setTimeout(r, 1000));
      } catch (err) {
        logEvent(
          "WARN",
          "RETRY_LEAVE_FAILED",
          "=== NURSE === Failed to leave session during retry",
          { error: err }
        );
      }

      // Get new token if retry function provided
      let newToken = sdkToken;
      if (onRetryRequest) {
        try {
          newToken = await onRetryRequest();
          logEvent(
            "INFO",
            "NEW_TOKEN_RECEIVED",
            "=== NURSE === Received new SDK token for retry"
          );
        } catch (err) {
          logEvent(
            "ERROR",
            "RETRY_TOKEN_FAILED",
            "=== NURSE === Failed to get new token",
            {
              error: err,
            }
          );
        }
      }

      // Reset state
      setLocalUser(null);
      setRemoteUsers(null);
      setRemoteUserAudio(false);
      setLocalUserName("");
      setRemoteUserName("");
      setViewKey((k) => k + 1);

      // Retry joining with new or existing token
      const success = await joinSessionHandler();
      if (!success) {
        updateCallState({ showRetryButton: true });
      }
    } catch (error) {
      logEvent(
        "ERROR",
        "RETRY_ERROR",
        "=== NURSE === Error during retry attempt",
        error as object
      );
      updateCallState({ showRetryButton: true });
    }
  }, [
    sdkToken,
    onRetryRequest,
    cleanupListeners,
    leaveSession,
    joinSessionHandler,
    updateCallState,
  ]);

  const enableRetryButton = useCallback(() => {
    setTimeout(() => {
      updateCallState({ disableRetry: false });
    }, 8000);
  }, [updateCallState]);

  // Initialize call
  useEffect(() => {
    joinSessionHandler();

    return () => {
      cleanupListeners();
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  // Render loading state
  if (callState.loading || callState.isRecovering) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#ffffff" />
        <Text style={styles.loadingText}>
          {callState.isRecovering
            ? "Recovering connection..."
            : callState.endingCall
              ? "Ending call..."
              : "Joining call..."}
        </Text>
        {callState.showRetryButton && !callState.endingCall && (
          <TouchableOpacity
            style={[
              styles.retryButton,
              callState.disableRetry && styles.disabledButton,
            ]}
            onPress={retryConnection}
            disabled={callState.disableRetry}
          >
            <Text
              style={[
                styles.retryButtonText,
                callState.disableRetry && styles.disabledText,
              ]}
            >
              Retry Connection
            </Text>
          </TouchableOpacity>
        )}
      </View>
    );
  }

  // Render call ended state
  if (callState.callEnded) {
    return (
      <View style={styles.callEndedContainer}>
        <View style={styles.actionGroup}>
          <TouchableOpacity
            style={styles.rejoinButton}
            onPress={retryConnection}
          >
            <PhoneCall size={24} color="#ffffff" />
          </TouchableOpacity>
          <Text style={styles.actionText}>Rejoin Call</Text>
        </View>
        <View style={styles.actionGroup}>
          <TouchableOpacity style={styles.endButton} onPress={onCallOverview}>
            <ArrowRight size={24} color="#ffffff" />
          </TouchableOpacity>
          <Text style={styles.actionText}>End Call</Text>
        </View>
      </View>
    );
  }

  // Render main call interface
  return (
    <View style={styles.container}>
      <View style={styles.topSpacer} />

      <View
        style={[
          styles.videoContainer,
          isLandscape && styles.landscapeVideoContainer,
        ]}
      >
        {/* Remote user video */}
        {remoteUsers ? (
          <View style={[styles.videoView, styles.remoteVideoView]}>
            <ZoomView
              key={`remote-${viewKey}`}
              style={styles.fullVideo}
              userId={remoteUsers.userId}
              sharing={false}
              fullScreen={false}
              videoAspect={VideoAspect.PanAndScan}
              videoResolution={VideoResolution.ResolutionAuto}
            />
            <View style={styles.userInfoOverlay}>
              <Text style={styles.userNameText}>{remoteUserName}</Text>
              {remoteUserAudio ? (
                <Mic size={12} color="green" />
              ) : (
                <MicOff size={12} color="red" />
              )}
            </View>
          </View>
        ) : (
          <View style={[styles.videoView, styles.placeholderView]}>
            <Text style={styles.placeholderText}>
              {callState.providerLeft
                ? "Provider left the call"
                : "Waiting for provider..."}
            </Text>
          </View>
        )}

        {/* Local user video */}
        {localUser && (
          <View style={[styles.videoView, styles.localVideoView]}>
            <ZoomView
              key={`local-${viewKey}`}
              style={styles.fullVideo}
              userId={localUser.userId}
              sharing={false}
              fullScreen={false}
              videoAspect={VideoAspect.PanAndScan}
              videoResolution={VideoResolution.ResolutionAuto}
            />
            <View style={styles.userInfoOverlay}>
              <Text style={styles.userNameText}>{localUserName}</Text>
              {callState.audioOn ? (
                <Mic size={12} color="green" />
              ) : (
                <MicOff size={12} color="red" />
              )}
            </View>
          </View>
        )}
      </View>

      {/* Call controls */}
      {callState.callStarted && (
        <View style={styles.controlsContainer}>
          <TouchableOpacity
            style={[
              styles.controlButton,
              !callState.audioOn && styles.activeControlButton,
            ]}
            onPress={toggleAudio}
          >
            {callState.audioOn ? (
              <Mic size={24} color="#fff" />
            ) : (
              <MicOff size={24} color="#fff" />
            )}
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.controlButton,
              !callState.videoOn && styles.activeControlButton,
            ]}
            onPress={toggleVideo}
          >
            {callState.videoOn ? (
              <Video size={24} color="#fff" />
            ) : (
              <VideoOff size={24} color="#fff" />
            )}
          </TouchableOpacity>

          <TouchableOpacity style={styles.controlButton} onPress={switchCamera}>
            <SwitchCamera size={24} color="#fff" />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.controlButton, styles.endCallButton]}
            onPress={leaveSessionHandler}
          >
            <PhoneCall size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      )}

      {/* Notifications */}
      <Notification
        message={notification.message}
        visible={notification.visible}
        onHide={() => setNotification((prev) => ({ ...prev, visible: false }))}
      />

      <CustomNotification
        message={
          callState.providerLeft
            ? "Provider left the call"
            : "Waiting for provider to join call"
        }
        visible={!callState.isProviderInCall && callState.callStarted}
        onHide={() => {}}
      />

      {/* Retry button for errors */}
      {callState.showRetryButton && (
        <View style={styles.retryContainer}>
          <TouchableOpacity
            style={[
              styles.retryButton,
              callState.disableRetry && styles.disabledButton,
            ]}
            onPress={retryConnection}
            disabled={callState.disableRetry}
          >
            <Text
              style={[
                styles.retryButtonText,
                callState.disableRetry && styles.disabledText,
              ]}
            >
              Retry Connection
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  callEndedContainer: {
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "row",
    flex: 1,
    gap: 20,
  },
  buttonContainer: {
    flexDirection: "row",
    gap: 16,
    alignItems: "center",
    justifyContent: "center",
  },
  callDetailsButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: "#555",
    justifyContent: "center",
    alignItems: "center",
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  callEndedButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    backgroundColor: "#4CAF50",
    alignItems: "center",
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  container: {
    flex: 1,
    backgroundColor: "#0C111D",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#0C111D",
  },
  topSpacer: {
    flex: 0,
  },
  videoContainer: {
    flex: 0.9,
    padding: 10,
  },
  videoWrapper: {
    flex: 1,
    marginVertical: 5,
    borderRadius: 10,
    overflow: "hidden",
    position: "relative",
    backgroundColor: "#1D2939",
  },
  zoomView: {
    width: "100%",
    height: "100%",
  },
  placeholder: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#333",
    borderRadius: 10,
    marginVertical: 5,
  },
  placeholderText: {
    color: "#ffffff",
    fontSize: 16,
    fontWeight: "500",
  },
  controlsContainer: {
    flex: 0.1,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
    backgroundColor: "#1D2939",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    marginInline: 10,
    gap: 20,
  },
  reloadVideoButtonContainer: {
    // padding: 5,
    backgroundColor: "#1D2939",
    // borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
    marginInline: 10,
    flexDirection: "row",
    gap: 10,
  },
  loadingReloadVideoButtonContainer: {
    padding: 5,
    backgroundColor: "#1D2939",
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
    marginInline: 10,
    marginBottom: 20,
    marginTop: 40,
    flexDirection: "row",
    gap: 10,
  },
  reloadVideoButton: {
    // backgroundColor: "#555",
    padding: 6,
    paddingHorizontal: 15,
    borderRadius: 50,
  },
  reloadButtonText: {
    color: "#ffffff",
    fontWeight: "600",
  },
  controlButton: {
    padding: 15,
    backgroundColor: "#555",
    borderRadius: 50,
    justifyContent: "center",
    alignItems: "center",
  },
  retryText: {
    color: "#ffffff",
    fontWeight: "600",
  },
  endCallButton: {
    backgroundColor: "#ff4444",
    height: 50,
    width: 50,
    padding: 10,
  },
  userInfoOverlay: {
    position: "absolute",
    bottom: 8,
    left: 8,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.6)",
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },

  userNameText: {
    color: "#fff",
    fontSize: 12,
    marginRight: 4,
  },
  // container: {
  //   flex: 1,
  //   backgroundColor: "#000",
  // },
  // loadingContainer: {
  //   flex: 1,
  //   backgroundColor: "#000",
  //   justifyContent: "center",
  //   alignItems: "center",
  //   gap: 20,
  // },
  loadingText: {
    color: "#fff",
    fontSize: 16,
    marginTop: 10,
  },
  // topSpacer: {
  //   height: 60,
  // },
  // videoContainer: {
  //   flex: 1,
  //   padding: 10,
  //   gap: 10,
  // },
  landscapeVideoContainer: {
    flexDirection: "row",
  },
  videoView: {
    flex: 1,
    borderRadius: 12,
    overflow: "hidden",
    position: "relative",
    minHeight: 200,
  },
  remoteVideoView: {
    // flex: 2,
  },
  localVideoView: {
    // flex: 1,
    // maxHeight: 200,
  },
  fullVideo: {
    flex: 1,
    backgroundColor: "#333",
  },
  placeholderView: {
    backgroundColor: "#333",
    justifyContent: "center",
    alignItems: "center",
  },
  // placeholderText: {
  //   color: "#fff",
  //   fontSize: 16,
  //   opacity: 0.7,
  // },
  // userInfoOverlay: {
  //   position: "absolute",
  //   bottom: 8,
  //   left: 8,
  //   flexDirection: "row",
  //   alignItems: "center",
  //   backgroundColor: "rgba(0,0,0,0.6)",
  //   borderRadius: 8,
  //   paddingHorizontal: 8,
  //   paddingVertical: 4,
  // },
  // userNameText: {
  //   color: "#fff",
  //   fontSize: 12,
  //   marginRight: 4,
  // },
  // controlsContainer: {
  //   flexDirection: "row",
  //   justifyContent: "center",
  //   alignItems: "center",
  //   paddingVertical: 20,
  //   paddingHorizontal: 20,
  //   gap: 20,
  // },
  // controlButton: {
  //   width: 56,
  //   height: 56,
  //   borderRadius: 28,
  //   backgroundColor: "#555",
  //   justifyContent: "center",
  //   alignItems: "center",
  // },
  activeControlButton: {
    // backgroundColor: "#ff4444",
  },
  // endCallButton: {
  //   backgroundColor: "#ff4444",
  // },
  // callEndedContainer: {
  //   flex: 1,
  //   backgroundColor: "#000",
  //   justifyContent: "center",
  //   alignItems: "center",
  //   flexDirection: "row",
  //   gap: 60,
  // },
  actionGroup: {
    alignItems: "center",
    gap: 10,
  },
  rejoinButton: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: "#28a745",
    justifyContent: "center",
    alignItems: "center",
  },
  endButton: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: "#007bff",
    justifyContent: "center",
    alignItems: "center",
  },
  actionText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  retryContainer: {
    // position: "absolute",
    // bottom: 100,
    // left: 20,
    // right: 20,
    backgroundColor: "#1D2939",
    // padding: 20,
    marginInline: 10,
    alignItems: "center",
  },
  retryButton: {
    // backgroundColor: "#007bff",
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
  },
  retryButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  disabledButton: {
    backgroundColor: "#666",
  },
  disabledText: {
    color: "#aaa",
  },
});

// Provider wrapper component
interface NurseCallWrapperProps extends NurseCallProps {}

const NurseCallWrapper: React.FC<NurseCallWrapperProps> = (props) => {
  const sdkConfig = useMemo(
    () => ({
      domain: "zoom.us",
      enableLog: true,
      enableCallKit: false,
    }),
    []
  );

  return (
    <ZoomVideoSdkProvider config={sdkConfig}>
      <NurseCallComponent {...props} />
    </ZoomVideoSdkProvider>
  );
};

export default NurseCallWrapper;
export { NurseCallComponent };
