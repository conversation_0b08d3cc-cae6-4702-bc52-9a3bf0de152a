import { ReactNode } from "react";
import { NativeModules, Platform, Vibration } from "react-native";
import { Button, Dialog, Text, XStack, YStack } from "tamagui";
import CallKitService from "~/services/CallKitService";
interface DialogBoxProps {
  open: boolean;
  onClose: (open: boolean) => void;
  title: string;
  body: string | ReactNode;
  btnText: string;
  onFinishLater?: () => void;
  showCloseBtn?: boolean;
  generateSummary?: () => void;
}

export function DialogBox({
  open,
  onClose,
  title,
  body,
  btnText,
  onFinishLater,
  showCloseBtn = true,
  generateSummary,
}: DialogBoxProps): JSX.Element {
  const styles = useCallPendingDialogStyle();
  return (
    <Dialog modal open={open} onOpenChange={onClose}>
      <Dialog.Overlay {...styles.overlay} />
      <Dialog.Content {...styles.dialogContent}>
        <YStack {...styles.container}>
          <XStack {...styles.headerContainer}>
            <Text {...styles.headerText}>{title}</Text>
          </XStack>
          <YStack>
            <Text {...styles.infoBlockValue}>{body}</Text>
          </YStack>
          <XStack
            style={{
              display: "flex",
              justifyContent: "space-around",
              marginTop: 20,
              gap: 10,
            }}
          >
            {showCloseBtn && (
              <Button
                {...styles.closeButton}
                onPress={async () => {
                  // Cancel any ongoing vibration
                  Vibration.cancel();
                  console.log("Canceled vibration in Dialog closeButton");

                  // End any active CallKit calls
                  if (Platform.OS === "ios") {
                    try {
                      // Get active calls for debugging
                      const activeCalls = await CallKitService.getActiveCalls();
                      console.log(
                        "Active calls before ending in Dialog closeButton:",
                        activeCalls
                      );

                      // Use CallKitService to end all calls
                      const result = await CallKitService.endAllCalls();
                      console.log(
                        "Ended all active CallKit calls from Dialog closeButton:",
                        result
                      );

                      // If there are still active calls, use the nuclear option
                      setTimeout(async () => {
                        const remainingCalls =
                          await CallKitService.getActiveCalls();
                        console.log(
                          "Remaining calls after ending in Dialog closeButton:",
                          remainingCalls
                        );

                        if (remainingCalls.length > 0) {
                          console.log(
                            "Calls still active, using direct method to end calls"
                          );

                          // Try to directly end calls using the native module
                          if (
                            Platform.OS === "ios" &&
                            NativeModules.RingtoneModule &&
                            typeof NativeModules.RingtoneModule
                              .directEndAllCalls === "function"
                          ) {
                            try {
                              const directResult =
                                await NativeModules.RingtoneModule.directEndAllCalls();
                              console.log(
                                "Direct end all calls result:",
                                directResult
                              );
                            } catch (error) {
                              console.error(
                                "Error using direct method to end calls:",
                                error
                              );
                            }
                          }

                          // Also try the nuclear option
                          const resetResult =
                            await CallKitService.resetCallKitProvider();
                          console.log(
                            "Reset CallKit provider result:",
                            resetResult
                          );
                        }
                      }, 500);
                    } catch (error) {
                      console.error(
                        "Error ending CallKit calls from Dialog closeButton:",
                        error
                      );
                    }
                  }

                  onClose(false);
                }}
              >
                Close
              </Button>
            )}
            <Button
              {...styles.button}
              onPress={async () => {
                // Cancel any ongoing vibration
                Vibration.cancel();
                console.log("Canceled vibration in Dialog actionButton");

                // End any active CallKit calls
                if (Platform.OS === "ios") {
                  try {
                    // Get active calls for debugging
                    const activeCalls = await CallKitService.getActiveCalls();
                    console.log(
                      "Active calls before ending in Dialog actionButton:",
                      activeCalls
                    );

                    // Use CallKitService to end all calls
                    const result = await CallKitService.endAllCalls();
                    console.log(
                      "Ended all active CallKit calls from Dialog actionButton:",
                      result
                    );

                    // If there are still active calls, use the nuclear option
                    setTimeout(async () => {
                      const remainingCalls =
                        await CallKitService.getActiveCalls();
                      console.log(
                        "Remaining calls after ending in Dialog actionButton:",
                        remainingCalls
                      );

                      if (remainingCalls.length > 0) {
                        console.log(
                          "Calls still active, using direct method to end calls"
                        );

                        // Try to directly end calls using the native module
                        if (
                          Platform.OS === "ios" &&
                          NativeModules.RingtoneModule &&
                          typeof NativeModules.RingtoneModule
                            .directEndAllCalls === "function"
                        ) {
                          try {
                            const directResult =
                              await NativeModules.RingtoneModule.directEndAllCalls();
                            console.log(
                              "Direct end all calls result:",
                              directResult
                            );
                          } catch (error) {
                            console.error(
                              "Error using direct method to end calls:",
                              error
                            );
                          }
                        }

                        // Also try the nuclear option
                        const resetResult =
                          await CallKitService.resetCallKitProvider();
                        console.log(
                          "Reset CallKit provider result:",
                          resetResult
                        );
                      }
                    }, 500);
                  } catch (error) {
                    console.error(
                      "Error ending CallKit calls from Dialog actionButton:",
                      error
                    );
                  }
                }

                onClose(false);
                onFinishLater?.();
                generateSummary?.();
              }}
            >
              {btnText}
            </Button>
          </XStack>
        </YStack>
      </Dialog.Content>
    </Dialog>
  );
}

export const useCallPendingDialogStyle = () => {
  return {
    dialogContent: {
      bordered: true,
      bg: "$screenBackgroundcolor" as any,
      br: "$4" as any,
      shadowColor: "transparent" as any,
      shadowOpacity: 0 as any,
      shadowRadius: 0 as any,
      width: "90%" as any,
      alignSelf: "center" as any,
      justifyContent: "center" as any,
      borderRadius: "$7" as any,
      position: "absolute" as any,
      top: "50%" as any,
      left: "50%" as any,
      transform: [{ translateX: "-50%" }, { translateY: "-50%" }] as any,
      borderColor: "$primaryBorderColor" as any,
    },
    container: {
      gap: "$2" as any,
      paddingHorizontal: "$4" as any,
      marginHor: "$4" as any,
    },
    headerContainer: {
      justifyContent: "space-between",
    },
    headerText: {
      fontSize: 24,
      fontWeight: "600" as any,
    },
    callEndsText: {
      fontSize: 16,
      fontWeight: "200" as any,
    },
    overlay: {
      animation: "quick" as any,
      enterStyle: { opacity: 0 },
      exitStyle: { opacity: 0 },
      backgroundColor: "$callAlertBackground" as any,
      opacity: 0.5,
    },
    infoBlockLabel: {
      fontSize: 18,
      fontWeight: "400" as any,
    },
    infoBlockValue: {
      fontSize: 18,
      fontWeight: "300" as any,
      marginBlockStart: 10,
    },
    button: {
      color: "$buttonWhiteColor" as any,
      backgroundColor: "$primaryColor" as any,
      marginBlock: 10,
      fontSize: 18,
      fontWeight: 500 as any,
      flex: 1,
    },
    closeButton: {
      color: "$loginForgotPasswordColor" as any,
      backgroundColor: "$buttonWhiteColor" as any,
      marginBlock: 10,
      fontSize: 18,
      fontWeight: 500 as any,
      flex: 1,
    },
    spinner: {
      marginBlock: 30,
    },
  };
};
