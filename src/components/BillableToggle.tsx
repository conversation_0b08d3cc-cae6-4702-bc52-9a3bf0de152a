import React, { useState } from "react";
import { TouchableOpacity } from "react-native";
import { View, Text, Switch, Label, XStack } from "tamagui";

type BillableToggleProps = {
  isBillable: boolean;
  onToggle: (isBillable: boolean) => void;
  disabled?: boolean;
};

const BillableToggle = ({
  isBillable,
  onToggle,
  disabled = false,
}: BillableToggleProps) => {
  const dashboardStyles = useToggleStyles();
  const { toggleText, toggleStack, switchStyle, switchThumbStyle } =
    dashboardStyles;

  const containerStyle = {
    alignItems: "center" as any,
    justifyContent: "center" as any,
  };

  return (
    <View {...containerStyle}>
      <XStack {...toggleStack}>
        <Switch
          {...switchStyle}
          checked={isBillable}
          disabled={disabled}
          onCheckedChange={() => !disabled && onToggle(!isBillable)}
          backgroundColor={
            isBillable ? "$switchToggleGreen" : "$inactiveToggleColor"
          }
        >
          <Switch.Thumb {...switchThumbStyle} />
        </Switch>
        <Label {...toggleText}>
          {isBillable ? "Billable" : "Non Billable"}
        </Label>
      </XStack>
    </View>
  );
};

export default BillableToggle;

const useToggleStyles = () => {
  return {
    toggleText: {
      fontSize: 14,
      fontWeight: "500" as "500",
      width: "60" as any,
      lineHeight: 20,
      textWrap: "wrap" as any,
    },
    toggleStack: { alignItems: "center" as any, gap: "$2" as any },
    switchStyle: {
      size: "$1" as any,
      borderColor: "$primaryBorderColor" as any,
      borderWidth: 0.5,
    },
    switchThumbStyle: {
      animation: "bouncy" as any,
      backgroundColor: "white" as any,
      size: "$1" as any,
      marginTop: "auto" as any,
      marginBottom: "auto" as any,
    },
  };
};
