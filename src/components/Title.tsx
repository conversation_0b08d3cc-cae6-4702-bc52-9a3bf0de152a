import { XStack, Text } from "tamagui";

interface TitleProps {
  text: string;
  backgroundColor?: string;
  borderColor?: string;
}

const Title = ({
  text,
  backgroundColor = "#ECFDF3",
  borderColor = "#ABEFC6",
}: TitleProps) => {
  const textAlign = {
    textAlign: "center",
  };
  const badgeBackground = {
    backgroundColor,
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 2,
    alignItems: "center",
    justifyContent: "center",
    minWidth: 60,
  };
  return (
    <XStack
      {...badgeBackground}
      borderColor={borderColor as any}
      height={24}
      borderWidth={1}
    >
      <Text fontSize={12} fontWeight="500" color="#000" {...textAlign}>
        {text}
      </Text>
    </XStack>
  );
};

export default Title;
