import React from "react";
import * as Svg from "react-native-svg";

type IHorizontalDashedLine = {
  /** total rendered thickness (px) */
  height?: number;
  /** dash/gap lengths in user units (not px) */
  dashLength?: number;
  dashGap?: number;
  /** stroke color */
  color?: string;
  style?: object;
};

/**
 * Stretches to 100% of parent width.
 * Internally uses a 0–100 coordinate system so x2=100 spans the full width.
 */
export function HorizontalDashedLine({
  height = 1,
  dashLength = 2,
  dashGap = 2,
  color = "#D2D6DB",
  style = {},
}: IHorizontalDashedLine) {
  return (
    <Svg.Svg
      height={height}
      width="100%"
      viewBox="0 0 100 1"
      preserveAspectRatio="none"
      style={{ ...style, alignSelf: "stretch" }}
    >
      <Svg.Line
        x1="0"
        y1="0.5"
        x2="100"
        y2="0.5"
        stroke={color}
        strokeWidth={height}
        strokeDasharray={`${dashLength},${dashGap}`}
      />
    </Svg.Svg>
  );
}
