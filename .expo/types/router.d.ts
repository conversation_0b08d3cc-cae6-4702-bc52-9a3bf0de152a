/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/admin/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/common/customBadge`; params?: Router.UnknownInputParams; } | { pathname: `/common/header`; params?: Router.UnknownInputParams; } | { pathname: `/common/login`; params?: Router.UnknownInputParams; } | { pathname: `/common/userAlerts`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/call-old`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/call`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/calloverview`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/chat`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/consultations`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/messages`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/new-visit`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/requestVisit`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/settings`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/telehealthconsent`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/transscript`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/verify-pin`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/Styles/DashboardStyle`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/Styles/MessagesStyle`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/Styles/RequestNewVisitStyle`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/Styles/RequestVisitStyles`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/Styles/TelehealthConsentStyle`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/Styles/TranscriptStyle`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/patient-consent/styles/style`; params?: Router.UnknownInputParams; } | { pathname: `/provider/BillingCodes`; params?: Router.UnknownInputParams; } | { pathname: `/provider/CallContainer`; params?: Router.UnknownInputParams; } | { pathname: `/provider/CallDetails`; params?: Router.UnknownInputParams; } | { pathname: `/provider/ConsultationTabs`; params?: Router.UnknownInputParams; } | { pathname: `/provider/ICDCodes`; params?: Router.UnknownInputParams; } | { pathname: `/provider/ProblemListDropdown`; params?: Router.UnknownInputParams; } | { pathname: `/provider/ProviderConsultations`; params?: Router.UnknownInputParams; } | { pathname: `/provider/Visit`; params?: Router.UnknownInputParams; } | { pathname: `/provider/billing`; params?: Router.UnknownInputParams; } | { pathname: `/provider/call-old`; params?: Router.UnknownInputParams; } | { pathname: `/provider/call`; params?: Router.UnknownInputParams; } | { pathname: `/provider/chat`; params?: Router.UnknownInputParams; } | { pathname: `/provider/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/provider/messages`; params?: Router.UnknownInputParams; } | { pathname: `/provider/profile`; params?: Router.UnknownInputParams; } | { pathname: `/provider/reviewcall`; params?: Router.UnknownInputParams; } | { pathname: `/provider/settings`; params?: Router.UnknownInputParams; } | { pathname: `/provider/soap`; params?: Router.UnknownInputParams; } | { pathname: `/provider/Styles/BillingStyles`; params?: Router.UnknownInputParams; } | { pathname: `/provider/Styles/CallDetailStyle`; params?: Router.UnknownInputParams; } | { pathname: `/provider/Styles/DashboardStyle`; params?: Router.UnknownInputParams; } | { pathname: `/provider/Styles/MessageStyle`; params?: Router.UnknownInputParams; } | { pathname: `/provider/Styles/ProblemListDropdownStyles`; params?: Router.UnknownInputParams; } | { pathname: `/provider/Styles/ProfileStyles`; params?: Router.UnknownInputParams; } | { pathname: `/provider/Styles/ReviewCallStyle`; params?: Router.UnknownInputParams; } | { pathname: `/provider/Styles/SOAPStyles`; params?: Router.UnknownInputParams; } | { pathname: `/provider/Styles/VisitStyles`; params?: Router.UnknownInputParams; } | { pathname: `/styles/HeaderStyle`; params?: Router.UnknownInputParams; } | { pathname: `/styles/LayoutStyle`; params?: Router.UnknownInputParams; } | { pathname: `/styles/LoginStyle`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/patient-consent/[index]`, params: Router.UnknownInputParams & { index: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `/admin/dashboard`; params?: Router.UnknownOutputParams; } | { pathname: `/common/customBadge`; params?: Router.UnknownOutputParams; } | { pathname: `/common/header`; params?: Router.UnknownOutputParams; } | { pathname: `/common/login`; params?: Router.UnknownOutputParams; } | { pathname: `/common/userAlerts`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/call-old`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/call`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/calloverview`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/chat`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/consultations`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/dashboard`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/messages`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/new-visit`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/requestVisit`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/settings`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/telehealthconsent`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/transscript`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/verify-pin`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/Styles/DashboardStyle`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/Styles/MessagesStyle`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/Styles/RequestNewVisitStyle`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/Styles/RequestVisitStyles`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/Styles/TelehealthConsentStyle`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/Styles/TranscriptStyle`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/patient-consent/styles/style`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/BillingCodes`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/CallContainer`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/CallDetails`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/ConsultationTabs`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/ICDCodes`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/ProblemListDropdown`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/ProviderConsultations`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/Visit`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/billing`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/call-old`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/call`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/chat`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/dashboard`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/messages`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/profile`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/reviewcall`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/settings`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/soap`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/Styles/BillingStyles`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/Styles/CallDetailStyle`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/Styles/DashboardStyle`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/Styles/MessageStyle`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/Styles/ProblemListDropdownStyles`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/Styles/ProfileStyles`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/Styles/ReviewCallStyle`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/Styles/SOAPStyles`; params?: Router.UnknownOutputParams; } | { pathname: `/provider/Styles/VisitStyles`; params?: Router.UnknownOutputParams; } | { pathname: `/styles/HeaderStyle`; params?: Router.UnknownOutputParams; } | { pathname: `/styles/LayoutStyle`; params?: Router.UnknownOutputParams; } | { pathname: `/styles/LoginStyle`; params?: Router.UnknownOutputParams; } | { pathname: `/nurse/patient-consent/[index]`, params: Router.UnknownOutputParams & { index: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `/admin/dashboard${`?${string}` | `#${string}` | ''}` | `/common/customBadge${`?${string}` | `#${string}` | ''}` | `/common/header${`?${string}` | `#${string}` | ''}` | `/common/login${`?${string}` | `#${string}` | ''}` | `/common/userAlerts${`?${string}` | `#${string}` | ''}` | `/nurse/call-old${`?${string}` | `#${string}` | ''}` | `/nurse/call${`?${string}` | `#${string}` | ''}` | `/nurse/calloverview${`?${string}` | `#${string}` | ''}` | `/nurse/chat${`?${string}` | `#${string}` | ''}` | `/nurse/consultations${`?${string}` | `#${string}` | ''}` | `/nurse/dashboard${`?${string}` | `#${string}` | ''}` | `/nurse/messages${`?${string}` | `#${string}` | ''}` | `/nurse/new-visit${`?${string}` | `#${string}` | ''}` | `/nurse/requestVisit${`?${string}` | `#${string}` | ''}` | `/nurse/settings${`?${string}` | `#${string}` | ''}` | `/nurse/telehealthconsent${`?${string}` | `#${string}` | ''}` | `/nurse/transscript${`?${string}` | `#${string}` | ''}` | `/nurse/verify-pin${`?${string}` | `#${string}` | ''}` | `/nurse/Styles/DashboardStyle${`?${string}` | `#${string}` | ''}` | `/nurse/Styles/MessagesStyle${`?${string}` | `#${string}` | ''}` | `/nurse/Styles/RequestNewVisitStyle${`?${string}` | `#${string}` | ''}` | `/nurse/Styles/RequestVisitStyles${`?${string}` | `#${string}` | ''}` | `/nurse/Styles/TelehealthConsentStyle${`?${string}` | `#${string}` | ''}` | `/nurse/Styles/TranscriptStyle${`?${string}` | `#${string}` | ''}` | `/nurse/patient-consent/styles/style${`?${string}` | `#${string}` | ''}` | `/provider/BillingCodes${`?${string}` | `#${string}` | ''}` | `/provider/CallContainer${`?${string}` | `#${string}` | ''}` | `/provider/CallDetails${`?${string}` | `#${string}` | ''}` | `/provider/ConsultationTabs${`?${string}` | `#${string}` | ''}` | `/provider/ICDCodes${`?${string}` | `#${string}` | ''}` | `/provider/ProblemListDropdown${`?${string}` | `#${string}` | ''}` | `/provider/ProviderConsultations${`?${string}` | `#${string}` | ''}` | `/provider/Visit${`?${string}` | `#${string}` | ''}` | `/provider/billing${`?${string}` | `#${string}` | ''}` | `/provider/call-old${`?${string}` | `#${string}` | ''}` | `/provider/call${`?${string}` | `#${string}` | ''}` | `/provider/chat${`?${string}` | `#${string}` | ''}` | `/provider/dashboard${`?${string}` | `#${string}` | ''}` | `/provider/messages${`?${string}` | `#${string}` | ''}` | `/provider/profile${`?${string}` | `#${string}` | ''}` | `/provider/reviewcall${`?${string}` | `#${string}` | ''}` | `/provider/settings${`?${string}` | `#${string}` | ''}` | `/provider/soap${`?${string}` | `#${string}` | ''}` | `/provider/Styles/BillingStyles${`?${string}` | `#${string}` | ''}` | `/provider/Styles/CallDetailStyle${`?${string}` | `#${string}` | ''}` | `/provider/Styles/DashboardStyle${`?${string}` | `#${string}` | ''}` | `/provider/Styles/MessageStyle${`?${string}` | `#${string}` | ''}` | `/provider/Styles/ProblemListDropdownStyles${`?${string}` | `#${string}` | ''}` | `/provider/Styles/ProfileStyles${`?${string}` | `#${string}` | ''}` | `/provider/Styles/ReviewCallStyle${`?${string}` | `#${string}` | ''}` | `/provider/Styles/SOAPStyles${`?${string}` | `#${string}` | ''}` | `/provider/Styles/VisitStyles${`?${string}` | `#${string}` | ''}` | `/styles/HeaderStyle${`?${string}` | `#${string}` | ''}` | `/styles/LayoutStyle${`?${string}` | `#${string}` | ''}` | `/styles/LoginStyle${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/admin/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/common/customBadge`; params?: Router.UnknownInputParams; } | { pathname: `/common/header`; params?: Router.UnknownInputParams; } | { pathname: `/common/login`; params?: Router.UnknownInputParams; } | { pathname: `/common/userAlerts`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/call-old`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/call`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/calloverview`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/chat`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/consultations`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/messages`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/new-visit`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/requestVisit`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/settings`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/telehealthconsent`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/transscript`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/verify-pin`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/Styles/DashboardStyle`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/Styles/MessagesStyle`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/Styles/RequestNewVisitStyle`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/Styles/RequestVisitStyles`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/Styles/TelehealthConsentStyle`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/Styles/TranscriptStyle`; params?: Router.UnknownInputParams; } | { pathname: `/nurse/patient-consent/styles/style`; params?: Router.UnknownInputParams; } | { pathname: `/provider/BillingCodes`; params?: Router.UnknownInputParams; } | { pathname: `/provider/CallContainer`; params?: Router.UnknownInputParams; } | { pathname: `/provider/CallDetails`; params?: Router.UnknownInputParams; } | { pathname: `/provider/ConsultationTabs`; params?: Router.UnknownInputParams; } | { pathname: `/provider/ICDCodes`; params?: Router.UnknownInputParams; } | { pathname: `/provider/ProblemListDropdown`; params?: Router.UnknownInputParams; } | { pathname: `/provider/ProviderConsultations`; params?: Router.UnknownInputParams; } | { pathname: `/provider/Visit`; params?: Router.UnknownInputParams; } | { pathname: `/provider/billing`; params?: Router.UnknownInputParams; } | { pathname: `/provider/call-old`; params?: Router.UnknownInputParams; } | { pathname: `/provider/call`; params?: Router.UnknownInputParams; } | { pathname: `/provider/chat`; params?: Router.UnknownInputParams; } | { pathname: `/provider/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/provider/messages`; params?: Router.UnknownInputParams; } | { pathname: `/provider/profile`; params?: Router.UnknownInputParams; } | { pathname: `/provider/reviewcall`; params?: Router.UnknownInputParams; } | { pathname: `/provider/settings`; params?: Router.UnknownInputParams; } | { pathname: `/provider/soap`; params?: Router.UnknownInputParams; } | { pathname: `/provider/Styles/BillingStyles`; params?: Router.UnknownInputParams; } | { pathname: `/provider/Styles/CallDetailStyle`; params?: Router.UnknownInputParams; } | { pathname: `/provider/Styles/DashboardStyle`; params?: Router.UnknownInputParams; } | { pathname: `/provider/Styles/MessageStyle`; params?: Router.UnknownInputParams; } | { pathname: `/provider/Styles/ProblemListDropdownStyles`; params?: Router.UnknownInputParams; } | { pathname: `/provider/Styles/ProfileStyles`; params?: Router.UnknownInputParams; } | { pathname: `/provider/Styles/ReviewCallStyle`; params?: Router.UnknownInputParams; } | { pathname: `/provider/Styles/SOAPStyles`; params?: Router.UnknownInputParams; } | { pathname: `/provider/Styles/VisitStyles`; params?: Router.UnknownInputParams; } | { pathname: `/styles/HeaderStyle`; params?: Router.UnknownInputParams; } | { pathname: `/styles/LayoutStyle`; params?: Router.UnknownInputParams; } | { pathname: `/styles/LoginStyle`; params?: Router.UnknownInputParams; } | `/nurse/patient-consent/${Router.SingleRoutePart<T>}` | { pathname: `/nurse/patient-consent/[index]`, params: Router.UnknownInputParams & { index: string | number; } };
    }
  }
}
