// https://docs.expo.dev/guides/using-eslint/
module.exports = {
  extends: "expo",
  ignorePatterns: ["/dist/*", "ios/*", "android/*"],
  settings: {
    // Disable the problematic import resolver
    "import/resolver": {
      node: {
        extensions: [".js", ".jsx", ".ts", ".tsx"]
      }
    }
  },
  rules: {
    // Disable the rule that's causing the error
    "import/namespace": "off"
  }
};
