import "dotenv/config";

export default ({ config }) => ({
  ...config,
  name: "VitalCare",
  slug: "VITALCARE",
  version: "1.0.14",
  orientation: "portrait",
  icon: "./src/assets/images/icon.png",
  scheme: "myapp",
  userInterfaceStyle: "automatic",
  newArchEnabled: true,
  ios: {
    ...config.ios,
    googleServicesFile: "./GoogleService-Info.plist",
    entitlements: {
      // Let EAS set this for you automatically in production builds,
      "aps-environment":
        process.env.EAS_BUILD_PROFILE === "production"
          ? "production"
          : "development",

      // **Required** on iOS 13+ to actually receive PushKit VoIP pushes
      "com.apple.developer.pushkit.unrestricted-voip": true,
      // Temporarily commented out until Apple approves this entitlement
      // "com.apple.developer.usernotifications.critical-alerts": true,
    },
    buildNumber: "1.0.14",
    supportsTablet: true,
    bundleIdentifier: "com.austinr47.VITALCARE",
    infoPlist: {
      NSCameraUsageDescription:
        "VitalCare needs camera access so you can take photos of wounds, injuries, or lab results to share securely with your care team.",
      NSMicrophoneUsageDescription:
        "VitalCare needs microphone access so you can speak with your clinician during telehealth calls.",
      NSCriticalAlertsUsageDescription:
        "VitalCare needs to play emergency sounds even when the device is muted.",
      UIBackgroundModes: ["voip", "audio", "remote-notification"],
      NSPhotoLibraryUsageDescription:
        "VitalCare needs access to your photo library so you can select a profile picture and upload images for visits.",
      NSPhotoLibraryAddUsageDescription:
        "We need permission to save photos you take in VitalCare.",
    },
  },
  android: {
    ...config.android,
    googleServicesFile: "./google-services.json",
    package: "com.austinr47.VITALCARE",
    adaptiveIcon: {
      foregroundImage: "./src/assets/images/adaptive-icon.png",
      backgroundColor: "#ffffff",
    },
    permissions: ["CAMERA", "RECORD_AUDIO", "POST_NOTIFICATIONS"],
  },
  web: {
    ...config.web,
    bundler: "metro",
    output: "static",
    favicon: "./src/assets/images/favicon.png",
  },
  plugins: [
    "expo-router",
    [
      "expo-splash-screen",
      {
        image: "./src/assets/images/splash-icon.png",
        imageWidth: 200,
        resizeMode: "contain",
        backgroundColor: "#0279EB",
      },
    ],
    "@react-native-firebase/app",
    "@react-native-firebase/messaging",
    [
      "expo-build-properties",
      {
        ios: {
          entitlements: {
            "aps-environment":
              process.env.EAS_BUILD_PROFILE === "production"
                ? "production"
                : "development",
            "com.apple.developer.pushkit.unrestricted-voip": true,
            "com.apple.developer.usernotifications.critical-alerts": true,
          },
        },
      },
    ],
    "react-native-document-scanner-plugin",
    "@config-plugins/react-native-callkeep",
  ],
  experiments: {
    typedRoutes: true,
  },
  runtimeVersion: "1.0.14",
  updates: {
    url: "https://u.expo.dev/bb33e355-99c6-43e8-9672-4d943d4258cd",
    enabled: true,
    checkAutomatically: "ON_LOAD",
    fallbackToCacheTimeout: 0,
  },
  extra: {
    apiUrl: process.env.API_URL || "http://localhost:3008",
    PCC_CLIENT_ID: process.env.PCC_CLIENT_ID || "default-client-id",
    REDIRECT_URI:
      process.env.REDIRECT_URI ||
      "https://www.staging.vitalcare.org/api/v1/pcc/auth/callback",
    PCC_AUTH_URL:
      process.env.PCC_AUTH_URL ||
      "https://connect.pointclickcare.com/auth/login",
    eas: {
      projectId: "bb33e355-99c6-43e8-9672-4d943d4258cd",
    },
    EAS_BUILD_PROFILE: process.env.EAS_BUILD_PROFILE || "development",
  },
});
