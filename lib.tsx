import { useEffect } from "react";
import {
  ColorValue,
  PermissionsAndroid,
  Platform,
  Text,
  TouchableOpacity,
  Alert,
} from "react-native";
import { EventType, ZoomVideoSdkUserType } from "@zoom/react-native-videosdk";
import { ZoomVideoSdkContext } from "@zoom/react-native-videosdk/lib/typescript/Context";
import { EmitterSubscription } from "react-native";
import { StyleSheet } from "react-native";
import { PERMISSIONS, requestMultiple } from "react-native-permissions";
import { check, openSettings, RESULTS } from "react-native-permissions";

import { request, Permission } from "react-native-permissions";

const requestWithPrompt = async (perm: Permission, label: string) => {
  const status = await check(perm);

  if (status === RESULTS.GRANTED) return;

  if (status === RESULTS.BLOCKED) {
    Alert.alert(
      `${label} Permission Needed`,
      `Please enable ${label.toLowerCase()} access in settings.`,
      [
        {
          text: "Open Settings",
          onPress: () =>
            openSettings().catch(() => console.warn("Failed to open settings")),
        },
      ]
    );
    return;
  }

  // If status is not blocked, prompt again
  const result = await request(perm);
  if (result !== RESULTS.GRANTED) {
    Alert.alert(
      `${label} Permission Denied`,
      `Without ${label.toLowerCase()} access, this feature won't work properly.`,
      [{ text: "OK" }]
    );
  }
};

export const requestAndUpdatePermissions = async () => {
  if (Platform.OS === "ios") {
    await requestWithPrompt(PERMISSIONS.IOS.CAMERA, "Camera");
    await requestWithPrompt(PERMISSIONS.IOS.MICROPHONE, "Microphone");
  } else {
    await requestWithPrompt(PERMISSIONS.ANDROID.CAMERA, "Camera");
    await requestWithPrompt(PERMISSIONS.ANDROID.RECORD_AUDIO, "Microphone");
  }
};

export default function Button(props: {
  onPress: () => void;
  title: string;
  color?: ColorValue;
}) {
  const { onPress, title, color = "#0e71eb" } = props;
  return (
    <TouchableOpacity
      style={{ ...styles.button, backgroundColor: color }}
      onPress={onPress}
    >
      <Text style={styles.text}>{title}</Text>
    </TouchableOpacity>
  );
}

declare module "@zoom/react-native-videosdk" {
  export function useZoom(): Omit<ZoomVideoSdkContext, "addListener"> &
    CustomEvents;
  export function getRemoteUsers(): Promise<ZoomVideoSdkUserType[]>;
  export interface CustomEvents {
    addListener(
      event: EventType.onSessionJoin,
      handler: (event: { mySelf: userFromEvent }) => void
    ): EmitterSubscription;
    addListener(
      event: EventType.onUserJoin,
      handler: (event: {
        joinedUsers: userFromEvent[];
        remoteUsers: userFromEvent[];
      }) => void
    ): EmitterSubscription;
    addListener(
      event: EventType.onUserLeave,
      handler: (event: {
        leftUsers: userFromEvent[];
        remoteUsers: userFromEvent[];
      }) => void
    ): EmitterSubscription;
    addListener(
      event: EventType.onUserAudioStatusChanged,
      handler: (event: { changedUsers: userFromEvent[] }) => void
    ): EmitterSubscription;
    addListener(
      event: EventType.onUserVideoStatusChanged,
      handler: (event: { changedUsers: userFromEvent[] }) => void
    ): EmitterSubscription;
    addListener(
      event: EventType,
      handler: (data?: any) => void
    ): EmitterSubscription;
  }
}

type userFromEvent = ZoomVideoSdkUserType; // this type isn't correct i think, missing methods
// type userFromEvent = {
//   customUserId: string;
//   isHost: boolean;
//   isManager: boolean;
//   userId: string;
//   userName: string;
// };

const styles = StyleSheet.create({
  safe: {
    width: "90%",
    alignSelf: "center",
    margin: 16,
    flex: 1,
    justifyContent: "center",
  },
  container: {
    width: "100%",
    alignSelf: "center",
    height: "100%",
    flex: 1,
    justifyContent: "center",
  },
  spacer: {
    height: 16,
    width: 8,
  },
  heading: {
    fontSize: 24,
    fontWeight: "bold",
    textAlign: "center",
  },
  button: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 4,
    elevation: 3,
  },
  buttonHolder: {
    flexDirection: "row",
    justifyContent: "center",
    margin: 8,
  },
  text: {
    fontSize: 16,
    lineHeight: 21,
    fontWeight: "bold",
    letterSpacing: 0.25,
    color: "white",
  },
});
