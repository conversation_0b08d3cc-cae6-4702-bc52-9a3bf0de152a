## [Getting Started](#getting-started)

> **Note**: Make sure you have completed the
> https://reactnative.dev/docs/set-up-your-environment guide before proceeding.

1.  _Clone the repo:_
    `git clone https://github.com/VitalCareTech/vitalcare-nativeapp.git cd vitalcare-nativeapp`
2.  _Install dependencies:_ `npm i`
3.  _Run Application:_ Using npm: `npx expo start`

- _For Android:_

  - Using npm: `a`

- _For iOS:_
  - Using npm: `i`

4.  _Access the app:_

- Once running, the app should be accessible on the emulator or a connected
  device

## [Adding New Screens and Functions](#api-documentation)

#### [New Screens](#routes)

- _Location:_ Create your screen file in `/src/app/nurse.` or
  `/src/app/provider`.
- _Structure:_

  - Use React Native functional components with hooks.
  - Example:

    ` import React from 'react'; import { View, Text } from 'react-native';

    const ExampleScreen = () => { return ( <View> <Text>Hello from Example
    Screen!</Text> </View> ); };

    export default ExampleScreen;

    `

#### [New Functions](#functions)

- _Location:_ Place helper functions in `/src/utils/.`.
- _Usage:_ Export your function and import it wherever needed.

## [Code Standards & Best Practices](#standards)

- _Indentation:_ 2 spaces per level.
- _Line Length:_ Keep lines under 80 characters.
- _Whitespace:_ Remove trailing spaces; add a newline at file end.
- _Comments:_
  - Explain complex logic only.
  - Keep them minimal.
- _Functions:_ Use arrow functions (`() => {}`) when possible.
- _Naming:_
  - Variables/functions: `camelCase`
  - Components/Types: `PascalCase`
  - Constants: `UPPERCASE`
- _Imports:_
  - Always at the top.
  - Sorted alphabetically.
- _Language:_ Use TypeScript with `"strict": true` in `tsconfig.json`.
- _Frameworks:_
  - Use functional components/hooks for React.
  - Use async/await in Node.js for async tasks.

For more details, check out the https://standardjs.com/.

## [Scripts Overview](#scripts)

- _Start/Stop:_

  - `npx espo start` – Start the expo server
  - `i` – Run iOS app
  - `j` – Open debugger
  - `a` - Run android app

- _Formatting and Linting:_

  - `npm run format` – Auto-format code with Prettier
  - `npm run lint` - Check for linting issues

## [Testing](#testing)

_Testing Best Practices:_

- Place test files next to the files they test and name them <filename>.test.ts.
- Use Jest for unit and integration tests.
- Use React Native Testing Library for UI tests.
- Ensure all tests properly clean up resources, including unmounting components.

## [Quick Troubleshooting](#troubleshoot)

- _App Not Starting? :_ Ensure Android SDK is installed and device is running.
- Ensure dependencies are installed
- _Android Emulator Not Working? :_ Ensure Android SDK is installed and device
  is running.
- _iOS Build Failing? :_ Ensure you have installed CocoaPods (npx pod-install).
- _TypeScript errors?_ Ensure `"strict": true` is maintained and types are
  defined.

### [File Structure](#file-structure)

```/vitalcare-native/
├── _test_/
├── .expo/
├── node_modules/
├── src/
│   ├── app/
│   │   ├── common/
│   │   ├── nurse/
│   │   ├── provider/
│   │   ├── _layout.tsx
│   │   ├── index.tsx
│   ├── assets/
│   ├── components/
│   ├── context/
│   ├── models/
│   ├── services/
│   ├── store/
│   ├── utils/
├── .gitignore
├── .prettierrc
├── app.json
├── expo-end.d.ts
├── package.json
├── package-lock.json
├── README.md
├── tamagui.config.ts
├── tsconfig.json

```
