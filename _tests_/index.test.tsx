import React from "react";
import renderer from "react-test-renderer";
import { Text } from "react-native";
import Index from "@/index";

describe("Index Component", () => {
  it('should render CustomText with "Welcome!" message', () => {
    const tree = renderer.create(<Index />);
    const customText = tree.root.findByType(Text);
    console.log(customText.props.children);
    expect(customText.props.children).toBe("Vital Care!");
  });
});
