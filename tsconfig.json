{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    "strict": true,
    "baseUrl": ".", // Ensure that the base URL is set correctly
    "paths": {
      "@/*": [
        "src/app/*" // Pointing the alias to the 'app' folder
      ],
      "~/services/*": ["src/services/*"],
      "~/context/*": ["src/context/*"]
    }
  },
  "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"]
}
