import ExpoModulesCore
import AVFoundation

public class RingtoneModule: Module {
  // Define a variable to hold the audio player
  private var audioPlayer: AVAudioPlayer?
  private var ringtoneTimer: Timer?
  
  // Define the module name that JavaScript will use to access it
  public func definition() -> ModuleDefinition {
    Name("RingtoneModule")
    
    // Define a method to play the ringtone
    AsyncFunction("playRingtone") { (promise: Promise) in
      DispatchQueue.main.async {
        do {
          // Stop any existing audio
          self.stopRingtone()
          
          // Configure audio session
          let session = AVAudioSession.sharedInstance()
          try session.setCategory(.playback, mode: .default)
          try session.setActive(true)
          
          // Get path to ringtone file
          guard let soundPath = Bundle.main.path(forResource: "ringtone", ofType: "mp3") else {
            promise.reject(NSError(domain: "RingtoneModule", code: 100, userInfo: [NSLocalizedDescriptionKey: "Ringtone file not found"]))
            return
          }
          
          let soundURL = URL(fileURLWithPath: soundPath)
          
          // Create audio player
          self.audioPlayer = try AVAudioPlayer(contentsOf: soundURL)
          
          // Configure audio player
          self.audioPlayer?.numberOfLoops = -1 // Loop indefinitely
          self.audioPlayer?.volume = 1.0
          
          // Play sound
          guard let player = self.audioPlayer, player.play() else {
            promise.reject(NSError(domain: "RingtoneModule", code: 101, userInfo: [NSLocalizedDescriptionKey: "Failed to play ringtone"]))
            return
          }
          
          // Set timer to stop after 30 seconds
          self.ringtoneTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: false) { [weak self] _ in
            self?.stopRingtone()
          }
          
          promise.resolve(true)
        } catch {
          promise.reject(error)
        }
      }
    }
    
    // Define a method to stop the ringtone
    AsyncFunction("stopRingtone") { (promise: Promise) in
      DispatchQueue.main.async {
        self.stopRingtone()
        promise.resolve(true)
      }
    }
  }
  
  // Helper method to stop the ringtone
  private func stopRingtone() {
    // Stop and release audio player
    if let player = audioPlayer {
      player.stop()
      audioPlayer = nil
    }
    
    // Invalidate timer
    ringtoneTimer?.invalidate()
    ringtoneTimer = nil
    
    // Deactivate audio session
    do {
      try AVAudioSession.sharedInstance().setActive(false)
    } catch {
      print("Error deactivating audio session: \(error)")
    }
  }
}
