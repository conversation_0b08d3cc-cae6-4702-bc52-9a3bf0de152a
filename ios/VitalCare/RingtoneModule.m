#import "RingtoneModule.h"
#import "AppDelegate.h"
#import <AVFoundation/AVFoundation.h>

@implementation RingtoneModule

RCT_EXPORT_MODULE();

// Play ringtone for 30 seconds
RCT_EXPORT_METHOD(playRingtone:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  dispatch_async(dispatch_get_main_queue(), ^{
    AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
    if ([appDelegate respondsToSelector:@selector(playRingtone)]) {
      [appDelegate playRingtone];
      resolve(@YES);
    } else {
      NSError *error = [NSError errorWithDomain:@"RingtoneModule" code:100 userInfo:@{NSLocalizedDescriptionKey: @"Method not found"}];
      reject(@"play_error", @"Failed to play ringtone", error);
    }
  });
}

// Stop ringtone
RCT_EXPORT_METHOD(stopRingtone:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  dispatch_async(dispatch_get_main_queue(), ^{
    AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
    if ([appDelegate respondsToSelector:@selector(stopRingtone)]) {
      [appDelegate stopRingtone];
      resolve(@YES);
    } else {
      NSError *error = [NSError errorWithDomain:@"RingtoneModule" code:101 userInfo:@{NSLocalizedDescriptionKey: @"Method not found"}];
      reject(@"stop_error", @"Failed to stop ringtone", error);
    }
  });
}

// End current CallKit call
RCT_EXPORT_METHOD(endCurrentCallKitCall:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  dispatch_async(dispatch_get_main_queue(), ^{
    // Disable Zoom SDK CallKit integration
    [[NSUserDefaults standardUserDefaults] setBool:NO forKey:@"ZoomVideoSDKCallKitEnabled"];
    [[NSUserDefaults standardUserDefaults] synchronize];

    // Try multiple approaches to end calls

    // 1. Use AppDelegate's method if available
    AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
    if ([appDelegate respondsToSelector:@selector(endAllActiveCalls)]) {
      NSLog(@"Using AppDelegate's endAllActiveCalls method");
      [appDelegate endAllActiveCalls];
    }

    // 2. Also use direct CXCallController approach as a backup
    // Get all active calls from CallKit
    CXCallObserver *callObserver = [[CXCallObserver alloc] init];
    NSArray<CXCall *> *calls = callObserver.calls;

    if (calls.count == 0) {
      // No active calls
      NSLog(@"No active calls to end with CXCallController");
      resolve(@YES);
      return;
    }

    NSLog(@"Ending %lu calls with CXCallController", (unsigned long)calls.count);

    // Create a call controller to end calls
    CXCallController *callController = [[CXCallController alloc] init];

    // Create a transaction to end all calls
    CXTransaction *transaction = [[CXTransaction alloc] init];

    for (CXCall *call in calls) {
      NSLog(@"Adding end action for call UUID: %@", call.UUID);
      CXEndCallAction *endCallAction = [[CXEndCallAction alloc] initWithCallUUID:call.UUID];
      [transaction addAction:endCallAction];
    }

    // Execute the transaction
    [callController requestTransaction:transaction completion:^(NSError * _Nullable error) {
      if (error) {
        NSLog(@"ERROR: Failed to end CallKit calls: %@", error);
        reject(@"end_call_error", @"Failed to end CallKit calls", error);
      } else {
        NSLog(@"Successfully ended all CallKit calls");
        resolve(@YES);
      }
    }];
  });
}

// End call with specific UUID
RCT_EXPORT_METHOD(endCallWithUUID:(NSString *)callUUID
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  dispatch_async(dispatch_get_main_queue(), ^{
    // Disable Zoom SDK CallKit integration
    [[NSUserDefaults standardUserDefaults] setBool:NO forKey:@"ZoomVideoSDKCallKitEnabled"];
    [[NSUserDefaults standardUserDefaults] synchronize];

    if (!callUUID || [callUUID length] == 0) {
      NSError *error = [NSError errorWithDomain:@"RingtoneModule" code:102 userInfo:@{NSLocalizedDescriptionKey: @"Invalid UUID"}];
      reject(@"invalid_uuid", @"Invalid call UUID provided", error);
      return;
    }

    // Create a call controller to end the call
    CXCallController *callController = [[CXCallController alloc] init];

    // Create a transaction to end the call
    CXTransaction *transaction = [[CXTransaction alloc] init];

    // Convert string UUID to NSUUID
    NSUUID *uuid = [[NSUUID alloc] initWithUUIDString:callUUID];
    if (!uuid) {
      NSError *error = [NSError errorWithDomain:@"RingtoneModule" code:103 userInfo:@{NSLocalizedDescriptionKey: @"Invalid UUID format"}];
      reject(@"invalid_uuid_format", @"Invalid call UUID format", error);
      return;
    }

    // Create end call action
    CXEndCallAction *endCallAction = [[CXEndCallAction alloc] initWithCallUUID:uuid];
    [transaction addAction:endCallAction];

    // Execute the transaction
    [callController requestTransaction:transaction completion:^(NSError * _Nullable error) {
      if (error) {
        NSLog(@"ERROR: Failed to end call with UUID %@: %@", callUUID, error);
        reject(@"end_call_error", [NSString stringWithFormat:@"Failed to end call with UUID %@", callUUID], error);
      } else {
        NSLog(@"Successfully ended call with UUID %@", callUUID);
        resolve(@YES);
      }
    }];
  });
}

// Force end all calls using direct CXCallController
RCT_EXPORT_METHOD(forceEndAllCalls:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  dispatch_async(dispatch_get_main_queue(), ^{
    // Disable Zoom SDK CallKit integration
    [[NSUserDefaults standardUserDefaults] setBool:NO forKey:@"ZoomVideoSDKCallKitEnabled"];
    [[NSUserDefaults standardUserDefaults] synchronize];

    // Try multiple approaches to end calls

    // 1. Use AppDelegate's method if available
    AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
    if ([appDelegate respondsToSelector:@selector(endAllActiveCalls)]) {
      NSLog(@"Using AppDelegate's endAllActiveCalls method");
      [appDelegate endAllActiveCalls];
    }

    // 2. Also use direct CXCallController approach as a backup
    // Get all active calls from CallKit
    CXCallObserver *callObserver = [[CXCallObserver alloc] init];
    NSArray<CXCall *> *calls = callObserver.calls;

    if (calls.count == 0) {
      // No active calls
      NSLog(@"No active calls to force end with CXCallController");
      resolve(@YES);
      return;
    }

    NSLog(@"Force ending %lu calls with CXCallController", (unsigned long)calls.count);

    // Create a call controller to end calls
    CXCallController *callController = [[CXCallController alloc] init];

    // Create a transaction to end all calls
    CXTransaction *transaction = [[CXTransaction alloc] init];

    for (CXCall *call in calls) {
      NSLog(@"Adding end action for call UUID: %@", call.UUID);
      CXEndCallAction *endCallAction = [[CXEndCallAction alloc] initWithCallUUID:call.UUID];
      [transaction addAction:endCallAction];
    }

    // Execute the transaction
    [callController requestTransaction:transaction completion:^(NSError * _Nullable error) {
      if (error) {
        NSLog(@"ERROR: Failed to force end all CallKit calls: %@", error);
        reject(@"force_end_call_error", @"Failed to force end all CallKit calls", error);
      } else {
        NSLog(@"Successfully force ended all CallKit calls");
        resolve(@YES);
      }
    }];
  });
}

// Direct method to end all calls - this is a simpler approach that should work
RCT_EXPORT_METHOD(directEndAllCalls:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  dispatch_async(dispatch_get_main_queue(), ^{
    // Disable Zoom SDK CallKit integration
    [[NSUserDefaults standardUserDefaults] setBool:NO forKey:@"ZoomVideoSDKCallKitEnabled"];
    [[NSUserDefaults standardUserDefaults] synchronize];

    // Get all active calls from CallKit
    CXCallObserver *callObserver = [[CXCallObserver alloc] init];
    NSArray<CXCall *> *calls = callObserver.calls;

    if (calls.count == 0) {
      // No active calls
      NSLog(@"No active calls to end with directEndAllCalls");
      resolve(@YES);
      return;
    }

    NSLog(@"Directly ending %lu calls", (unsigned long)calls.count);

    // Create a call controller to end calls
    CXCallController *callController = [[CXCallController alloc] init];

    // Create a transaction to end all calls
    CXTransaction *transaction = [[CXTransaction alloc] init];

    for (CXCall *call in calls) {
      NSLog(@"Adding end action for call UUID: %@", call.UUID);
      CXEndCallAction *endCallAction = [[CXEndCallAction alloc] initWithCallUUID:call.UUID];
      [transaction addAction:endCallAction];
    }

    // Execute the transaction
    [callController requestTransaction:transaction completion:^(NSError * _Nullable error) {
      if (error) {
        NSLog(@"ERROR: Failed to end calls with directEndAllCalls: %@", error);

        // Try a different approach if the first one fails
        AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
        if ([appDelegate respondsToSelector:@selector(endAllActiveCalls)]) {
          NSLog(@"Falling back to AppDelegate's endAllActiveCalls method");
          [appDelegate endAllActiveCalls];
          resolve(@YES);
        } else {
          reject(@"end_call_error", @"Failed to end calls with directEndAllCalls", error);
        }
      } else {
        NSLog(@"Successfully ended all calls with directEndAllCalls");
        resolve(@YES);
      }
    }];
  });
}

// Reset CallKit provider - nuclear option
RCT_EXPORT_METHOD(resetCallKitProvider:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
  dispatch_async(dispatch_get_main_queue(), ^{
    // Disable Zoom SDK CallKit integration
    [[NSUserDefaults standardUserDefaults] setBool:NO forKey:@"ZoomVideoSDKCallKitEnabled"];
    [[NSUserDefaults standardUserDefaults] synchronize];

    // Get AppDelegate
    AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];

    // First try to end all active calls
    if ([appDelegate respondsToSelector:@selector(endAllActiveCalls)]) {
      NSLog(@"Using AppDelegate's endAllActiveCalls method");
      [appDelegate endAllActiveCalls];
    }

    // Then reset the provider if available
    if (appDelegate.callProvider) {
      NSLog(@"Resetting CallKit provider");

      // Get the current configuration
      CXProviderConfiguration *oldConfig = appDelegate.callProvider.configuration;

      // Invalidate the current provider
      [appDelegate.callProvider invalidate];

      // Create a new provider with the same configuration
      appDelegate.callProvider = [[CXProvider alloc] initWithConfiguration:oldConfig];
      [appDelegate.callProvider setDelegate:appDelegate queue:nil];

      NSLog(@"CallKit provider has been reset");
      resolve(@YES);
    } else {
      NSLog(@"CallKit provider not available");
      resolve(@NO);
    }
  });
}

@end
