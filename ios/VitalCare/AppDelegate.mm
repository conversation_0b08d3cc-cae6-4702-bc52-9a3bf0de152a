#import "AppDelegate.h"
#import <Firebase/Firebase.h>
#import <React/RCTBundleURLProvider.h>
#import <React/RCTLinkingManager.h>
#import <PushKit/PushKit.h>
#import "RNVoipPushNotificationManager.h"
#import <CallKit/CallKit.h>
#import <UIKit/UIKit.h>

@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions
{
  // @generated begin @react-native-firebase/app-didFinishLaunchingWithOptions
  [FIRApp configure];
  // @generated end @react-native-firebase/app-didFinishLaunchingWithOptions

  self.moduleName = @"main";
  self.initialProps = @{};

  // Setup PushKit for VoIP
  self.voipRegistry = [[PKPushRegistry alloc] initWithQueue:dispatch_get_main_queue()];
  self.voipRegistry.delegate = self;
  self.voipRegistry.desiredPushTypes = [NSSet setWithObject:PKPushTypeVoIP];

  // Setup CallKit provider
  CXProviderConfiguration *configuration = [[CXProviderConfiguration alloc] initWithLocalizedName:@"VitalCare"];
  configuration.includesCallsInRecents = NO;
  configuration.supportsVideo = YES;
  configuration.maximumCallsPerCallGroup = 1;
  configuration.supportedHandleTypes = [NSSet setWithObject:@(CXHandleTypePhoneNumber)];

  self.callProvider = [[CXProvider alloc] initWithConfiguration:configuration];
  [self.callProvider setDelegate:self queue:nil];

  NSLog(@"App launched with options: %@", launchOptions);

  // Register for application lifecycle notifications
  [[NSNotificationCenter defaultCenter] addObserver:self
                                           selector:@selector(applicationDidBecomeActive:)
                                               name:UIApplicationDidBecomeActiveNotification
                                             object:nil];

  return [super application:application didFinishLaunchingWithOptions:launchOptions];
}

#pragma mark - PushKit Delegate

// VoIP token registration
- (void)pushRegistry:(PKPushRegistry *)registry didUpdatePushCredentials:(PKPushCredentials *)credentials forType:(PKPushType)type {
  // Forward to RNVoipPushNotificationManager
  [RNVoipPushNotificationManager didUpdatePushCredentials:credentials forType:(NSString *)type];

  // Log token for debugging
  NSString *token = [self stringWithDeviceToken:credentials.token];
  NSLog(@"VoIP Token: %@", token);
}

// Helper method to convert token data to string
- (NSString *)stringWithDeviceToken:(NSData *)deviceToken {
  const uint8_t *tokenBuffer = (const uint8_t *)[deviceToken bytes];
  NSMutableString *hexString = [NSMutableString string];
  for (NSUInteger i = 0; i < deviceToken.length; i++) {
    [hexString appendFormat:@"%02x", tokenBuffer[i]];
  }
  return [hexString copy];
}

// VoIP push received - show CallKit UI
- (void)pushRegistry:(PKPushRegistry *)registry didReceiveIncomingPushWithPayload:(PKPushPayload *)payload forType:(PKPushType)type withCompletionHandler:(void (^)(void))completion {
  // Process with RNVoipPushNotificationManager (required for token handling)
  [RNVoipPushNotificationManager didReceiveIncomingPushWithPayload:payload forType:(NSString *)type];

  // Log the VoIP push receipt for debugging
  NSLog(@"Received VoIP push: %@", payload.dictionaryPayload);

  // Extract caller info from payload
  NSDictionary *payloadDict = payload.dictionaryPayload;
  NSString *callerName = payloadDict[@"caller"] ?: @"Unknown Caller";

  // Report incoming call to CallKit
  [self reportIncomingCallWithUUID:[NSUUID UUID] handle:@"Unknown" callerName:callerName];

  // Call the completion handler as required by iOS
  if (completion) {
    completion();
  }
}

- (void)reportIncomingCallWithUUID:(NSUUID *)uuid handle:(NSString *)handle callerName:(NSString *)callerName {
  CXCallUpdate *update = [[CXCallUpdate alloc] init];
  update.remoteHandle = [[CXHandle alloc] initWithType:CXHandleTypeGeneric value:handle];
  update.localizedCallerName = callerName;
  update.hasVideo = YES;

  [self.callProvider reportNewIncomingCallWithUUID:uuid update:update completion:^(NSError * _Nullable error) {
    if (error) {
      NSLog(@"Error reporting incoming call: %@", error.localizedDescription);
    } else {
      NSLog(@"Incoming call reported successfully!");
    }
  }];
}

#pragma mark - CallKit Provider Delegate

- (void)providerDidReset:(CXProvider *)provider {
  NSLog(@"CallKit provider did reset");
}

- (void)provider:(CXProvider *)provider performAnswerCallAction:(CXAnswerCallAction *)action {
  NSLog(@"CallKit call answered: %@", action.callUUID);

  // Fulfill the action first
  [action fulfill];

  // Store the call UUID for later reference
  NSString *callUUIDString = action.callUUID.UUIDString;
  [[NSUserDefaults standardUserDefaults] setObject:callUUIDString forKey:@"LastAnsweredCallUUID"];

  // Check if the app is already active
  UIApplicationState appState = [UIApplication sharedApplication].applicationState;
  if (appState != UIApplicationStateActive) {
    // Only set the flag to end calls if the app is not already active
    NSLog(@"App is not active, setting ShouldEndCallsOnActive flag");
    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"ShouldEndCallsOnActive"];

    // Mark the call as active in CallKit
    CXCallUpdate *update = [[CXCallUpdate alloc] init];
    [self.callProvider reportCallWithUUID:action.callUUID updated:update];

    // Open the app and present view controller only if app is not active
    dispatch_async(dispatch_get_main_queue(), ^{
      UIViewController *rootVC = [[[UIApplication sharedApplication] windows] firstObject].rootViewController;
      if (rootVC) {
        // Check for authentication token in secure storage
        [self openAppWithAuthentication];
      }
    });
  } else {
    NSLog(@"App is already active, just ending the call without redirecting");
    // If app is already active, just end the call immediately
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self endAllActiveCalls];
    });
  }

  [[NSUserDefaults standardUserDefaults] synchronize];
  NSLog(@"Stored answered call UUID: %@", callUUIDString);
}

// New method to handle authenticated URL opening
- (void)openAppWithAuthentication {
  // Log the attempt to open with authentication
  NSLog(@"Attempting to open app with authentication check");

  // Create a URL with authentication flag
  NSURL *appURL = [NSURL URLWithString:@"com.austinr47.VITALCARE://provider/dashboard?requireAuth=true"];

  if ([[UIApplication sharedApplication] canOpenURL:appURL]) {
    [[UIApplication sharedApplication] openURL:appURL options:@{} completionHandler:^(BOOL success) {
      if (success) {
        NSLog(@"Successfully opened app URL with authentication flag");
      } else {
        NSLog(@"Failed to open app URL with authentication flag");
      }
    }];
  } else {
    NSLog(@"Cannot open app URL: %@", appURL);
  }
}

- (void)provider:(CXProvider *)provider performEndCallAction:(CXEndCallAction *)action {
  NSLog(@"CallKit call ended: %@", action.callUUID);
  [action fulfill];
}

- (void)endAllActiveCalls {
  // Get all active calls from CallKit
  CXCallObserver *callObserver = [[CXCallObserver alloc] init];
  NSArray<CXCall *> *calls = callObserver.calls;

  if (calls.count == 0) {
    NSLog(@"No active calls to end");
    return;
  }

  NSLog(@"Ending %lu active calls from AppDelegate", (unsigned long)calls.count);

  // APPROACH 1: Use CXCallController (standard approach)
  CXCallController *callController = [[CXCallController alloc] init];
  CXTransaction *transaction = [[CXTransaction alloc] init];

  for (CXCall *call in calls) {
    NSLog(@"Adding end action for call UUID: %@", call.UUID);
    CXEndCallAction *endCallAction = [[CXEndCallAction alloc] initWithCallUUID:call.UUID];
    [transaction addAction:endCallAction];
  }

  [callController requestTransaction:transaction completion:^(NSError * _Nullable error) {
    if (error) {
      NSLog(@"ERROR: Failed to end calls with CXCallController: %@", error);

      // If the transaction fails, try approach 2 as fallback
      for (CXCall *call in calls) {
        NSLog(@"Fallback: Directly ending call with UUID: %@", call.UUID);
        if (self.callProvider) {
          [self.callProvider reportCallWithUUID:call.UUID endedAtDate:[NSDate date] reason:CXCallEndedReasonRemoteEnded];
        }
      }
    } else {
      NSLog(@"Successfully ended calls with CXCallController");
    }
  }];

  // APPROACH 2: Use CXProvider directly (more direct approach)
  // This is a redundant approach that ensures calls are ended even if approach 1 fails
  for (CXCall *call in calls) {
    NSLog(@"Directly ending call with UUID: %@", call.UUID);
    CXEndCallAction *endCallAction = [[CXEndCallAction alloc] initWithCallUUID:call.UUID];
    [endCallAction fulfill];

    // Also try to report call as ended to the provider
    if (self.callProvider) {
      NSLog(@"Reporting call as ended to provider: %@", call.UUID);
      [self.callProvider reportCallWithUUID:call.UUID endedAtDate:[NSDate date] reason:CXCallEndedReasonRemoteEnded];
    }
  }

  // APPROACH 3: Reset the provider as a last resort
  // This is a nuclear option that should only be used if all else fails
  dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    // Check if there are still active calls after a short delay
    NSArray<CXCall *> *remainingCalls = callObserver.calls;
    if (remainingCalls.count > 0) {
      NSLog(@"Still have %lu active calls after direct ending, resetting provider", (unsigned long)remainingCalls.count);

      if (self.callProvider) {
        NSLog(@"Invalidating and recreating the call provider");
        CXProviderConfiguration *oldConfig = self.callProvider.configuration;
        [self.callProvider invalidate];

        // Create a new provider with the same configuration
        self.callProvider = [[CXProvider alloc] initWithConfiguration:oldConfig];
        [self.callProvider setDelegate:self queue:nil];
      }
    }
  });
}

// These methods are required by the CXProviderDelegate protocol
- (void)provider:(CXProvider *)provider didActivateAudioSession:(AVAudioSession *)audioSession {
  NSLog(@"CallKit audio session activated");
}

- (void)provider:(CXProvider *)provider didDeactivateAudioSession:(AVAudioSession *)audioSession {
  NSLog(@"CallKit audio session deactivated");
}

#pragma mark - React Native Methods

- (NSURL *)sourceURLForBridge:(RCTBridge *)bridge {
  return [self bundleURL];
}

- (NSURL *)bundleURL {
#if DEBUG
  return [[RCTBundleURLProvider sharedSettings] jsBundleURLForBundleRoot:@".expo/.virtual-metro-entry"];
#else
  return [[NSBundle mainBundle] URLForResource:@"main" withExtension:@"jsbundle"];
#endif
}

- (BOOL)application:(UIApplication *)application openURL:(NSURL *)url options:(NSDictionary<UIApplicationOpenURLOptionsKey,id> *)options {
  return [super application:application openURL:url options:options] || [RCTLinkingManager application:application openURL:url options:options];
}

- (BOOL)application:(UIApplication *)application continueUserActivity:(NSUserActivity *)userActivity restorationHandler:(nonnull void (^)(NSArray<id<UIUserActivityRestoring>> * _Nullable))restorationHandler {
  return [super application:application continueUserActivity:userActivity restorationHandler:restorationHandler] || [RCTLinkingManager application:application continueUserActivity:userActivity restorationHandler:restorationHandler];
}

#pragma mark - Application Lifecycle

// This method is called when the app becomes active (in foreground)
- (void)applicationDidBecomeActive:(NSNotification *)notification {
  NSLog(@"Application did become active - checking if we should end calls");

  // Check if we should end calls (set when a call was answered)
  BOOL shouldEndCalls = [[NSUserDefaults standardUserDefaults] boolForKey:@"ShouldEndCallsOnActive"];

  if (shouldEndCalls) {
    NSLog(@"ShouldEndCallsOnActive flag is set - will end calls after delay");

    // Wait a short delay to ensure the app is fully active
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      // End all active calls
      [self endAllActiveCalls];

      // Reset the flag
      [[NSUserDefaults standardUserDefaults] setBool:NO forKey:@"ShouldEndCallsOnActive"];
      [[NSUserDefaults standardUserDefaults] synchronize];
      NSLog(@"Reset ShouldEndCallsOnActive flag");
    });
  } else {
    NSLog(@"ShouldEndCallsOnActive flag not set - not ending calls");
  }
}

// Clean up when app is terminated
- (void)dealloc {
  [[NSNotificationCenter defaultCenter] removeObserver:self];
}

@end