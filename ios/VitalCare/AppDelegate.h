#import <RCTAppDelegate.h>
#import <UIKit/UIKit.h>
#import <Expo/Expo.h>
#import <PushKit/PushKit.h>
#import <CallKit/CallKit.h>

@interface AppDelegate : EXAppDelegateWrapper <PKPushRegistryDelegate, CXProviderDelegate>

// VoIP properties
@property (nonatomic, strong) PKPushRegistry *voipRegistry;
@property (nonatomic, strong) CXProvider *callProvider;

// Method to report incoming call to CallKit
- (void)reportIncomingCallWithUUID:(NSUUID *)uuid handle:(NSString *)handle callerName:(NSString *)callerName;

// Method to end all active calls
- (void)endAllActiveCalls;

@end
