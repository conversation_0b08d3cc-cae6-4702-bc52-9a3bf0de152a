import { createThemes, defaultComponentThemes } from "@tamagui/theme-builder";

const darkPalette = [
  "hsla(0, 15%, 1%, 1)",
  "hsla(0, 15%, 6%, 1)",
  "hsla(0, 15%, 12%, 1)",
  "hsla(0, 15%, 17%, 1)",
  "hsla(0, 15%, 23%, 1)",
  "hsla(0, 15%, 28%, 1)",
  "hsla(0, 15%, 34%, 1)",
  "hsla(0, 15%, 39%, 1)",
  "hsla(0, 15%, 45%, 1)",
  "hsla(0, 15%, 50%, 1)",
  "hsla(0, 15%, 93%, 1)",
  "hsla(0, 15%, 99%, 1)",
];
const lightPalette = [
  "hsla(0, 15%, 99%, 1)",
  "hsla(0, 15%, 94%, 1)",
  "hsla(0, 15%, 88%, 1)",
  "hsla(0, 15%, 83%, 1)",
  "hsla(0, 15%, 77%, 1)",
  "hsla(0, 15%, 72%, 1)",
  "hsla(0, 15%, 66%, 1)",
  "hsla(0, 15%, 61%, 1)",
  "hsla(0, 15%, 55%, 1)",
  "hsla(0, 15%, 50%, 1)",
  "hsla(0, 15%, 15%, 1)",
  "hsla(0, 15%, 1%, 1)",
];

const builtThemes = createThemes({
  componentThemes: defaultComponentThemes,

  base: {
    palette: {
      dark: darkPalette,
      light: lightPalette,
    },

    extra: {
      light: {
        primaryColor: "rgba(21, 112, 239, 1)",
        loginForgotPasswordColor: "rgba(21, 112, 239, 1)",
        screenBackgroundcolor: "rgba(255, 255, 255, 1)",
        primaryBorderColor: "rgba(208, 213, 221, 1)",
        textcolor: "rgba(18, 25, 38, 1)",
        placeholdertextColor: "rgba(102, 112, 133, 1)",
        blueShadowColor: "rgba(178, 221, 255, 1)",
        confirmOrderBlue: "rgba(239, 248, 255, 1)",
        confirmOrderTextColor: "rgba(23, 92, 211, 1)",
        confirmOrderBorderCOlor: "rgba(178, 221, 255, 1)",
        buttonWhiteColor: "rgba(255, 255, 255, 1)",
        nurseBadgeBackgroundColor: "rgba(248, 249, 252, 1)",
        callAlertBackground: "rgba(0, 0, 0, 0.3)",
        switchToggleGreen: "rgba(23, 178, 106, 1)",
        switchToggleGray: "rgba(242, 244, 247, 1)",
        activeTabColor: "rgba(249, 250, 251, 1)",
        inactiveToggleColor: "rgba(242, 244, 247, 1)",
        verifyPinContainerColor: "rgba(249, 250, 251, 1)",
        chatOutgoingMessageColor: "rgba(239, 248, 255, 1)",
        chatIncomingMessageColor: "rgba(248, 250, 252, 1)",
        chatBorderColor: "rgba(210, 214, 219, 1)",
        orderConfirmedBackground: "rgba(228, 228, 237, 1)",
        orderConfirmedTextColor: "rgba(195, 195, 209, 1)",
        removeTextBlue: "rgba(0, 0, 255, 1)",
        physicalExamItemColor: "rgba(243, 244, 246, 1)",
        physicalExamPlusBackground: "rgba(46, 144, 250, 1)",
        physicalExamMinusBackground: "rgba(249, 112, 102, 1)",
        soapNotesSubtitleText: "rgba(71, 84, 103, 1)",
        soapNotesBodyText: "rgba(102, 112, 133, 1)",
        selectCardGrayBackground: "rgba(247, 248, 251, 1)",
        selectCardBorderColor: "rgba(212, 216, 234, 1)",
        disableButtonPrimaryColor: "rgba(21, 112, 239, 0.1)",
        emptyTextColor:"rgba(136, 136, 136, 1)",
        patientDropDownBackgroundColor: "rgba(255, 255, 255, 1)",        
        disbaledSelectedButtonBorderColor: "rgba(181, 181, 181, 0.29)",
        disbaledSelectedButtonBackgroundColor: "rgba(199, 199, 199, 0.29)",
        disabledSelectedTextColor: "rgba(173, 173, 173, 1)",
        
      },
      dark: {
        primaryColor: "rgba(21, 112, 239, 1)",
        loginForgotPasswordColor: "rgba(21, 112, 239, 1)",
        screenBackgroundcolor: "rgba(13, 18, 28, 1)",
        primaryBorderColor: "rgba(105, 117, 134, 1)",
        textcolor: "rgba(255, 255, 255, 1)",
        placeholdertextColor: "rgba(255, 255, 255, 1)",
        blueShadowColor: "rgba(178, 221, 255, 1)",
        confirmOrderBlue: "rgba(239, 248, 255, 1)",
        confirmOrderTextColor: "rgba(23, 92, 211, 1)",
        confirmOrderBorderCOlor: "rgba(178, 221, 255, 1)",
        buttonWhiteColor: "rgba(255, 255, 255, 1)",
        nurseBadgeBackgroundColor: "rgba(248, 249, 252, 1)",
        callAlertBackground: "rgba(0, 0, 0, 0.3)",
        switchToggleGreen: "rgba(23, 178, 106, 1)",
        switchToggleGray: "rgba(242, 244, 247, 1)",
        activeTabColor: "rgba(249, 250, 251, 1)",
        inactiveToggleColor: "rgba(105, 117, 134, 1)",
        verifyPinContainerColor: "rgba(249, 250, 251, 1)",
        chatOutgoingMessageColor: "rgba(239, 248, 255, 1)",
        chatIncomingMessageColor: "rgba(248, 250, 252, 1)",
        chatBorderColor: "rgba(210, 214, 219, 1)",
        orderConfirmedBackground: "rgba(228, 228, 237, 1)",
        orderConfirmedTextColor: "rgba(195, 195, 209, 1)",
        removeTextBlue: "rgba(0, 0, 255, 1)",
        physicalExamItemColor: "rgba(105, 117, 134, 1)",
        physicalExamPlusBackground: "rgba(46, 144, 250, 1)",
        physicalExamMinusBackground: "rgba(249, 112, 102, 1)",
        soapNotesSubtitleText: "rgba(71, 84, 103, 1)",
        soapNotesBodyText: "rgba(102, 112, 133, 1)",
        selectCardGrayBackground: "rgba(247, 248, 251, 1)",
        selectCardBorderColor: "rgba(212, 216, 234, 1)",
        disableButtonPrimaryColor: "rgba(21, 112, 239, 0.1)",
        emptyTextColor:"rgba(136, 136, 136, 1)",
        patientDropDownBackgroundColor: "rgba(30, 30, 30, 1)",
        disbaledSelectedButtonBorderColor: "rgba(181, 181, 181, 0.29)",
        disbaledSelectedButtonBackgroundColor: "rgba(199, 199, 199, 0.29)",
        disabledSelectedTextColor: "rgba(173, 173, 173, 1)",
      },
    },
  },
});

export type Themes = typeof builtThemes;

export const themes: Themes =
  process.env.TAMAGUI_ENVIRONMENT === "client" &&
  process.env.NODE_ENV === "production"
    ? (builtThemes as any)
    : (builtThemes as any);
