#!/usr/bin/env ruby

require 'xcodeproj'

# Path to your .xcodeproj file
project_path = 'ios/VitalCare.xcodeproj'

# Open the project
project = Xcodeproj::Project.open(project_path)

# Find the main target
target = project.targets.find { |t| t.name == 'VitalCare' }

if target.nil?
  puts "Target 'VitalCare' not found"
  exit 1
end

# Add CallKit framework
frameworks_build_phase = target.frameworks_build_phase
callkit_framework_path = 'System/Library/Frameworks/CallKit.framework'

# Check if CallKit is already added
callkit_ref = frameworks_build_phase.files.find { |f| f.display_name == 'CallKit.framework' }

if callkit_ref.nil?
  # Add CallKit framework
  framework_ref = project.frameworks_group.new_file(callkit_framework_path)
  frameworks_build_phase.add_file_reference(framework_ref)
  puts "Added CallKit.framework to the project"
else
  puts "CallKit.framework is already in the project"
end

# Save the project
project.save

puts "Project updated successfully"
