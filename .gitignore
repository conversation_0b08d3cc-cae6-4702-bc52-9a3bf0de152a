# Node modules
/node_modules/

# React Native packager
*.log
*.metro
*.watchman

# Android/gradle build files
/android/.gradle/
/android/app/build/
/android/build/
/android/.idea/

# iOS build files
/ios/Pods/
/ios/build/
/ios/.idea/

# Xcode workspace (if using Xcode for iOS development)
*.xcworkspace
*.xcuserstate

# Temporary files
*.swp
*.swo

# macOS system files
.DS_Store

# Windows system files
Thumbs.db

# Npm debug log files
npm-debug.log

# .env files (if you use them to store sensitive data like API keys)
.env

# Metro bundler cache
/.metro-*

# React Native packager cache
/react-native-packager-cache/

# Yarn integrity file
.yarn-integrity

# JetBrains IDE files
.idea/

# VSCode settings
.vscode/

# Platform-specific files
/android/app/debug.keystore

# Gemfile.lock (if not using Ruby or CocoaPods)
Gemfile.lock

# Other generated files
*.iml

# Lock files (only if using yarn)
yarn.lock

# Ignore Bundler's default directory
/vendor/

# Ignore the gemspec files in the vendor/bundle directory
/vendor/bundle/

# @generated expo-cli sync-2b81b286409207a5da26e14c78851eb30d8ccbdb
# The following patterns were generated by expo-cli

expo-env.d.ts
# @end expo-cli

.env